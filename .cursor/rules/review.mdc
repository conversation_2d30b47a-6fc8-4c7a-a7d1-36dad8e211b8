---
description: 
globs: 
alwaysApply: false
---
# Cursor Review Rule: PR-Ready Checklist for Updated Files

**Only review the files that have been modified in the current branch compared to the `main` branch, give me potential fixes.**

Focus on critical issues that could affect correctness, performance, or maintainability. Skip stylistic suggestions unless they break conventions.

## ✅ Review Checklist (Updated Files Only)

- Logic correctness and edge case coverage  
- Improper or mixed usage of `async`, `await`, `.then()`  
- Missing or incorrect `try/catch` in async flows  
- Unnecessary `async/await` usage  
- Direct or unintended state mutations  
- Immutable state handling  
- Missing dependencies in React hooks (`useEffect`, `useCallback`, etc.)  
- Custom hooks with incomplete dependency arrays  
- Incorrect hook call order or conditional hooks  
- Missing imports (e.g., `dayjs` plugins)  
- Unused or missing modules  
- File and component placement in folder structure  
- Naming consistency and descriptiveness  
- Performance concerns (unnecessary re-renders, missing `useMemo` / `useCallback`)  
- Missing error handling or fallback logic  
- Side effects outside of lifecycle-aware hooks  
- Inconsistent or redundant logic across files  
- Non-atomic commits or mixed concerns in PR  
- Lack of test coverage for critical logic paths
