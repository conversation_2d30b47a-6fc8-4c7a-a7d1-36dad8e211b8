import * as admin from 'firebase-admin'
import data from './WeeklySchedule.json'
import moment from 'moment'

const typedData = data as {
  [date: string]: any
}

// const serviceAccount = require('../pivot-not-production.json')
const serviceAccount = require('../../pivot-inc.json')

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  databaseURL: 'https://pivot-inc.firebaseio.com'
  // databaseURL: 'https://pivot-not-production-project.firebaseio.com'
})

// command to extract the data from the backup
// jq '.WeeklySchedule."-ORRAh7kpfnIvqiY2kge"' backup.json > WeeklySchedule.json

const companyId = '-ORRAh7kpfnIvqiY2kge'
const from = '2025-05-29'
const to = '2025-06-05'

const key = 'WeeklySchedule'
const dates: string[] = []
const startDate = moment(from, 'YYYY-MM-DD')

const updates: {
  [key: string]: null
} = {}

while (startDate.format('YYYY-MM-DD') <= to) {
  dates.push(startDate.format('YYYY-MM-DD'))
  startDate.add(1, 'days')
}

console.log(dates)

dates.forEach(date => {
  // const schedule = data[companyId]?.[date]
  const schedule = typedData?.[date]

  console.log(date, typedData)

  if (schedule) {
    // updates[date] = schedule

    updates[`${key}/${companyId}/${date}`] = schedule
    updates[`ScheduleDrafts/${companyId}/${date}`] = schedule
  }
})
// admin
//   .database()
//   .ref()
//   .update(updates)
//   .then(() => {
console.log('done!', updates, Object.keys(updates).length)
// })
