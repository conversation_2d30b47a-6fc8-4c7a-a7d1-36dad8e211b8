import * as admin from 'firebase-admin'

const serviceAccount = require('../../pivot-not-production.json')
// const serviceAccount = require('../pivot-inc.json')

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  // databaseURL: 'https://pivot-inc.firebaseio.com'
  databaseURL: 'https://pivot-not-production-project.firebaseio.com'
})

const timezones = [
  // Canadian cities
  // Eastern Time Zone
  {
    name: 'Toronto',
    lat: 43.6532,
    lng: -79.3832,
    timezone: 'America/Toronto',
    timezoneCityId: 'America/Toronto|Toronto'
  },
  {
    name: 'Ottawa',
    lat: 45.4215,
    lng: -75.6972,
    timezone: 'America/Toronto',
    timezoneCityId: 'America/Toronto|Ottawa'
  },
  {
    name: 'Montreal',
    lat: 45.5017,
    lng: -73.5673,
    timezone: 'America/Toronto',
    timezoneCityId: 'America/Toronto|Montreal'
  },
  {
    name: 'Quebec City',
    lat: 46.8139,
    lng: -71.208,
    timezone: 'America/Toronto',
    timezoneCityId: 'America/Toronto|Quebec City'
  },
  {
    name: 'Windsor',
    lat: 42.3149,
    lng: -83.0364,
    timezone: 'America/Toronto',
    timezoneCityId: 'America/Toronto|Windsor'
  },

  // Atlantic Time Zone
  {
    name: 'Halifax',
    lat: 44.6488,
    lng: -63.5752,
    timezone: 'America/Halifax',
    timezoneCityId: 'America/Halifax|Halifax'
  },
  {
    name: 'Moncton',
    lat: 46.0878,
    lng: -64.7782,
    timezone: 'America/Halifax',
    timezoneCityId: 'America/Halifax|Moncton'
  },
  {
    name: 'Charlottetown',
    lat: 46.2382,
    lng: -63.1311,
    timezone: 'America/Halifax',
    timezoneCityId: 'America/Halifax|Charlottetown'
  },
  {
    name: 'Fredericton',
    lat: 45.9636,
    lng: -66.6431,
    timezone: 'America/Halifax',
    timezoneCityId: 'America/Halifax|Fredericton'
  },

  // Newfoundland Time Zone
  {
    name: "St. John's",
    lat: 47.5615,
    lng: -52.7126,
    timezone: 'America/St_Johns',
    timezoneCityId: "America/St_Johns|St. John's"
  },
  {
    name: 'Corner Brook',
    lat: 48.9566,
    lng: -57.9508,
    timezone: 'America/St_Johns',
    timezoneCityId: 'America/St_Johns|Corner Brook'
  },
  {
    name: 'Gander',
    lat: 48.9566,
    lng: -54.6088,
    timezone: 'America/St_Johns',
    timezoneCityId: 'America/St_Johns|Gander'
  },

  // Central Time Zone
  {
    name: 'Winnipeg',
    lat: 49.8951,
    lng: -97.1384,
    timezone: 'America/Winnipeg',
    timezoneCityId: 'America/Winnipeg|Winnipeg'
  },
  {
    name: 'Regina',
    lat: 50.4452,
    lng: -104.6189,
    timezone: 'America/Winnipeg',
    timezoneCityId: 'America/Winnipeg|Regina'
  },
  {
    name: 'Saskatoon',
    lat: 52.1579,
    lng: -106.6702,
    timezone: 'America/Winnipeg',
    timezoneCityId: 'America/Winnipeg|Saskatoon'
  },
  {
    name: 'Thunder Bay',
    lat: 48.3809,
    lng: -89.2477,
    timezone: 'America/Winnipeg',
    timezoneCityId: 'America/Winnipeg|Thunder Bay'
  },

  // Mountain Time Zone
  {
    name: 'Calgary',
    lat: 51.0447,
    lng: -114.0719,
    timezone: 'America/Edmonton',
    timezoneCityId: 'America/Edmonton|Calgary'
  },
  {
    name: 'Edmonton',
    lat: 53.5461,
    lng: -113.4938,
    timezone: 'America/Edmonton',
    timezoneCityId: 'America/Edmonton|Edmonton'
  },
  {
    name: 'Banff',
    lat: 51.1784,
    lng: -115.5708,
    timezone: 'America/Edmonton',
    timezoneCityId: 'America/Edmonton|Banff'
  },
  {
    name: 'Jasper',
    lat: 52.8737,
    lng: -118.0814,
    timezone: 'America/Edmonton',
    timezoneCityId: 'America/Edmonton|Jasper'
  },

  // Pacific Time Zone
  {
    name: 'Vancouver',
    lat: 49.2827,
    lng: -123.1207,
    timezone: 'America/Vancouver',
    timezoneCityId: 'America/Vancouver|Vancouver'
  },
  {
    name: 'Victoria',
    lat: 48.4284,
    lng: -123.3656,
    timezone: 'America/Vancouver',
    timezoneCityId: 'America/Vancouver|Victoria'
  },
  {
    name: 'Kelowna',
    lat: 49.888,
    lng: -119.496,
    timezone: 'America/Vancouver',
    timezoneCityId: 'America/Vancouver|Kelowna'
  },
  {
    name: 'Abbotsford',
    lat: 49.0504,
    lng: -122.3045,
    timezone: 'America/Vancouver',
    timezoneCityId: 'America/Vancouver|Abbotsford'
  },
  {
    name: 'Minsk',
    lat: 53.9023,
    lng: 27.5618,
    timezone: 'Europe/Minsk',
    timezoneCityId: 'Europe/Minsk|Minsk'
  },
  {
    name: 'New York',
    lat: 40.7128,
    lng: -74.006,
    timezone: 'America/New_York',
    timezoneCityId: 'America/New_York|New York'
  }
]

const companies: {
  [key: string]: {
    location?: {
      lat: number
      lng: number
    }
  }
} = require('./companies.json')

let counter = 0

const updates: {
  [key: string]: string
} = {}

function findClosestTimezone(lat: number, lng: number) {
  let closestTimezone = timezones[0]
  let shortestDistance = calculateDistance(
    lat,
    lng,
    closestTimezone.lat,
    closestTimezone.lng
  )

  for (let i = 1; i < timezones.length; i++) {
    const distance = calculateDistance(
      lat,
      lng,
      timezones[i].lat,
      timezones[i].lng
    )
    if (distance < shortestDistance) {
      closestTimezone = timezones[i]
      shortestDistance = distance
    }
  }
  return closestTimezone.timezoneCityId
}

function calculateDistance(
  lat1: number,
  lng1: number,
  lat2: number,
  lng2: number
) {
  const R = 6378
  const dLat = ((lat2 - lat1) * Math.PI) / 180
  const dLng = ((lng2 - lng1) * Math.PI) / 180

  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos((lat1 * Math.PI) / 180) *
      Math.cos((lat2 * Math.PI) / 180) *
      Math.sin(dLng / 2) *
      Math.sin(dLng / 2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
  return R * c
}

const init = async () => {
  Object.entries(companies).forEach(([companyId, company]) => {
    const { location } = company
    if (location) {
      const timezoneCityId = findClosestTimezone(location.lat, location.lng)
      updates[`Companies/${companyId}/timezoneCityId`] = timezoneCityId
      counter++
    }
  })

  await admin.database().ref().update(updates)

  console.log(updates)
  console.log('Companies updated', counter)
}

init()
