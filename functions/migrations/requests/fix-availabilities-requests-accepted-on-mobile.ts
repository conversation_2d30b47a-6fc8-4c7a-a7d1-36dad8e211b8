// take all availabilities notifications after 13/06/2025
// and till 08/07/2025 7pm
// and not byEmployer
// if not notification.processed
// means accepted from mobile
// check how many requests affected
// then we will decide what to do about it
import admin from 'firebase-admin'
import dayjs from 'dayjs'
// import notifications from './notifications.json'
import fs from 'fs'
import path from 'path'

// const serviceAccount = require('../../pivot-not-production.json')
const serviceAccount = require('../../pivot-inc.json')

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  databaseURL: 'https://pivot-inc.firebaseio.com'
  // databaseURL: 'https://pivot-not-production-project.firebaseio.com'
})

type NotificationsJson = {
  [companyId: string]: {
    [notificationId: string]: Notification
  }
}

const notifications = JSON.parse(
  fs.readFileSync(path.join(__dirname, 'notifications.json'), 'utf8')
)

const typedNotifications = notifications as unknown as {
  [companyId: string]: {
    [notificationId: string]: {
      processed: boolean
      statusEmployer: string
      status: string
      type: string
      updatedAt: number
      byEmployer: boolean
      effectiveDate: string
      supervisorResponseTimestamp: number
      employeeId: string
      newAvailabilities: {
        maxHoursInWeek: number
        maxDaysPerWeek: number
      }
      createdAt: number
      time: number
    }
  }
}

let affectedRequests = 0
let affectedPastRequests = 0
let notTheLatestRequests = 0
let theLatestRequests = 0

const from = dayjs('2025-06-13 00:00')
const to = dayjs('2025-07-08 19:00')

const updates: {
  [key: string]: any
} = {}

for (const companyId in typedNotifications) {
  for (const notificationId in typedNotifications[companyId]) {
    const notification = typedNotifications[companyId][notificationId]
    if (
      notification.type === 'availabilities' &&
      !notification.processed &&
      !notification.byEmployer &&
      dayjs(
        notification.supervisorResponseTimestamp || notification.updatedAt
      ).isAfter(from) &&
      dayjs(
        notification.supervisorResponseTimestamp || notification.updatedAt
      ).isBefore(to)
    ) {
      const isFutureRequest = dayjs(notification.effectiveDate).isAfter(to)
      const isPastRequest = dayjs(notification.effectiveDate).isBefore(to)

      if (isFutureRequest) {
        updates[
          'PendingAvailabilities/' +
            companyId +
            '/' +
            notification.effectiveDate +
            '/' +
            notification.employeeId
        ] = notification.newAvailabilities || {
          maxHoursInWeek: 0,
          maxDaysPerWeek: 0
        }
        affectedRequests++
      } else {
        affectedPastRequests++
        const notTheLatestRequest = Object.entries(
          typedNotifications[companyId]
        ).find(([key, notification2]) => {
          return (
            notification2.type === 'availabilities' &&
            notification2.employeeId === notification.employeeId &&
            key !== notificationId &&
            ((!notification2.byEmployer &&
              dayjs(notification2.effectiveDate).isAfter(
                notification.effectiveDate
              ) &&
              dayjs(notification2.effectiveDate).isBefore(to)) ||
              (notification2.byEmployer &&
                dayjs(notification2.createdAt || notification2.time).isAfter(
                  notification.effectiveDate
                )))
          )
        })

        // notTheLatestRequest &&
        // console.log(notTheLatestRequest, notification.effectiveDate)

        if (notTheLatestRequest) {
          notTheLatestRequests++
        } else {
          theLatestRequests++
        }

        if (!notTheLatestRequest) {
          updates[`Employees/${notification.employeeId}/availabilities`] =
            notification.newAvailabilities || {
              maxHoursInWeek: 0,
              maxDaysPerWeek: 0
            }
        }
      }
    }
  }
}

console.log('affectedRequests', affectedRequests)
console.log('affectedPastRequests', affectedPastRequests)
console.log('notTheLatestRequests', notTheLatestRequests)
console.log('theLatestRequests', theLatestRequests)

// console.log(updates, Object.keys(updates).length)

// admin
//   .database()
//   .ref()
//   .update(updates)
//   .then(() => {
//     console.log('updates done')
//   })
