import admin from 'firebase-admin'
import { VeloceInvoice } from '../../integrations/veloce/interfaces/veloce-invoice'
import { computeSalesData } from '../../integrations/veloce/utils/compute-sales'
import { fetchInvoices } from '../../integrations/veloce/utils/fetch-invoices'
import { getVeloceAccessToken } from '../../integrations/veloce/utils/get-access-token'
import dayjs from 'dayjs'
import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/utc'

dayjs.extend(utc)
dayjs.extend(timezone)

interface VeloceCompanies {
  [companyId: string]: {
    email: string
    password: string
    token: string
    locationID: string
    dayChangeTime: number
  }
}

interface SalesActual {
  [companyId: string]: {
    [date: string]: {
      morningSales: number
      endOfDaySales: number
    }
  }
}
const veloceCompanies: VeloceCompanies = require('./veloceCompanies.json')
const salesActual: SalesActual = require('./salesActual.json')

// Process a single company's sales data
const processCompanySales = async (
  companyId: string,
  company: VeloceCompanies[string]
) => {
  const { email, password, locationID, dayChangeTime = 18000 } = company

  const startTime = Date.now()
  console.log(`\nProcessing company: ${companyId}`)

  try {
    // Get access token
    const veloceToken = await getVeloceAccessToken(email, password)
    if (!veloceToken) {
      console.error(`Failed to get access token for company ${companyId}`)
      return { companyId, salesUpdates: 0, error: 'Failed to get access token' }
    }

    const dayChangeDateTime = dayjs()
      .startOf('day')
      .add(dayChangeTime, 'second')
    const timeString = dayChangeDateTime.format('HH:mm:ss')
    const hours = dayChangeDateTime.hour()

    // Fetch data in 3-week batches for the last 13 months
    const endDate = dayjs()
    const startDate = dayjs().subtract(13, 'month')

    let totalSalesUpdates = 0
    let currentFrom = startDate
    let batchNumber = 1

    while (currentFrom.isBefore(endDate)) {
      // Calculate 3-week batch period
      const currentTo = currentFrom.add(21, 'day').isAfter(endDate)
        ? endDate
        : currentFrom.add(21, 'day')

      const from = `${currentFrom.format('YYYY-MM-DD')}T${timeString}Z`
      const to = `${currentTo.format('YYYY-MM-DD')}T${timeString}Z`

      try {
        // Fetch invoices for this 3-week period
        const invoices = await fetchInvoices({
          offset: 0,
          token: veloceToken,
          locationID,
          from,
          to,
          delay: 200
        })

        // Group invoices by date
        const salesByDate = invoices.reduce(
          (acc: Record<string, VeloceInvoice[]>, invoice: VeloceInvoice) => {
            if (invoice.isCancelled || invoice.isTraining) return acc

            const invoiceTime = dayjs(invoice.invoiceTime)
            const date =
              invoiceTime.hour() < hours
                ? invoiceTime.subtract(1, 'day').format('YYYY-MM-DD')
                : invoiceTime.format('YYYY-MM-DD')

            if (!acc[date]) {
              acc[date] = []
            }
            acc[date].push(invoice)
            return acc
          },
          {}
        )

        // Process sales data for each date
        const salesUpdates: Record<
          string,
          { morningSales: number; endOfDaySales: number }
        > = {}

        for (const date in salesByDate) {
          if (salesActual[companyId]?.[date]) {
            continue
          }

          const dailyInvoices = salesByDate[date]
          const { morningSales, endOfDaySales } = computeSalesData(
            date,
            dailyInvoices
          )

          salesUpdates[`SalesActual/${companyId}/${date}`] = {
            morningSales,
            endOfDaySales
          }
        }

        const updateCount = Object.keys(salesUpdates).length
        if (updateCount > 0) {
          totalSalesUpdates += updateCount

          await admin.database().ref().update(salesUpdates)
        }
      } catch (error) {
        console.error(`    Error fetching batch ${batchNumber}:`, error)
      }

      // Move to next 3-week period
      currentFrom = currentTo
      batchNumber++
    }
    const duration = Date.now() - startTime
    console.log(`Company ${companyId} processed in ${duration}ms`)

    return { companyId, salesUpdates: totalSalesUpdates }
  } catch (error) {
    console.error(`Error processing company ${companyId}:`, error)
    return {
      companyId,
      salesUpdates: 0,
      error: error instanceof Error ? error.message : String(error)
    }
  }
}

const getVeloceLastYearSales = async () => {
  const results = await Promise.all(
    Object.entries(veloceCompanies).map(([companyId, company]) =>
      processCompanySales(companyId, company)
    )
  )

  results.forEach(({ companyId, salesUpdates, error }) => {
    if (error) {
      console.log(`${companyId}: ERROR - ${error}`)
    } else {
      console.log(`${companyId}: ${salesUpdates} days added`)
    }
  })
}

getVeloceLastYearSales().catch(error => {
  console.error('Fatal error:', error)
  process.exit(1)
})
