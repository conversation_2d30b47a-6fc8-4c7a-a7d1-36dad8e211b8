import * as admin from 'firebase-admin'
import * as functions from 'firebase-functions'

export const checkUserCreatedCompanies = functions.https.onCall(
  async ({ email }) => {
    try {

      let user
      try {
        user = await admin.auth().getUserByEmail(email)
      } catch (error) {
        return { hasCreatedCompanies: false }
      }

      const userId = user.uid

      // Check if user has created companies
      const userCompaniesCreated = await admin
        .database()
        .ref(`Users/${userId}/companiesCreated`)
        .once('value')
        .then(snapshot => snapshot.val())

      return { 
        hasCreatedCompanies: userCompaniesCreated ? true : false 
      }
    } catch (error) {
      throw new functions.https.HttpsError(
        'unknown',
        (error as Error).message || 'An unexpected error occurred'
      )
    }
  }
) 