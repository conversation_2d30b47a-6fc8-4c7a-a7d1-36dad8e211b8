import React from 'react'

import styled from 'styled-components'
import { theme } from 'styles/theme'

import { Input } from 'components/ui/Input'

interface InputWithPrefixProps {
  label?: string
  prefix?: string
  placeholder?: string
  value: string
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  type?: string
  maxLength?: number
}

export const InputWithPrefix = ({
  label,
  prefix,
  placeholder = '',
  value,
  onChange,
  type,
  maxLength
}: InputWithPrefixProps) => {
  return (
    <InputBlockStyled>
      <InputLabelStyled>{label}</InputLabelStyled>
      <InputWrapStyled>
        {prefix && <InputIconStyled>{prefix}</InputIconStyled>}
        <InputStyled
          $withIcon={!!prefix}
          placeholder={placeholder}
          value={value}
          onChange={onChange}
          type={type}
          maxLength={maxLength}
        />
      </InputWrapStyled>
    </InputBlockStyled>
  )
}

const InputWrapStyled = styled.div`
  display: flex;
  align-items: center;
  width: 100%;
  position: relative;
`
const InputIconStyled = styled.span`
  display: flex;
  align-items: center;
  justify-content: center;

  position: absolute;
  left: 0;
  width: 2rem;
  height: 70%;

  border-right: 1px solid #c6d4dd;

  font-family: ${theme.fonts.bold};
  font-size: ${theme.remFont(14)};
  line-height: normal;
  color: ${theme.colors.midGrey600};

  pointer-events: none;
`

const InputBlockStyled = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  flex: 1;
`
const InputLabelStyled = styled.span`
  display: flex;
  align-items: center;

  margin: 0 0 0.2rem 0.5rem;
  color: ${theme.colors.midGrey600};
  font-family: ${theme.fonts.bold};
  font-size: ${theme.remFont(14)};
`

interface InputStyledProps {
  $withIcon?: boolean
}
const InputStyled = styled(Input)<InputStyledProps>`
  width: 100%;
  height: ${theme.rem(45)};
  padding: ${({ $withIcon }) => ($withIcon ? '0 2.4rem' : null)};

  border: 1px solid #c6d4dd;
  border-radius: ${theme.rem(12)};
  font-family: ${theme.fonts.bold};
  text-align: ${props => (props.$withIcon ? 'center' : null)};

  &:hover,
  &:focus {
    box-shadow: 0px 0px 10px -3px rgba(0, 0, 0, 0.4);
  }
`
