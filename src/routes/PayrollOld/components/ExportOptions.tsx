import React, { useEffect, useRef, useState } from 'react'
import { I18n } from 'react-redux-i18n'

import styled from 'styled-components'
import { theme } from 'styles/theme'

import arrowOpen from '../../../img/icons/arrow_down.svg'
import arrowClose from '../../../img/icons/arrow_up.svg'

interface ExportOptionsProps {
  currentOption: string
  exportOptions: string[]
  setExportOption: (option: string) => void
}

const ExportOptions = React.memo(
  ({
    currentOption: initialOption,
    exportOptions,
    setExportOption
  }: ExportOptionsProps) => {
    const optionRef = useRef<HTMLDivElement>(null)
    const [showChangeView, setShowChangeView] = useState(false)
    const [currentOption, setCurrentOption] = useState(initialOption)

    useEffect(() => {
      setCurrentOption(initialOption)
    }, [initialOption])

    const handleOptionSelect = (option: string) => {
      setCurrentOption(option)
      setExportOption(option)
      setShowChangeView(false)
    }

    const getValueTranslation = (option: string) => {
      switch (option) {
        case 'simplified':
          return I18n.t('attendance.export_modal.simplified')
        case 'detailed':
          return I18n.t('attendance.export_modal.detailed')
        default:
          return option
      }
    }

    return (
      <SelectContainerStyled
        onClick={() => setShowChangeView(!showChangeView)}
        ref={optionRef}
      >
        <CustomSelectStyled isOpened={showChangeView}>
          <p>{getValueTranslation(currentOption)}</p>
          <img
            src={showChangeView ? arrowClose : arrowOpen}
            alt=''
          />
        </CustomSelectStyled>
        {showChangeView && (
          <CustomSelectListStyled>
            {exportOptions.map(option => (
              <CustomSelectItemStyled
                key={option}
                onClick={e => {
                  e.stopPropagation()
                  handleOptionSelect(option)
                }}
              >
                <p>{getValueTranslation(option)}</p>
              </CustomSelectItemStyled>
            ))}
          </CustomSelectListStyled>
        )}
      </SelectContainerStyled>
    )
  }
)

export default ExportOptions

const SelectContainerStyled = styled.div<{ disabled?: boolean }>`
  margin-left: 64px;
  display: flex;
  min-width: 64px;
  min-height: 24px;
  flex: 1;
  height: 100%;
  position: relative;
  background-color: ${theme.colors.midGrey600};
  border-radius: ${theme.rem(14)};
  cursor: ${({ disabled }) => (disabled ? '' : 'pointer')};
`

const CustomSelectStyled = styled.div<{ isOpened: boolean }>`
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  flex: 1;
  padding: 0 ${theme.rem(25)};
  border-radius: ${({ isOpened }) => (isOpened ? theme.rem(14) : null)};
  background: ${({ isOpened }) => (isOpened ? '#dbe3eb' : null)};
  z-index: ${({ isOpened }) => (isOpened ? 2 : null)};

  p {
    color: ${({ isOpened }) => (isOpened ? theme.colors.darkGrey : '#fff')};
    font-family: ${theme.fonts.normal};
  }

  img {
    width: ${theme.rem(14)};
    height: ${theme.rem(14)};
  }
`

const CustomSelectListStyled = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  width: 100%;
  padding: ${theme.rem(20)} ${theme.rem(10)} 0;
  margin-top: ${theme.rem(-10)};
  position: absolute;
  top: 100%;
  z-index: 1;
  background-color: #fff;
  border-bottom-left-radius: ${theme.rem(14)};
  border-bottom-right-radius: ${theme.rem(14)};
  box-shadow: 0px 0 12px 0px rgb(172 190 208 / 50%);
  cursor: pointer;

  p {
    color: ${theme.colors.darkGrey};
  }
`

const CustomSelectItemStyled = styled.div<{ isSelected?: boolean }>`
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin-bottom: ${theme.rem(10)};
  padding: ${theme.rem(4)} ${theme.rem(8)} ${theme.rem(4)} ${theme.rem(14)};
  border-radius: ${theme.rem(20)};
  background-color: ${({ isSelected }) =>
    isSelected ? '#cdf5e8!important' : '#fff'};
  font-size: ${theme.remFont(14)};

  &:hover {
    background-color: #ecf1f5;
  }

  img {
    width: ${theme.rem(18)};
    height: ${theme.rem(18)};
  }
`
