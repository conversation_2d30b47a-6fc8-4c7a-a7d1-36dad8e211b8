import React, { MouseEvent, forwardRef, useEffect, useState } from 'react'
import Popover from 'react-bootstrap/Popover'
import { I18n } from 'react-redux-i18n'

import styled from 'styled-components'
import { theme } from 'styles/theme'

import { OutlineButton } from 'components/ui/OutlineButton'
import CustomTimePicker from 'components/ui/TimePicker'

import { getSingleBreakLength } from '../payrollUtils'

import { getShiftLength } from 'utils/schedule'
import { minutesToHours, minutesToMoment } from 'utils/time'

import { AttendanceBreak, AttendanceBreaks } from 'types/attendance'

import { ReactComponent as EditIcon } from 'img/icons/editIcon.svg'

type BreaksPopoverProps = {
  breaks: {
    [key: string]: AttendanceBreak
  }
  roundingTime: number
  onBreaksUpdate: (breaks: AttendanceBreaks) => void
}

type BreakRowProps = {
  breakData: AttendanceBreak
  index: number
  onEdit: () => void
  roundingTime: number
  isBeingEdited: boolean
  onSave: (breakData: AttendanceBreak) => void
}

const BreakRow = ({
  breakData,
  index,
  onEdit,
  roundingTime,
  isBeingEdited,
  onSave
}: BreakRowProps) => {
  const [breakDataCopy, setBreakDataCopy] = useState(breakData)
  const breakTime = getSingleBreakLength(breakDataCopy, roundingTime)

  useEffect(() => {
    if (isBeingEdited) {
      setBreakDataCopy(breakData)
    }
  }, [isBeingEdited, breakData])

  return (
    <WrapStyled>
      <BlockStyled>
        {!isBeingEdited && (
          <EditButtonStyled
            onClick={onEdit}
            $isActive={isBeingEdited}
          >
            <EditIcon />
          </EditButtonStyled>
        )}
        <TitleStyled>
          {I18n.t('attendance.break')} #{index + 1}
        </TitleStyled>
        <TimeStyled>
          {breakTime ? (
            `${breakTime} ${
              breakTime !== 1
                ? I18n.t('attendance.minutes')
                : I18n.t('attendance.minute')
            }`
          ) : (
            <>&nbsp;</>
          )}
        </TimeStyled>
        {isBeingEdited ? (
          <BreakTimeStyled>
            <CustomTimePickerStyled
              minuteStep={roundingTime}
              value={minutesToMoment(breakDataCopy.start)}
              hideArrow
              onChange={value => {
                const minutes = value!.hour() * 60 + value!.minute()
                const lengthRounded = getShiftLength(
                  minutes,
                  breakDataCopy.end,
                  0
                )
                setBreakDataCopy(state => ({
                  ...state,
                  start: minutes,
                  lengthRounded
                }))
              }}
              alignPopupMenu='left'
            />
            <BreakTimeTextStyled>{I18n.t('attendance.to')}</BreakTimeTextStyled>
            <CustomTimePickerStyled
              minuteStep={roundingTime}
              value={
                breakDataCopy.end !== undefined
                  ? minutesToMoment(breakDataCopy.end)
                  : undefined
              }
              hideArrow
              alignPopupMenu='right'
              onChange={value => {
                const minutes = value!.hour() * 60 + value!.minute()
                const lengthRounded = getShiftLength(
                  breakDataCopy.start,
                  minutes,
                  0
                )
                setBreakDataCopy(state => ({
                  ...state,
                  end: minutes,
                  lengthRounded
                }))
              }}
            />
          </BreakTimeStyled>
        ) : (
          <BreakTimeStyled>
            {minutesToHours(breakDataCopy.start)}{' '}
            <BreakTimeTextStyled>{I18n.t('attendance.to')}</BreakTimeTextStyled>
            {minutesToHours(breakDataCopy.end)}
          </BreakTimeStyled>
        )}
      </BlockStyled>

      {isBeingEdited && (
        <ButtonBlockStyled>
          <OutlineButtonStyled
            color='red'
            onClick={onEdit}
          >
            {I18n.t('common.close')}
          </OutlineButtonStyled>
          <OutlineButtonStyled
            color='green'
            onClick={() => onSave(breakDataCopy)}
          >
            {I18n.t('common.save_shorten')}
          </OutlineButtonStyled>
        </ButtonBlockStyled>
      )}
    </WrapStyled>
  )
}

const BreaksPopover = forwardRef<HTMLDivElement, BreaksPopoverProps>(
  ({ breaks, roundingTime, onBreaksUpdate, ...rest }, ref) => {
    const [breakKeyEdited, setBreakKeyEdited] = useState('')

    return (
      <PopoverStyled
        ref={ref}
        id='attendance-shift-popover-breaks'
        $isEdit={Boolean(breakKeyEdited)}
        {...rest}
        onClick={(e: MouseEvent) => e.stopPropagation()}
      >
        <ContainerStyled $isEdit={Boolean(breakKeyEdited)}>
          {Object.entries(breaks).map(([key, breakData], i) => (
            <BreakRow
              breakData={breakData}
              key={key}
              index={i}
              isBeingEdited={breakKeyEdited === key}
              onEdit={() => {
                if (breakKeyEdited === key) {
                  setBreakKeyEdited('')
                } else {
                  setBreakKeyEdited(key)
                }
              }}
              roundingTime={roundingTime}
              onSave={breakData => {
                const newBreaks = {
                  ...breaks,
                  [breakKeyEdited]: breakData
                }
                onBreaksUpdate(newBreaks)
                setBreakKeyEdited('')
              }}
            />
          ))}
        </ContainerStyled>
      </PopoverStyled>
    )
  }
)

export default BreaksPopover

const PopoverStyled = styled(Popover)<{ $isEdit?: boolean }>`
  max-width: 20rem;
  width: unset;
  padding: 1rem;
  min-width: 11.5rem;

  && {
    margin-top: ${({ $isEdit }) => ($isEdit ? '-14rem' : '-1.5rem')};
    .arrow {
      border-top-color: #69748f;
      :after {
        border-top-color: #69748f;
      }
    }
  }

  border-radius: 1.2rem;
  background-color: #69748f;
`

const ContainerStyled = styled.div<{ $isEdit?: boolean }>`
  display: flex;
  justify-content: space-between;

  gap: 1rem;
  width: 100%;
  max-width: 100%;
  overflow-x: auto;
  min-height: ${({ $isEdit }) => ($isEdit ? '18.5rem' : 'unset')};
`

const WrapStyled = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  align-self: flex-start;

  flex: 1;
  width: 8.5rem;
`
const BlockStyled = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  flex: 1;
  width: 100%;
  padding: 0.5rem 0.8rem;

  position: relative;

  border-radius: 0.8rem;
  background-color: #fff;
`
const TitleStyled = styled.p`
  display: flex;
  align-self: flex-start;

  color: ${theme.colors.midGrey600};
  text-align: left;
  font-family: ${theme.fonts.boldItalic};
  font-size: ${theme.remFont(13)};
`
const TimeStyled = styled.p`
  margin: 0.5rem 0;

  text-transform: lowercase;
  text-align: center;
  color: ${theme.colors.darkGrey};
  font-family: ${theme.fonts.heavy};
  font-size: ${theme.remFont(17)};
  line-height: normal;
`
const BreakTimeStyled = styled.div`
  display: flex;
  align-items: center;

  gap: 0.4rem;

  color: #a3acbc;
  font-family: ${theme.fonts.bold};
  font-size: ${theme.remFont(14)};
`

const BreakTimeTextStyled = styled.span`
  color: #bcc3cf;
  font-family: ${theme.fonts.boldItalic};
  font-size: ${theme.remFont(12)};
`
const EditButtonStyled = styled.button<{ $isActive?: boolean }>`
  display: flex;

  padding: 0.4rem;

  position: absolute;
  right: 0;
  top: 0;

  border: 0;
  background: none;
  color: ${({ $isActive }) =>
    $isActive ? theme.colors.blue : theme.colors.darkGrey};
  :hover {
    color: ${theme.colors.blue};
  }

  svg {
    width: 0.75rem;
    height: 0.75rem;
    fill: currentColor;
  }
`

const ButtonBlockStyled = styled.div`
  display: flex;

  gap: 0.75rem;
  margin-top: 0.75rem;
  width: 100%;
`

const OutlineButtonStyled = styled(OutlineButton)`
  flex: 1;
  min-width: unset;
  width: unset;
  height: 2rem;

  border-color: transparent;

  font-size: ${theme.remFont(14)};
`

const CustomTimePickerStyled = styled(CustomTimePicker)`
  height: 2rem;
  padding: 0.2rem;
  min-width: 3rem;

  border: 0;
  background-color: #ecf1f5;

  font-size: 0.8rem;
`
