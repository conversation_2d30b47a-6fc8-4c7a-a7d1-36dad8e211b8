import React, { forwardRef, useEffect, useRef, useState } from 'react'
import Overlay from 'react-bootstrap/Overlay'
import Popover, { PopoverProps } from 'react-bootstrap/Popover'
import { I18n } from 'react-redux-i18n'

import dayjs from 'dayjs'
import firebase from 'firebase/app'
import 'firebase/database'
import _, { cloneDeep, isEmpty, isNumber } from 'lodash'
import styled from 'styled-components'
import { theme } from 'styles/theme'

import { OutlineButton } from 'components/ui/OutlineButton'
import CustomSelect from 'components/ui/Select'
import CustomTimePicker from 'components/ui/TimePicker'

import { roundTime } from '../payrollUtils'
import BreaksPopover from './BreaksPopover'

import { SALARY_TYPE_YEARLY } from 'utils/constants'
import { fetchAndGetEmployeeRate } from 'utils/employees'
import { getTime, minutesToHours } from 'utils/time'

import {
  AttendanceBreaks,
  AttendanceShift,
  IAttendanceEnhancedShift,
  PartialShift
} from 'types/attendance'
import { IPositions } from 'types/company'
import { IEmployee } from 'types/employee'
import { Option } from 'types/selectOption'

import { ReactComponent as AddCircleIcon } from 'img/icons/add.svg'
import checkGreenIcon from 'img/icons/checkGreenFillIcon.svg'
import clockFilledBlue from 'img/icons/clockFilledBlue.svg'
import clockFilledOrange from 'img/icons/clockFilledOrange.svg'
import closeFilledIcon from 'img/icons/closeFilledIcon.svg'
import exclamationOrange from 'img/icons/exclamationOrange.svg'
import exclamationRed from 'img/icons/exclamationRed.svg'
import trashIcon from 'img/icons/trash.svg'

interface Props extends PopoverProps {
  onClose: () => void
  onSave: (shift: { [key: string]: AttendanceShift }) => void
  onDeleteShift: (key: string) => void
  shifts: {
    [key: string]: IAttendanceEnhancedShift
  }
  jobs: IPositions
  date: string
  employee: IEmployee
  roundingTime: number
  companyId: string
  shiftKey: string
  isOnLeft: boolean
  showBreakPopover: boolean
  setShowBreakPopover: (show: boolean) => void
  id: string
}

type CopyOfShifts = {
  [key: string]: PartialShift
}

const EditShiftPopover = forwardRef<HTMLDivElement, Props>((props, ref) => {
  const {
    onClose,
    onSave,
    shifts,
    jobs,
    date,
    employee,
    roundingTime,
    companyId,
    shiftKey,
    onDeleteShift,
    isOnLeft,

    showBreakPopover,
    setShowBreakPopover,
    id,
    ...rest
  } = props

  const [selectedShiftKey, setSelectedShiftKey] = useState('')
  const [copyOfShifts, setcopyOfShifts] = useState<CopyOfShifts>({})
  const breaksPopoverElementRef = useRef<HTMLElement>(null)

  useEffect(() => {
    setShowBreakPopover(false)
  }, [selectedShiftKey, setShowBreakPopover])

  useEffect(() => {
    if (shiftKey && shifts[shiftKey]) {
      setcopyOfShifts(shifts)
      setSelectedShiftKey(shiftKey)
    } else {
      const path = `Attendance/${companyId}/${date}/${employee.uid}`
      const key = firebase.database().ref(path).push().key as string
      setcopyOfShifts({
        [key]: {
          manuallyCreated: true
        }
      })
      setSelectedShiftKey(key)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  if (!employee || !selectedShiftKey) {
    return (
      <Popover
        id={id}
        ref={ref}
        {...rest}
      />
    )
  }

  const {
    start = null,
    end = null,
    positionId = '',
    manuallyCreated = false,
    isConfirmed = false,
    breaks = {},
    scheduledShift,
    isClockInDifferent,
    isClockOutDiffrent,
    notClockedOut,
    type,
    isConflictingShift
  } = copyOfShifts[selectedShiftKey]

  const isAutoShift = type === SALARY_TYPE_YEARLY && start === null
  const positions = employee.positions || employee.lastPositions || []

  const positionOptions: { label: string; value: string }[] = []

  Object.values(shifts).map(shift => ({ categoryId: shift.positionId }))
  _.uniqBy(positions, 'categoryId').forEach(position => {
    if (jobs?.[position.categoryId]?.name) {
      positionOptions.push({
        label: jobs[position.categoryId].name,
        value: position.categoryId
      })
    }
  })

  // this is a fix for the case when position was deleted from the employee
  if (
    positionId &&
    !positionOptions.some(option => option.value === positionId)
  ) {
    if (jobs?.[positionId]?.name) {
      positionOptions.push({
        value: positionId,
        label: jobs?.[positionId]?.name
      })
    }
  }

  const onConfirm = async () => {
    const path = `Attendance/${companyId}/${date}/${employee.uid}`
    const key =
      selectedShiftKey || (firebase.database().ref(path).push().key as string)

    const { rate, additionalSalary, type } = await fetchAndGetEmployeeRate({
      companyId: companyId || '',
      employeeId: employee.uid,
      positionId,
      jobs
    })

    const isConfirmed =
      (isNumber(end) && isNumber(start) && !!positionId) || false

    const payload = {
      start: start as number,
      end: end as number,
      positionId: positionId as string,
      isConfirmed,
      manuallyCreated,
      rate,
      additionalSalary,
      type,
      breaks
    }

    firebase
      .database()
      .ref(path + '/' + key)
      .set(payload)
      .then(() => {
        onFieldChange('isConfirmed', scheduledShift ? isConfirmed : false)
        onSave({ [key]: payload })
      })
  }

  const onBreaksUpdate = (newBreaks: AttendanceBreaks) => {
    const path = `Attendance/${companyId}/${date}/${employee.uid}/${selectedShiftKey}/breaks`

    firebase
      .database()
      .ref(path)
      .set(newBreaks)
      .then(() => {
        setShowBreakPopover(false)
        setcopyOfShifts(state => ({
          ...state,
          [selectedShiftKey]: {
            ...state[selectedShiftKey],
            breaks: newBreaks
          }
        }))
      })
  }

  const onDelete = () => {
    const path = `Attendance/${companyId}/${date}/${employee.uid}/${selectedShiftKey}`

    firebase
      .database()
      .ref(path)
      .set(null)
      .then(() => {
        onDeleteShift(selectedShiftKey)
        const copy = cloneDeep(copyOfShifts)

        delete copy[selectedShiftKey]

        const nextShiftKey = Object.keys(copy).find(
          key => key !== selectedShiftKey
        )

        if (nextShiftKey) {
          setSelectedShiftKey(nextShiftKey)

          setcopyOfShifts(copy)
        } else {
          onClose()
        }
      })
  }

  const onFieldChange = (
    key: string,
    value: boolean | number | string | null
  ) => {
    const copy = cloneDeep(copyOfShifts)

    // @ts-ignore
    copy[selectedShiftKey][key as keyof PartialShift] = value

    setcopyOfShifts(copy)
  }

  const shiftKeys = Object.keys(copyOfShifts).sort(
    (a, b) =>
      (copyOfShifts[a]!.start || Infinity) -
      (copyOfShifts[b]!.start || Infinity)
  )
  const shiftIndex = shiftKeys.indexOf(selectedShiftKey)

  const shiftsChanged = !_.isEqual(shifts, copyOfShifts)
  const notScheduled = !scheduledShift

  if (positionOptions.length === 1 && !positionId) {
    onFieldChange('positionId', positionOptions[0].value)
  }

  const shiftStartRounded = roundTime(start, roundingTime)
  const shiftEndRounded =
    end === undefined || end === null ? null : roundTime(end, roundingTime)

  return (
    <PopoverStyled
      id={id}
      ref={ref}
      {...rest}
      $isOnLeft={isOnLeft}
    >
      <HeaderStyled>
        <DateStyled>{dayjs(date, 'YYYY-MM-DD').format('dddd D')}</DateStyled>
        <EmlpoyeeStyled>
          {employee.name} {employee.surname}
        </EmlpoyeeStyled>
      </HeaderStyled>
      <ShiftRowStyled>
        {shiftKeys.map((shiftKey, i) => (
          <ShiftRowButtonStyled
            activeShift={shiftIndex === i}
            onClick={() => setSelectedShiftKey(shiftKey)}
            key={i}
          >
            {i < 4 && I18n.t('indexes.' + (i + 1))}{' '}
            {shiftIndex === i || shiftKeys.length <= 2
              ? I18n.t('attendance.shift')
              : null}
            {!isAutoShift && (
              <DeleteShiftButtonStyled
                color='red'
                onClick={onDelete}
              >
                <img
                  src={trashIcon}
                  alt=''
                />
              </DeleteShiftButtonStyled>
            )}
          </ShiftRowButtonStyled>
        ))}

        {shiftKeys.length < 4 && (
          <AddShiftButtonStyled
            disabled={shiftKeys.length === 4}
            $isAbsolute={shiftKeys.length < 2}
            onClick={() => {
              const path = `Attendance/${companyId}/${date}/${employee.uid}`
              const key = firebase.database().ref(path).push().key as string

              setcopyOfShifts({
                ...copyOfShifts,
                [key]: {
                  manuallyCreated: true
                }
              })
              setSelectedShiftKey(key)
            }}
          >
            <AddCircleIconStyled />
            {I18n.t('attendance.add_shift')}
          </AddShiftButtonStyled>
        )}
      </ShiftRowStyled>

      <BodyStyled manuallyCreated={manuallyCreated}>
        <RolesRowStyled notScheduled={true}>
          <ColumnStyled>
            {notScheduled && !positionId && !manuallyCreated && (
              <ExclamationIconStyled
                orange
                zIndex
                top
              />
            )}
            <ColumnLabelStyled className={notScheduled ? 'requiredField' : ''}>
              {I18n.t('attendance.role')}
            </ColumnLabelStyled>
            <CustomSelectStyled
              key={selectedShiftKey}
              value={positionOptions.find(({ value }) => value === positionId)}
              options={positionOptions}
              onChange={option => {
                const newValue = option as Option
                if (newValue) {
                  onFieldChange('positionId', newValue.value)
                }
              }}
              placeholder={I18n.t('attendance.select')}
            />
          </ColumnStyled>
        </RolesRowStyled>

        {!manuallyCreated && (
          <ScheduledBlockStyled>
            <TableRowStyled>
              <TableLabelStyled />
              <TableLabelStyled>{I18n.t('attendance.in')}</TableLabelStyled>
              <TableLabelStyled>{I18n.t('attendance.out')}</TableLabelStyled>
            </TableRowStyled>
            <TableRowStyled>
              <TableLabelStyled align='right'>
                {I18n.t('attendance.scheduled')}
              </TableLabelStyled>
              {scheduledShift ? (
                <>
                  <TableLabelStyled>
                    {minutesToHours(scheduledShift.start)}
                  </TableLabelStyled>
                  <TableLabelStyled>
                    {minutesToHours(scheduledShift.end) || '-'}
                  </TableLabelStyled>
                </>
              ) : (
                <TableLabelStyled $fullWidth>
                  {I18n.t('attendance.notScheduled')}
                </TableLabelStyled>
              )}
            </TableRowStyled>
            <TableRowStyled>
              <TableLabelStyled align='right'>
                {I18n.t('attendance.clocked')}
              </TableLabelStyled>
              <TableTimeStyled
                isDifferentTime={Boolean(isClockInDifferent && scheduledShift)}
              >
                <TableLabelStyled>{minutesToHours(start)}</TableLabelStyled>
              </TableTimeStyled>
              <TableTimeStyled
                notClocked={notClockedOut}
                isDifferentTime={Boolean(isClockOutDiffrent && scheduledShift)}
              >
                <TableLabelStyled>
                  {minutesToHours(end) || ' '}
                </TableLabelStyled>
              </TableTimeStyled>
            </TableRowStyled>
          </ScheduledBlockStyled>
        )}

        <BottomRowStyled
          notClocked={!isConfirmed && notClockedOut}
          isDifferentTime={
            !isConfirmed && (isClockInDifferent || isClockOutDiffrent)
          }
          manuallyCreated={manuallyCreated}
        >
          {manuallyCreated && (
            <ManualRowStyled>
              <TableLabelStyled>
                {isConfirmed || isConflictingShift
                  ? I18n.t('attendance.createdShift')
                  : I18n.t('attendance.creatingShift')}
              </TableLabelStyled>
              <TableLabelStyled className='requiredField'>
                {I18n.t('attendance.in')}
              </TableLabelStyled>
              <TableLabelStyled>{I18n.t('attendance.out')}</TableLabelStyled>
            </ManualRowStyled>
          )}

          {notScheduled && !isConfirmed && !manuallyCreated && (
            <WarningRowStyled>
              <ExclamationIconStyled orange />
              {I18n.t('attendance.employeeNotScheduled')}
            </WarningRowStyled>
          )}

          <TableLabelStyled align='right'>
            {!manuallyCreated ? I18n.t('attendance.rounded') : ' '}
          </TableLabelStyled>

          <TimeWrapStyled notClocked={false}>
            {(isConfirmed || (!isClockInDifferent && !manuallyCreated)) && (
              <CheckGreenIconStyled
                src={checkGreenIcon}
                alt=''
                zIndex
              />
            )}

            {!isConfirmed && isClockInDifferent && scheduledShift && (
              <ExclamationIconStyled
                orange
                zIndex
              />
            )}
            <TimePickerWrapStyled
              $isDifferentTime={isClockInDifferent && !isConfirmed}
            >
              <CustomTimePickerStyled
                minuteStep={roundingTime}
                value={
                  !manuallyCreated ? getTime(shiftStartRounded) : getTime(start)
                }
                placeholder='(8:00)'
                hideArrow
                onChange={moment =>
                  onFieldChange(
                    'start',
                    moment
                      ? start || start === 0
                        ? moment.hour() * 60 + moment.minute()
                        : moment.hour() * 60
                      : null
                  )
                }
              />
            </TimePickerWrapStyled>
          </TimeWrapStyled>
          <TimeWrapStyled notClocked={notClockedOut && !isConfirmed}>
            {(isConfirmed || (!isClockOutDiffrent && !manuallyCreated)) && (
              <CheckGreenIconStyled
                src={checkGreenIcon}
                alt=''
                zIndex
              />
            )}
            {!isConfirmed && isClockOutDiffrent && scheduledShift && (
              <ExclamationIconStyled
                orange
                zIndex
              />
            )}
            {!isConfirmed && notClockedOut && (
              <CloseIconStyled
                zIndex
                small
              />
            )}

            <TimePickerWrapStyled
              $notClocked={notClockedOut && !isConfirmed}
              $isDifferentTime={isClockOutDiffrent && !isConfirmed}
            >
              <CustomTimePickerStyled
                minuteStep={roundingTime}
                value={
                  !manuallyCreated ? getTime(shiftEndRounded) : getTime(end)
                }
                placeholder='(17:00)'
                hideArrow
                onChange={moment =>
                  onFieldChange(
                    'end',
                    moment
                      ? end || end === 0
                        ? moment.hour() * 60 + moment.minute()
                        : moment.hour() * 60
                      : null
                  )
                }
              />
            </TimePickerWrapStyled>
          </TimeWrapStyled>
          {!isEmpty(breaks) && (
            <>
              <TableLabelStyled align='right'>
                {I18n.t('attendance.break')}s
              </TableLabelStyled>
              <TableLabelStyled $fullWidth>
                <Overlay
                  show={showBreakPopover}
                  onHide={() => {
                    setShowBreakPopover(false)
                  }}
                  placement='top'
                  rootClose
                  target={() => breaksPopoverElementRef.current}
                >
                  <BreaksPopover
                    breaks={breaks}
                    roundingTime={roundingTime}
                    onBreaksUpdate={onBreaksUpdate}
                  />
                </Overlay>
                <BreaksButtonStyled
                  ref={
                    breaksPopoverElementRef as React.RefObject<HTMLButtonElement>
                  }
                  onClick={() => setShowBreakPopover(!showBreakPopover)}
                  isActiveButton={showBreakPopover}
                >
                  {Object.keys(breaks).length}{' '}
                  {Object.keys(breaks).length === 1
                    ? I18n.t('attendance.break')
                    : I18n.t('attendance.breaks')}
                </BreaksButtonStyled>
              </TableLabelStyled>
            </>
          )}
        </BottomRowStyled>
      </BodyStyled>

      <FooterStyled>
        <OutlineButton
          color='red'
          onClick={onClose}
        >
          {I18n.t('common.cancel')}
        </OutlineButton>
        {isConfirmed && !shiftsChanged ? (
          <ShiftStatusStyled>
            <img
              src={checkGreenIcon}
              alt=''
            />
            {I18n.t('attendance.shiftConfirmed')}
          </ShiftStatusStyled>
        ) : (
          <OutlineButton
            disabled={start === null || positionId === ''}
            onClick={onConfirm}
            modalContrast
            color='green'
          >
            {isNumber(shiftStartRounded) && !isNumber(shiftEndRounded)
              ? I18n.t('attendance.editShift')
              : I18n.t('attendance.confirmShift')}
          </OutlineButton>
        )}
      </FooterStyled>
    </PopoverStyled>
  )
})

export default EditShiftPopover

const PopoverStyled = styled(Popover)<{
  $transform?: string
  $isOnLeft?: boolean
}>`
  width: 21rem;
  max-width: 21rem;
  padding: 1rem 0.6rem;

  /* hardocded absolute positioning */
  transform: unset !important;
  margin-right: ${({ $isOnLeft }) => ($isOnLeft ? '110%' : null)} !important;
  margin-left: ${({ $isOnLeft }) => ($isOnLeft ? null : '110%')} !important;
  inset: unset !important;
  left: ${({ $isOnLeft }) => ($isOnLeft ? null : '0')} !important;
  right: ${({ $isOnLeft }) => ($isOnLeft ? '0' : null)} !important;
  top: 0 !important;

  border-radius: 1.2rem;
  background-color: #69748f;

  &.bs-popover-left .arrow {
    top: 2rem !important;
    transform: unset !important;
    ::before,
    ::after {
      border-left-color: #69748f;
    }
  }
  &.bs-popover-right .arrow {
    top: 2rem !important;
    transform: unset !important;
    ::before,
    ::after {
      border-right-color: #69748f;
    }
  }
`

const HeaderStyled = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
`
const DateStyled = styled.div`
  color: #d9e1e9;
  font-size: ${theme.remFont(15)};
  font-family: ${theme.fonts.boldItalic};
`
const EmlpoyeeStyled = styled.div`
  color: #fff;
  font-size: ${theme.remFont(17)};
  font-family: ${theme.fonts.bold};
`

const ShiftRowStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;

  width: 100%;
  margin-top: 1rem;
  gap: 0.4rem;
`

const DeleteShiftButtonStyled = styled(OutlineButton)`
  display: none;

  width: 1.2rem;
  height: 1.2rem;
  padding: 0;
  margin: 0 -0.3rem 0 1rem;

  border-radius: 50%;
  border: none;

  img {
    width: 0.8rem;
    height: 0.8rem;
  }
`

type ShiftRowButtonStyledProps = {
  activeShift?: boolean
}

const ShiftRowButtonStyled = styled.div<ShiftRowButtonStyledProps>`
  display: flex;
  align-items: center;
  justify-content: center;

  padding: 0.5rem 0.8rem;

  position: relative;
  border: unset;
  border-radius: 0.8rem 0.8rem 0 0;
  background-color: ${({ activeShift }) =>
    activeShift ? theme.colors.midGrey600 : 'transparent'};

  color: ${({ activeShift }) => (activeShift ? '#fff' : '#d9e1e9')};
  font-size: ${theme.remFont(13)};
  font-family: ${theme.fonts.bold};
  cursor: pointer;

  ${DeleteShiftButtonStyled} {
    display: ${({ activeShift }) => (activeShift ? 'flex' : null)};
  }

  &:after {
    content: ${({ activeShift }) => (activeShift ? "''" : null)};
    width: 100%;
    height: 4px;
    position: absolute;
    bottom: 0px;
    background-color: ${theme.colors.blue};
  }

  &:hover {
    color: #fff;

    &:after {
      content: '';
    }
  }
`

const AddShiftButtonStyled = styled.button<{ $isAbsolute?: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;

  padding: 0.4rem 0.5rem;
  gap: 0.3rem;

  position: ${({ $isAbsolute }) => ($isAbsolute ? 'absolute' : null)};
  right: ${({ $isAbsolute }) => ($isAbsolute ? '0.75rem' : null)};

  border: none;
  background: transparent;
  border-radius: 0.8rem;

  color: ${theme.colors.blue};
  font-size: ${theme.remFont(13)};
  font-family: ${theme.fonts.normal};

  &:not(:disabled):hover {
    background-color: ${theme.colors.midGrey600};
  }

  &:disabled {
    color: #7b869f;
    position: relative;
    right: unset;
  }
`

const AddCircleIconStyled = styled(AddCircleIcon)`
  width: 0.9rem;
  height: 0.9rem;
  stroke: currentColor;
`

type BodyStyledProps = {
  manuallyCreated?: boolean
}

const BodyStyled = styled.div<BodyStyledProps>`
  display: flex;
  align-items: center;
  flex-direction: column;

  width: 100%;

  border-radius: 1.2rem;
  background-color: ${({ manuallyCreated }) =>
    manuallyCreated ? '#dbe3eb' : '#f9fcff'};
`

const CustomSelectStyled = styled(CustomSelect)``

type RolesRowStyledProps = {
  notScheduled?: boolean
}

const RolesRowStyled = styled.div<RolesRowStyledProps>`
  display: flex;
  align-items: center;
  justify-content: space-between;

  width: 100%;
  padding: 0.8rem 1rem;
  gap: 1rem;

  border-radius: 1.2rem;
  background-color: ${({ notScheduled }) =>
    notScheduled ? '#fdeacd' : '#dbe3eb'};

  ${CustomSelectStyled} {
    .Select__placeholder {
      color: ${({ notScheduled }) =>
        notScheduled ? '#f9b63a!important' : null};
    }
  }
`

const ColumnStyled = styled.div`
  display: flex;
  flex-direction: column;

  max-width: 50%;
  margin-inline: auto;
  flex: 1;
  gap: 0.2rem;

  position: relative;
`

const ColumnLabelStyled = styled.p`
  margin-inline: 0.6rem;

  font-size: ${theme.remFont(14)};
  font-family: ${theme.fonts.boldItalic};
  color: ${theme.colors.midGrey600};
  text-align: left;
`

const ScheduledBlockStyled = styled.div`
  display: flex;
  align-items: center;
  flex-direction: column;

  width: 100%;
  padding: 0 1rem 0.6rem;
`

const TableRowStyled = styled.div`
  display: grid;
  align-items: center;
  grid-template-columns: repeat(3, 1fr);
  grid-column-gap: 1.2rem;

  padding: 0.4rem 0;
  width: 100%;
`

type TableLabelStyledProps = {
  $fullWidth?: boolean
  align?: string
}

const TableLabelStyled = styled.span<TableLabelStyledProps>`
  grid-column-start: ${({ $fullWidth }) => ($fullWidth ? 2 : null)};
  grid-column-end: ${({ $fullWidth }) => ($fullWidth ? 4 : null)};
  padding: ${({ $fullWidth }) => ($fullWidth ? 0 : null)};

  font-size: ${theme.remFont(14)};
  font-family: ${theme.fonts.boldItalic};
  color: ${({ $fullWidth }) =>
    $fullWidth ? '#dde5ec' : theme.colors.midGrey600};
  text-align: ${props => props.align || 'center'};
`

const ManualRowStyled = styled(TableRowStyled)`
  grid-column-start: 1;
  grid-column-end: 4;
  grid-column-gap: 0.4rem;
  padding-block: 0;

  ${TableLabelStyled} {
    &:first-child {
      padding-bottom: 0.7rem;
      color: ${theme.colors.blue};
      white-space: nowrap;
    }
  }
`

type BottomRowStyledProps = {
  notClocked?: boolean
  isDifferentTime?: boolean
  manuallyCreated?: boolean
}

const BottomRowStyled = styled(TableRowStyled)<BottomRowStyledProps>`
  grid-column-gap: 0.9rem;
  grid-row-gap: ${({ manuallyCreated }) => (manuallyCreated ? 0 : '0.8rem')};
  padding: ${({ manuallyCreated }) =>
    manuallyCreated ? '0.5rem 0.8rem 0.8rem' : '0.8rem 1rem'};

  background-color: ${props =>
    props.notClocked
      ? '#fee6eb'
      : props.isDifferentTime
        ? '#fdeacd'
        : props.manuallyCreated
          ? '#c6dff8'
          : '#dbe3eb'};
  border-radius: 1.2rem;

  > ${TableLabelStyled} {
    padding-right: 0.25rem;
  }
`

type ExclamationIconStyledProps = {
  orange?: boolean
  zIndex?: boolean
  top?: boolean
}

const ExclamationIconStyled = styled.div<ExclamationIconStyledProps>`
  display: flex;
  justify-content: center;
  align-items: center;

  width: 1rem;
  height: 1rem;

  position: absolute;
  right: -0.3rem;
  top: ${({ top }) => (top ? '0.8rem' : '-0.4rem')};
  z-index: ${({ zIndex }) => (zIndex ? '1' : '')};

  background-image: url(${({ orange }) =>
    orange ? exclamationOrange : exclamationRed});
  background-color: ${({ orange }) => (orange ? '#f9b63a' : '#ec758a')};
  background-size: 0.1rem;
  background-position: center;
  background-repeat: no-repeat;
  border-radius: 50%;
`

const WarningRowStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;

  grid-column-start: 1;
  grid-column-end: 4;

  color: #f9b63a;
  font-family: ${theme.fonts.boldItalic};
  font-size: ${theme.remFont(14)};

  ${ExclamationIconStyled} {
    margin-right: 0.5rem;

    position: relative;
    top: unset;
    right: unset;
  }
`

type TimeWrapStyledProps = {
  notClocked?: boolean
}

const TimeWrapStyled = styled.div<TimeWrapStyledProps>`
  position: relative;
  border-radius: 0.6rem;

  box-shadow: ${({ notClocked }) =>
    notClocked ? '0px 0px 5px 1px #ec758a' : null};

  img {
    width: 1rem;
    height: 1rem;

    position: absolute;
    right: -0.3rem;
    top: -0.4rem;
  }
`

type TableTimeStyledProps = {
  notClocked?: boolean
  isDifferentTime?: boolean
}

const TableTimeStyled = styled.div<TableTimeStyledProps>`
  display: flex;
  align-items: center;
  justify-content: center;

  padding: 0.4rem 0.2rem;
  min-height: 2rem;

  border-radius: 0.6rem;
  background-color: ${props =>
    props.notClocked
      ? '#fee6eb'
      : props.isDifferentTime
        ? '#fdeacd'
        : '#ecf1f5'};

  ${TableLabelStyled} {
    font-family: ${theme.fonts.heavy};
    text-align: center;
    color: ${props =>
      props.notClocked
        ? '#ec758a'
        : props.isDifferentTime
          ? '#f9b63a'
          : theme.colors.darkGrey};
  }
`

type CheckGreenIconStyledProps = {
  zIndex?: boolean
}

const CheckGreenIconStyled = styled.img<CheckGreenIconStyledProps>`
  width: 1.1rem;
  height: 1.1rem;
  z-index: ${({ zIndex }) => (zIndex ? '1' : null)};
`

const FooterStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;

  padding: 1rem 0.4rem 0;
  gap: 1rem;
`
const ShiftStatusStyled = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;

  gap: 0.5rem;

  font-size: ${theme.remFont(14)};
  color: ${theme.colors.green};
  font-family: ${theme.fonts.bold};

  img {
    width: 1rem;
    height: 1rem;
  }
`

type BreaksButtonStyledProps = {
  isActiveButton?: boolean
}

const BreaksButtonStyled = styled.button<BreaksButtonStyledProps>`
  padding: 0;
  margin: 0.4rem 0;
  border: none;
  background: none;

  text-decoration: underline;
  color: ${({ isActiveButton }) =>
    isActiveButton ? theme.colors.blue : theme.colors.darkGrey};
  font-family: ${theme.fonts.heavy};
  font-size: ${theme.remFont(14)};
  text-transform: lowercase;
  text-decoration-thickness: 2px;

  &:hover {
    color: ${theme.colors.blue};
  }
`

type ClockIconStyledProps = {
  orange?: boolean
  zIndex?: boolean
  top?: boolean
  small?: boolean
}

const ClockIconStyled = styled.div<ClockIconStyledProps>`
  display: flex;
  justify-content: center;
  align-items: center;

  width: ${theme.rem(23)};
  height: ${theme.rem(23)};

  position: absolute;
  right: -${theme.rem(6)};
  top: -${theme.rem(8)};

  background-image: url(${({ orange }) =>
    orange ? clockFilledOrange : clockFilledBlue});
  background-color: ${({ orange }) => (orange ? '#f9b63a' : theme.colors.blue)};
  background-size: ${theme.rem(15)};
  background-position: center;
  background-repeat: no-repeat;
  border-radius: 50%;
`

const CloseIconStyled = styled(ClockIconStyled)`
  width: ${({ small }) => (small ? theme.rem(20) : '')};
  height: ${({ small }) => (small ? theme.rem(20) : '')};

  background-image: url(${closeFilledIcon});
  background-color: #fff;
  background-size: 100%;

  z-index: ${({ zIndex }) => (zIndex ? '1' : '')};
`

const CustomTimePickerStyled = styled(CustomTimePicker)``
type TimePickerWrapStyledProps = {
  $notClocked?: boolean
  $isDifferentTime?: boolean
}

const TimePickerWrapStyled = styled.div<TimePickerWrapStyledProps>`
  ${CustomTimePickerStyled} {
    font-size: 0.85rem;
    background-color: #fff;
    color: ${props =>
      props.$notClocked
        ? '#ec758a!important'
        : props.$isDifferentTime
          ? '#f9b63a!important'
          : theme.colors.darkGrey};

    ::placeholder {
      font-family: ${theme.fonts.normalItalic};
    }
  }
`
