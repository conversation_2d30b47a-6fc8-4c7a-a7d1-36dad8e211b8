import { I18n } from 'react-redux-i18n'

import { Dayjs } from 'dayjs'
import { forEach, round } from 'lodash'

import { getSalaryRate } from './utils/get-salary-rate'
import {
  SALARY_TYPE_HOURLY,
  SALARY_TYPE_YEARLY,
  SalaryRateUnit
} from 'utils/constants'

import { ExportCell } from './types/exportCell'
import { IAttendanceEnhancedShifts } from 'types/attendance'
import { IPositions } from 'types/company'
import { IEmployee as Employee } from 'types/employee'

type Props = {
  startAsDate: string
  endAsDate: string
  employees: Employee[]
  name: string
  startOfPeriod: Dayjs
  shifts: IAttendanceEnhancedShifts
  jobs: IPositions
  isTwoWeekPayPeriod: boolean
  hasEmployeurDNumbers: boolean
  exportFormat: 'simplified' | 'detailed'
  maxHoursPerWeek: number
}

export const generateExportData = ({
  startAsDate,
  endAsDate,
  employees,
  name,
  startOfPeriod,
  shifts,
  jobs,
  isTwoWeekPayPeriod,
  hasEmployeurDNumbers,
  exportFormat = 'detailed',
  maxHoursPerWeek
}: Props) => {
  let totalHours = 0
  let totalHoursWeek1 = 0
  let totalHoursWeek2 = 0
  let totalAmount = 0

  const week1 = startOfPeriod.format('YYYY-MM-DD')
  const week2 = startOfPeriod.clone().add(1, 'week').format('YYYY-MM-DD')

  employees.forEach(employee => {
    forEach(shifts, (dayEmployees, date) => {
      forEach(dayEmployees[employee.uid], shift => {
        const {
          shiftLengthHours,
          overtimeDuration = 0,
          type = SALARY_TYPE_HOURLY as SalaryRateUnit,
          rate: shiftRate = 0
        } = shift

        const rate = getSalaryRate(
          type,
          Number(shiftRate) || 0,
          maxHoursPerWeek
        )

        const shiftHours =
          type === SALARY_TYPE_YEARLY ? maxHoursPerWeek / 7 : shiftLengthHours
        const regularHours =
          type === SALARY_TYPE_YEARLY
            ? maxHoursPerWeek / 7
            : shiftLengthHours - overtimeDuration

        totalHours += shiftHours
        const startOfWeek = date < week2 ? week1 : week2
        if (startOfWeek === week1) {
          totalHoursWeek1 += shiftHours
        } else {
          totalHoursWeek2 += shiftHours
        }

        totalAmount += regularHours * rate
        if (overtimeDuration) {
          const overtimeRate = rate * 1.5
          totalAmount += overtimeDuration * overtimeRate
        }
      })
    })
  })

  totalAmount = round(totalAmount, 2)

  const s2Header: ExportCell[] = isTwoWeekPayPeriod
    ? [
        {
          value: `${I18n.t('export_attendance.s2')} (${startOfPeriod
            .clone()
            .add(7, 'days')
            .format('MMM DD')}-${startOfPeriod
            .clone()
            .add(13, 'days')
            .format('DD')})`,
          type: 'string'
        }
      ]
    : []

  const s2Hours: ExportCell[] = isTwoWeekPayPeriod
    ? [
        {
          value: `${I18n.t('export_attendance.hours')} - ${
            Math.round(totalHoursWeek2 * 100) / 100
          }`,
          type: 'string'
        }
      ]
    : []

  const positionHeader: ExportCell[] =
    exportFormat !== 'simplified'
      ? [{ value: I18n.t('export_attendance.position'), type: 'string' }]
      : []

  const rateHeader: ExportCell[] =
    exportFormat !== 'simplified'
      ? [{ value: I18n.t('export_attendance.rate'), type: 'string' }]
      : []

  const totalCell: ExportCell[] = isTwoWeekPayPeriod
    ? [
        {
          value: `${I18n.t('export_attendance.total')} - ${
            Math.round(totalHours * 100) / 100
          }`,
          type: 'string'
        }
      ]
    : []

  const data: ExportCell[][] = [
    [{ value: name, type: 'string' }],
    [
      { value: I18n.t('export_attendance.pay_period'), type: 'string' },
      { value: startAsDate, type: 'string' },
      { value: endAsDate, type: 'string' },
      {
        value: `${I18n.t('export_attendance.s1')} (${startOfPeriod.format(
          'MMM DD'
        )}-${startOfPeriod.clone().add(6, 'days').format('DD')})`,
        type: 'string'
      },
      ...s2Header
    ],
    [
      { value: I18n.t('export_attendance.employees'), type: 'string' },
      { value: I18n.t('export_attendance.empl_number'), type: 'string' },
      ...rateHeader,
      {
        value: `${I18n.t('export_attendance.hours')} - ${
          Math.round(totalHoursWeek1 * 100) / 100
        }`,
        type: 'string'
      },
      ...s2Hours,
      ...totalCell,
      {
        value: `${I18n.t('export_attendance.amount')} - ${
          Math.round(totalAmount * 100) / 100
        }`,
        type: 'string'
      },
      ...positionHeader
    ]
  ]

  if (exportFormat === 'simplified') {
    // Simplified format: one line per employee
    employees.forEach(employee => {
      let employeeWeekOneHours = 0
      let employeeWeekTwoHours = 0
      let employeeAmount = 0

      forEach(shifts, (dayEmployees, date) => {
        forEach(dayEmployees[employee.uid], shift => {
          const {
            shiftLengthHours,
            overtimeDuration = 0,
            type = SALARY_TYPE_HOURLY as SalaryRateUnit,
            rate: shiftRate = 0,
            additionalSalary = 0
          } = shift

          const rate = getSalaryRate(
            type,
            Number(shiftRate) || 0,
            maxHoursPerWeek
          )
          const shiftHours =
            type === SALARY_TYPE_YEARLY ? maxHoursPerWeek / 7 : shiftLengthHours

          const startOfWeek = date < week2 ? week1 : week2

          if (startOfWeek === week1) {
            employeeWeekOneHours += shiftHours
          } else {
            employeeWeekTwoHours += shiftHours
          }

          const amountForTheShift = () => {
            let amount = 0
            if (type === SALARY_TYPE_YEARLY) {
              amount += rate * (maxHoursPerWeek / 7)
            } else {
              const regularHours = shiftLengthHours - overtimeDuration
              amount = regularHours * rate
              if (overtimeDuration) {
                const overtimeRate = rate * 1.5
                amount += overtimeDuration * overtimeRate
              }
            }
            if (additionalSalary) {
              amount += parseFloat(additionalSalary as string)
            }
            return amount
          }
          employeeAmount += amountForTheShift()
        })
      })

      totalHours += employeeWeekOneHours
      totalAmount += employeeAmount

      const emplWeekOneHoursCell: ExportCell[] = [
        {
          value: Math.round(employeeWeekOneHours * 100) / 100,
          type: 'number'
        }
      ]

      const emplWeekTwoHoursCell: ExportCell[] = isTwoWeekPayPeriod
        ? [
            {
              value: Math.round(employeeWeekTwoHours * 100) / 100,
              type: 'number'
            }
          ]
        : []

      const totalHoursCell: ExportCell[] = isTwoWeekPayPeriod
        ? [
            {
              value:
                Math.round(
                  (employeeWeekOneHours + employeeWeekTwoHours) * 100
                ) / 100,
              type: 'number'
            }
          ]
        : []

      data.push([
        {
          value: employee.name + ' ' + employee.surname,
          type: 'string'
        },
        {
          value:
            (hasEmployeurDNumbers ? employee.payrollId : employee.customId) ||
            '',
          type: 'string'
        },
        ...emplWeekOneHoursCell,
        ...emplWeekTwoHoursCell,
        ...totalHoursCell,
        {
          value: Math.round(employeeAmount * 100) / 100,
          type: 'number'
        },
        {
          value: '',
          type: 'string'
        }
      ])
    })
  } else {
    // Detailed format
    employees.forEach(employee => {
      const groupedByPosition: {
        [week: string]: {
          [positionId: string]: {
            [rate: string]: number
          }
        }
      } = {}

      // week -> positionId -> rate -> hours

      forEach(shifts, (dayEmployees, date) => {
        forEach(dayEmployees[employee.uid], (shift, shiftKey) => {
          const {
            shiftLengthHours,
            overtimeDuration,
            type = SALARY_TYPE_HOURLY as SalaryRateUnit,
            positionId
          } = shift

          const rate = getSalaryRate(
            type,
            Number(shift.rate) || 0,
            maxHoursPerWeek
          )

          const startOfWeek = date < week2 ? week1 : week2

          if (!groupedByPosition[startOfWeek]) {
            groupedByPosition[startOfWeek] = {}
          }

          if (!groupedByPosition[startOfWeek][positionId]) {
            groupedByPosition[startOfWeek][positionId] = {}
          }

          if (!groupedByPosition[startOfWeek][positionId][rate]) {
            groupedByPosition[startOfWeek][positionId][rate] = 0
          }

          groupedByPosition[startOfWeek][positionId][rate] +=
            type === SALARY_TYPE_YEARLY
              ? maxHoursPerWeek / 7
              : shiftLengthHours - overtimeDuration

          if (overtimeDuration) {
            const overtimeRate = rate * 1.5
            if (!groupedByPosition[startOfWeek][positionId][overtimeRate]) {
              groupedByPosition[startOfWeek][positionId][overtimeRate] = 0
            }

            groupedByPosition[startOfWeek][positionId][overtimeRate] +=
              overtimeDuration
          }
        })
      })

      const uniquePositions = Array.from(
        new Set([
          // Include all positions assigned to the employee (display position even if the employee has no shifts for the week)
          ...(employee.positions?.map(pos => pos.categoryId) || []),
          // Include all positions from shifts
          ...Object.keys(groupedByPosition[week1] || {}),
          ...Object.keys(groupedByPosition[week2] || {})
        ])
      )

      uniquePositions.forEach(positionId => {
        const rates = Array.from(
          new Set([
            ...Object.keys(groupedByPosition[week1]?.[positionId] || {}),
            ...Object.keys(groupedByPosition[week2]?.[positionId] || {})
          ])
        )

        const emptyCell: ExportCell[] = isTwoWeekPayPeriod
          ? [
              {
                value: 0,
                type: 'number'
              }
            ]
          : []

        if (!rates.length) {
          data.push([
            {
              value: employee.name + ' ' + employee.surname,
              type: 'string'
            },
            {
              value:
                (hasEmployeurDNumbers
                  ? employee.payrollId
                  : employee.customId) || '',
              type: 'string'
            },
            {
              value: '',
              type: 'string'
            },
            {
              value: 0,
              type: 'number'
            },
            ...emptyCell,
            {
              value: 0,
              type: 'number'
            },
            {
              value: 0,
              type: 'number'
            },
            {
              value: jobs?.[positionId]?.name || '',
              type: 'string'
            }
          ])
        } else {
          rates.forEach(rate => {
            const week1Hours =
              Math.round(
                (groupedByPosition[week1]?.[positionId]?.[rate] || 0) * 100
              ) / 100
            const week2Hours =
              Math.round(
                (groupedByPosition[week2]?.[positionId]?.[rate] || 0) * 100
              ) / 100

            const s2Hours: ExportCell[] = isTwoWeekPayPeriod
              ? [
                  {
                    value: week2Hours,
                    type: 'number'
                  }
                ]
              : []

            data.push([
              {
                value: employee.name + ' ' + employee.surname,
                type: 'string'
              },
              {
                value:
                  (hasEmployeurDNumbers
                    ? employee.payrollId
                    : employee.customId) || '',
                type: 'string'
              },
              {
                value: round(parseFloat(rate), 2),
                type: 'number'
              },
              {
                value: week1Hours,
                type: 'number'
              },
              ...s2Hours,
              {
                value: Math.round((week1Hours + week2Hours) * 100) / 100,
                type: 'number'
              },
              {
                value:
                  Math.round(
                    (week1Hours + week2Hours) * parseFloat(rate) * 100
                  ) / 100,
                type: 'number'
              },
              {
                value: jobs?.[positionId]?.name || '',
                type: 'string'
              }
            ])
          })
        }
      })
    })
  }

  return data
}
