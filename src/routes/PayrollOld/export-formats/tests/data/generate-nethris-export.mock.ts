export const rawMockData = {
  employees: [
    {
      address: '',
      appliedAt: 1707995461509,
      availabilities: {
        Friday: {
          allDay: true,
          priority: 4
        },
        Monday: {
          allDay: true,
          priority: 0
        },
        Saturday: {
          allDay: true,
          priority: 5
        },
        Sunday: {
          allDay: true,
          priority: 6
        },
        Thursday: {
          allDay: true,
          priority: 3
        },
        Tuesday: {
          allDay: true,
          priority: 1
        },
        Wednesday: {
          allDay: true,
          priority: 2
        },
        maxDaysPerWeek: 5,
        maxHoursInWeek: 40
      },
      avatar:
        'https://firebasestorage.googleapis.com/v0/b/pivot-inc.appspot.com/o/Avatars%2FZpwmiV5jIaO8MByWY3IVU4biAJn1?alt=media&token=488bd839-f879-465c-90fb-c8e72ebb5da2',
      bornDate: 678859200000,
      companyId: '-NpyzByw2JNlmHPJlemx',
      customEmplNumber: '91',
      daysOff: {
        '2024-06-23': {
          reason: 'Going back to Japan for me and my wife’s birthday',
          requestId: '-OL8_z20X0MPy-PLcBux'
        },
        '2024-06-24': {
          reason: 'Going back to Japan for me and my wife’s birthday',
          requestId: '-OL8_z20X0MPy-PLcBuy'
        },
        '2024-06-25': {
          reason: 'Going back to Japan for me and my wife’s birthday',
          requestId: '-OL8_z20X0MPy-PLcBuz'
        },
        '2024-06-26': {
          reason: 'Going back to Japan for me and my wife’s birthday',
          requestId: '-OL8_z20X0MPy-PLcBv-'
        },
        '2024-06-27': {
          reason: 'Going back to Japan for me and my wife’s birthday',
          requestId: '-OL8_z20X0MPy-PLcBv0'
        },
        '2024-06-28': {
          reason: 'Going back to Japan for me and my wife’s birthday',
          requestId: '-OL8_z20X0MPy-PLcBv1'
        },
        '2024-06-29': {
          reason: 'Going back to Japan for me and my wife’s birthday',
          requestId: '-OL8_z20X0MPy-PLcBv2'
        },
        '2024-06-30': {
          reason: 'Going back to Japan for me and my wife’s birthday',
          requestId: '-OL8_z20X0MPy-PLcBv3'
        },
        '2024-07-01': {
          reason: 'Going back to Japan for me and my wife’s birthday',
          requestId: '-OL8_z20X0MPy-PLcBv4'
        },
        '2024-07-02': {
          reason: 'Going back to Japan for me and my wife’s birthday',
          requestId: '-OL8_z20X0MPy-PLcBv5'
        },
        '2024-07-03': {
          reason: 'Going back to Japan for me and my wife’s birthday',
          requestId: '-OL8_z20X0MPy-PLcBv6'
        },
        '2024-07-04': {
          reason: 'Going back to Japan for me and my wife’s birthday',
          requestId: '-OL8_z20X0MPy-PLcBv7'
        },
        '2024-07-05': {
          reason: 'Going back to Japan for me and my wife’s birthday',
          requestId: '-OL8_z20X0MPy-PLcBv8'
        }
      },
      direct: true,
      email: '',
      emergencyContact: '',
      emergencyPhone: '',
      emergencyPhoneCode: '',
      emplNumber: '358',
      hiring: 1709075474244,
      incomplete: false,
      isActive: true,
      isLoading: false,
      lastSeen: 1709009140886,
      name: 'Adam',
      onlineStatus: 'offline',
      phone: '3551693',
      phoneCode: '438',
      positions: [
        {
          categoryId: '-Npz5aRQ6EVN-uC2Tmt_',
          subcategoryId: '-Npz5aRQ6EVN-uC2Tmta'
        }
      ],
      priority: {
        '-Npz5aRQ6EVN-uC2Tmt_': {
          '-Npz5aRQ6EVN-uC2Tmta': 8,
          positionPriority: 8
        }
      },
      surname: 'Omende',
      uid: '-NqgSbSkHRB_FlLljzg3',
      userId: 'ZpwmiV5jIaO8MByWY3IVU4biAJn1',
      veloceEmployeeId: '11eee20a-e0f8-c0af-9fea-0242ac130005',
      status: 'offline',
      customId: '91',
      payrollId: '358',
      veloceId: '11eee20a-e0f8-c0af-9fea-0242ac130005'
    },
    {
      address: '7982 rue st-andré',
      appliedAt: 1737831113140,
      availabilities: {
        Friday: {
          allDay: false,
          priority: 2,
          shift1: {
            start: 660
          }
        },
        Monday: {
          allDay: false,
          priority: 0,
          shift1: {
            start: 960
          }
        },
        Saturday: {
          allDay: false,
          priority: 3,
          shift1: {
            start: 960
          }
        },
        Sunday: {
          allDay: false,
          priority: 4,
          shift1: {
            start: 960
          }
        },
        Wednesday: {
          allDay: false,
          priority: 1,
          shift1: {
            start: 1020
          }
        },
        maxDaysPerWeek: 5,
        maxHoursInWeek: 30
      },
      avatar:
        'https://firebasestorage.googleapis.com/v0/b/pivot-inc.appspot.com/o/Avatars%2FBv2PtIll1lZz4uEV85gUHoHdC7O2?alt=media&token=3e00b0b1-5458-44a0-a798-db84b1c3dde1',
      bornDate: 1104987600000,
      companyId: '-NpyzByw2JNlmHPJlemx',
      customEmplNumber: '205',
      daysOff: {
        '2025-02-13': {
          acceptedBy: '-NpzAUAVI6a39j4rfPui',
          createdAt: 1738372563427,
          date: '2025-02-13',
          reason: 'Funérailles à mon grand-père.',
          requestId: '-OHz435wglSib2TokSsi',
          type: 'allDay'
        },
        '2025-02-14': {
          acceptedBy: '-NpzAUAVI6a39j4rfPui',
          createdAt: 1738372563427,
          date: '2025-02-14',
          reason: 'Funérailles à mon grand-père.',
          requestId: '-OHz435wglSib2TokSsi',
          type: 'allDay'
        },
        '2025-02-15': {
          acceptedBy: '-NpzAUAVI6a39j4rfPui',
          createdAt: 1738372563427,
          date: '2025-02-15',
          reason: 'Funérailles à mon grand-père.',
          requestId: '-OHz435wglSib2TokSsi',
          type: 'allDay'
        },
        '2025-03-21': {
          acceptedBy: '-NpzAUAVI6a39j4rfPui',
          createdAt: 1741640928318,
          customHours: {
            start: 1020
          },
          date: '2025-03-21',
          reason:
            'Chalet en famille, je suis disponible le vendredi matin et le dimanche soir. Merci! ',
          requestId: '-OKwTqIJy5iHJ3zlDTcR',
          type: 'custom'
        },
        '2025-03-22': {
          acceptedBy: '-NpzAUAVI6a39j4rfPui',
          createdAt: 1741640928318,
          date: '2025-03-22',
          reason:
            'Chalet en famille, je suis disponible le vendredi matin et le dimanche soir. Merci! ',
          requestId: '-OKwTqIJy5iHJ3zlDTcR',
          type: 'allDay'
        },
        '2025-03-23': {
          acceptedBy: '-NpzAUAVI6a39j4rfPui',
          createdAt: 1741640928318,
          customHours: {
            end: 960
          },
          date: '2025-03-23',
          reason:
            'Chalet en famille, je suis disponible le vendredi matin et le dimanche soir. Merci! ',
          requestId: '-OKwTqIJy5iHJ3zlDTcR',
          type: 'custom'
        }
      },
      direct: true,
      email: '<EMAIL>',
      emergencyContact: 'sophie st-amand',
      emergencyPhone: '5242295',
      emergencyPhoneCode: '514',
      emplNumber: '487',
      hiring: 1737832496798,
      isActive: true,
      isLoading: false,
      lastSeen: 1737831173707,
      locale: 'fr',
      name: 'Adèle',
      onlineStatus: 'offline',
      phone: '8092752',
      phoneCode: '514',
      positions: [
        {
          categoryId: '-Npz2TiWCgjIOtRNCriZ',
          subcategoryId: '-Npz2TiWCgjIOtRNCri_'
        },
        {
          categoryId: '-Npz2TiWCgjIOtRNCriZ',
          subcategoryId: '-Npz2X_kUFPLeTu_wHyd'
        },
        {
          categoryId: '-Npz2dqj7Ahe13dIcfPC',
          subcategoryId: '-Npz2dqj7Ahe13dIcfPD'
        }
      ],
      priority: {
        '-Npz2TiWCgjIOtRNCriZ': {
          '-Npz2TiWCgjIOtRNCri_': 26,
          '-Npz2X_kUFPLeTu_wHyd': 15,
          positionPriority: 59
        },
        '-Npz2dqj7Ahe13dIcfPC': {
          '-Npz2dqj7Ahe13dIcfPD': 15,
          positionPriority: 18
        }
      },
      replacements: {
        '2025-02-28': {
          day: '2025-02-28'
        },
        '2025-03-15': {
          day: '2025-03-15'
        }
      },
      surname: 'Mercieca',
      uid: '-OHTna58_3MIPkBStAYP',
      userId: 'Bv2PtIll1lZz4uEV85gUHoHdC7O2',
      status: 'offline',
      customId: '205',
      payrollId: '487'
    }
  ],
  shiftsByDay: {
    '2025-02-16': {
      '-OHTna58_3MIPkBStAYP': {
        '-OJFlrQUa5TFH3t6Nm91': {
          additionalSalary: 0,
          clockInSource: 'mobile',
          clockOutSource: 'mobile',
          date: '2025-02-16',
          end: 1397,
          isConflicting: false,
          positionId: '-Npz2TiWCgjIOtRNCriZ',
          rate: 12.6,
          shiftId: 'aa45ec4b-aa44-493a-9d42-f0b0acb38daa',
          start: 1021,
          subpositionId: '-Npz2TiWCgjIOtRNCri_',
          type: 'hourly',
          isConflictingShift: false,
          shiftLengthHours: 6.25,
          shiftEndRounded: 1395,
          shiftStartRounded: 1020,
          shiftPeriods: [],
          isClockInDifferent: false,
          isClockOutDiffrent: false,
          notClockedOut: false,
          isWorking: false,
          isConfirmed: false,
          isStartOverlapping: false,
          isEndOverlapping: false,
          scheduledShift: {
            end: 1380,
            onHold: false,
            start: 1020,
            uid: 'aa45ec4b-aa44-493a-9d42-f0b0acb38daa',
            positionId: '-Npz2TiWCgjIOtRNCriZ'
          },
          overtimeDuration: 0,
          salary: 78.75,
          totalSales: 0,
          cashSales: 0,
          totalDue: 0,
          cutToPay: 0,
          cutToReceive: 0,
          tips: 0,
          cutPercentageToShare: 0,
          weekPeriod: 1,
          paymentSectionCode: '001'
        }
      }
    },
    '2025-02-17': {
      '-NqgSbSkHRB_FlLljzg3': {
        '-OJKi2P33d3YCjhny__w': {
          additionalSalary: 0,
          clockInSource: 'mobile',
          clockOutSource: 'mobile',
          date: '2025-02-17',
          end: 21,
          isConflicting: false,
          positionId: '-Npz5aRQ6EVN-uC2Tmt_',
          rate: 18,
          shiftId: '2d57570f-d393-494e-a702-d6d14af83347',
          start: 962,
          subpositionId: '-Npz5aRQ6EVN-uC2Tmta',
          type: 'hourly',
          isConflictingShift: false,
          shiftLengthHours: 8.25,
          shiftEndRounded: 15,
          shiftStartRounded: 960,
          shiftPeriods: [],
          isClockInDifferent: false,
          isClockOutDiffrent: false,
          notClockedOut: false,
          isWorking: false,
          isConfirmed: false,
          isStartOverlapping: false,
          isEndOverlapping: false,
          scheduledShift: {
            end: 0,
            isRecurrent: true,
            onHold: false,
            start: 960,
            uid: '2d57570f-d393-494e-a702-d6d14af83347',
            positionId: '-Npz5aRQ6EVN-uC2Tmt_'
          },
          overtimeDuration: 0,
          salary: 148.5,
          totalSales: 0,
          cashSales: 0,
          totalDue: 0,
          cutToPay: 0,
          cutToReceive: 0,
          tips: 0,
          cutPercentageToShare: 0,
          weekPeriod: 1,
          paymentSectionCode: '001'
        }
      },
      '-OHTna58_3MIPkBStAYP': {
        '-OJL8eaaAOL-t1h15SWX': {
          additionalSalary: 0,
          clockInSource: 'mobile',
          clockOutSource: 'mobile',
          date: '2025-02-17',
          end: 1410,
          isConflicting: false,
          positionId: '-Npz2TiWCgjIOtRNCriZ',
          rate: 12.6,
          shiftId: '86a296b4-5b76-4e17-b224-daa2ac030173',
          start: 1083,
          subpositionId: '-Npz2TiWCgjIOtRNCri_',
          type: 'hourly',
          isConflictingShift: false,
          shiftLengthHours: 5.5,
          shiftEndRounded: 1410,
          shiftStartRounded: 1080,
          shiftPeriods: [],
          isClockInDifferent: false,
          isClockOutDiffrent: false,
          notClockedOut: false,
          isWorking: false,
          isConfirmed: false,
          isStartOverlapping: false,
          isEndOverlapping: false,
          scheduledShift: {
            end: 0,
            onHold: false,
            start: 1080,
            uid: '86a296b4-5b76-4e17-b224-daa2ac030173',
            positionId: '-Npz2TiWCgjIOtRNCriZ'
          },
          overtimeDuration: 0,
          salary: 69.3,
          totalSales: 0,
          cashSales: 0,
          totalDue: 0,
          cutToPay: 0,
          cutToReceive: 0,
          tips: 0,
          cutPercentageToShare: 0,
          weekPeriod: 1,
          paymentSectionCode: '001'
        }
      }
    },
    '2025-02-18': {
      '-NqgSbSkHRB_FlLljzg3': {
        '-OJOQ41Rt19XBeIhXrga': {
          additionalSalary: 0,
          clockInSource: 'mobile',
          clockOutSource: 'mobile',
          date: '2025-02-18',
          end: 1029,
          isConflicting: false,
          positionId: '-Npz5aRQ6EVN-uC2Tmt_',
          rate: 18,
          shiftId: '683405c2-4a45-48b0-8a0f-d09113dc75c3',
          start: 558,
          subpositionId: '-Npz5aRQ6EVN-uC2Tmta',
          type: 'hourly',
          isConflictingShift: false,
          shiftLengthHours: 8,
          shiftEndRounded: 1035,
          shiftStartRounded: 555,
          shiftPeriods: [],
          isClockInDifferent: false,
          isClockOutDiffrent: false,
          notClockedOut: false,
          isWorking: false,
          isConfirmed: false,
          isStartOverlapping: false,
          isEndOverlapping: false,
          scheduledShift: {
            end: 1020,
            onHold: false,
            start: 540,
            uid: '683405c2-4a45-48b0-8a0f-d09113dc75c3',
            positionId: '-Npz5aRQ6EVN-uC2Tmt_'
          },
          overtimeDuration: 0,
          salary: 144,
          totalSales: 0,
          cashSales: 0,
          totalDue: 0,
          cutToPay: 0,
          cutToReceive: 0,
          tips: 0,
          cutPercentageToShare: 0,
          weekPeriod: 1,
          paymentSectionCode: '001'
        }
      }
    },
    '2025-02-19': {
      '-NqgSbSkHRB_FlLljzg3': {
        '-OJTVqiknhaIs9nBqvpg': {
          additionalSalary: 0,
          clockInSource: 'mobile',
          clockOutSource: 'mobile',
          date: '2025-02-19',
          end: 1028,
          isConflicting: false,
          positionId: '-Npz5aRQ6EVN-uC2Tmt_',
          rate: 18,
          shiftId: 'cab07aea-d939-433e-a740-8ecb00e4d035',
          start: 541,
          subpositionId: '-Npz5aRQ6EVN-uC2Tmta',
          type: 'hourly',
          isConflictingShift: false,
          shiftLengthHours: 8.25,
          shiftEndRounded: 1035,
          shiftStartRounded: 540,
          shiftPeriods: [],
          isClockInDifferent: false,
          isClockOutDiffrent: false,
          notClockedOut: false,
          isWorking: false,
          isConfirmed: false,
          isStartOverlapping: false,
          isEndOverlapping: false,
          scheduledShift: {
            end: 1020,
            onHold: false,
            start: 540,
            uid: 'cab07aea-d939-433e-a740-8ecb00e4d035',
            positionId: '-Npz5aRQ6EVN-uC2Tmt_'
          },
          overtimeDuration: 0,
          salary: 148.5,
          totalSales: 0,
          cashSales: 0,
          totalDue: 0,
          cutToPay: 0,
          cutToReceive: 0,
          tips: 0,
          cutPercentageToShare: 0,
          weekPeriod: 1,
          paymentSectionCode: '001'
        }
      },

      '-OHTna58_3MIPkBStAYP': {
        '-OJVY1ZOpaNg-6hNwFY2': {
          additionalSalary: 0,
          clockInSource: 'mobile',
          clockOutSource: 'mobile',
          date: '2025-02-19',
          end: 1410,
          isConflicting: false,
          positionId: '-Npz2TiWCgjIOtRNCriZ',
          rate: 12.6,
          shiftId: 'e853b458-7a9e-4a36-a5e7-85d785849652',
          start: 1110,
          subpositionId: '-Npz2X_kUFPLeTu_wHyd',
          type: 'hourly',
          isConflictingShift: false,
          shiftLengthHours: 5,
          shiftEndRounded: 1410,
          shiftStartRounded: 1110,
          shiftPeriods: [],
          isClockInDifferent: false,
          isClockOutDiffrent: false,
          notClockedOut: false,
          isWorking: false,
          isConfirmed: false,
          isStartOverlapping: false,
          isEndOverlapping: false,
          scheduledShift: {
            end: 30,
            onHold: false,
            start: 1110,
            uid: 'e853b458-7a9e-4a36-a5e7-85d785849652',
            positionId: '-Npz2TiWCgjIOtRNCriZ'
          },
          overtimeDuration: 0,
          salary: 63,
          totalSales: 0,
          cashSales: 0,
          totalDue: 0,
          cutToPay: 0,
          cutToReceive: 0,
          tips: 0,
          cutPercentageToShare: 0,
          weekPeriod: 1,
          paymentSectionCode: '001'
        }
      }
    },
    '2025-02-20': {
      '-NqgSbSkHRB_FlLljzg3': {
        '-OJYfFhXoJClUWJ9JwJB': {
          additionalSalary: 0,
          clockInSource: 'mobile',
          clockOutSource: 'mobile',
          date: '2025-02-20',
          end: 1033,
          isConflicting: false,
          positionId: '-Npz5aRQ6EVN-uC2Tmt_',
          rate: 18,
          shiftId: '5c5aa4ff-e5f0-4c8b-bc6e-0be9b0442e7c',
          start: 544,
          subpositionId: '-Npz5aRQ6EVN-uC2Tmta',
          type: 'hourly',
          isConflictingShift: false,
          shiftLengthHours: 8.25,
          shiftEndRounded: 1035,
          shiftStartRounded: 540,
          shiftPeriods: [],
          isClockInDifferent: false,
          isClockOutDiffrent: false,
          notClockedOut: false,
          isWorking: false,
          isConfirmed: false,
          isStartOverlapping: false,
          isEndOverlapping: false,
          scheduledShift: {
            end: 1020,
            onHold: false,
            start: 540,
            uid: '5c5aa4ff-e5f0-4c8b-bc6e-0be9b0442e7c',
            positionId: '-Npz5aRQ6EVN-uC2Tmt_'
          },
          overtimeDuration: 0,
          salary: 148.5,
          totalSales: 0,
          cashSales: 0,
          totalDue: 0,
          cutToPay: 0,
          cutToReceive: 0,
          tips: 0,
          cutPercentageToShare: 0,
          weekPeriod: 1,
          paymentSectionCode: '001'
        }
      }
    },
    '2025-02-21': {
      '-NqgSbSkHRB_FlLljzg3': {
        '-OJcoSI6VwIDN8S5Xgn8': {
          additionalSalary: 0,
          clockInSource: 'mobile',
          clockOutSource: 'mobile',
          date: '2025-02-21',
          end: 1028,
          isConflicting: false,
          positionId: '-Npz5aRQ6EVN-uC2Tmt_',
          rate: 18,
          shiftId: '48b5c9ba-ec7c-44ab-a31d-eb0d5a400121',
          start: 543,
          subpositionId: '-Npz5aRQ6EVN-uC2Tmta',
          type: 'hourly',
          isConflictingShift: false,
          shiftLengthHours: 8.25,
          shiftEndRounded: 1035,
          shiftStartRounded: 540,
          shiftPeriods: [],
          isClockInDifferent: false,
          isClockOutDiffrent: false,
          notClockedOut: false,
          isWorking: false,
          isConfirmed: false,
          isStartOverlapping: false,
          isEndOverlapping: false,
          scheduledShift: {
            end: 1020,
            onHold: false,
            start: 540,
            uid: '48b5c9ba-ec7c-44ab-a31d-eb0d5a400121',
            positionId: '-Npz5aRQ6EVN-uC2Tmt_'
          },
          overtimeDuration: 1,
          salary: 157.5,
          totalSales: 0,
          cashSales: 0,
          totalDue: 0,
          cutToPay: 0,
          cutToReceive: 0,
          tips: 0,
          cutPercentageToShare: 0,
          weekPeriod: 1,
          paymentSectionCode: '001'
        }
      },

      '-OHTna58_3MIPkBStAYP': {
        '-OJdEESOv_NN8y6Is5aW': {
          additionalSalary: 0,
          clockInSource: 'mobile',
          clockOutSource: 'mobile',
          date: '2025-02-21',
          end: 843,
          isConflicting: false,
          positionId: '-Npz2TiWCgjIOtRNCriZ',
          rate: 12.6,
          shiftId: 'e798b765-0ca8-404e-ae20-e0cf66355e7c',
          start: 660,
          subpositionId: '-Npz2TiWCgjIOtRNCri_',
          type: 'hourly',
          isConflictingShift: false,
          shiftLengthHours: 3,
          shiftEndRounded: 840,
          shiftStartRounded: 660,
          shiftPeriods: [],
          isClockInDifferent: false,
          isClockOutDiffrent: false,
          notClockedOut: false,
          isWorking: false,
          isConfirmed: false,
          isStartOverlapping: false,
          isEndOverlapping: false,
          scheduledShift: {
            end: 840,
            onHold: false,
            start: 660,
            uid: 'e798b765-0ca8-404e-ae20-e0cf66355e7c',
            positionId: '-Npz2TiWCgjIOtRNCriZ'
          },
          overtimeDuration: 0,
          salary: 37.8,
          totalSales: 0,
          cashSales: 0,
          totalDue: 0,
          cutToPay: 0,
          cutToReceive: 0,
          tips: 0,
          cutPercentageToShare: 0,
          weekPeriod: 1,
          paymentSectionCode: '001'
        }
      }
    },
    '2025-02-22': {},
    '2025-02-23': {
      '-OHTna58_3MIPkBStAYP': {
        '-OJohGbRg8zE0XLyZ4gL': {
          additionalSalary: 0,
          clockInSource: 'mobile',
          clockOutSource: 'mobile',
          date: '2025-02-23',
          end: 1336,
          isConflicting: false,
          positionId: '-Npz2TiWCgjIOtRNCriZ',
          rate: 12.6,
          shiftId: '19fee0dd-a33c-4f66-9a1a-1cd366439023',
          start: 987,
          subpositionId: '-Npz2TiWCgjIOtRNCri_',
          type: 'hourly',
          isConflictingShift: false,
          shiftLengthHours: 5.75,
          shiftEndRounded: 1335,
          shiftStartRounded: 990,
          shiftPeriods: [],
          isClockInDifferent: false,
          isClockOutDiffrent: false,
          notClockedOut: false,
          isWorking: false,
          isConfirmed: false,
          isStartOverlapping: false,
          isEndOverlapping: false,
          scheduledShift: {
            end: 1350,
            onHold: false,
            start: 990,
            uid: '19fee0dd-a33c-4f66-9a1a-1cd366439023',
            positionId: '-Npz2TiWCgjIOtRNCriZ'
          },
          overtimeDuration: 0,
          salary: 72.45,
          totalSales: 0,
          cashSales: 0,
          totalDue: 0,
          cutToPay: 0,
          cutToReceive: 0,
          tips: 0,
          cutPercentageToShare: 0,
          weekPeriod: 2,
          paymentSectionCode: '001'
        }
      }
    },
    '2025-02-24': {
      '-NqgSbSkHRB_FlLljzg3': {
        '-OJrpK8ssJv7B6WnXsHu': {
          additionalSalary: 0,
          clockInSource: 'mobile',
          clockOutSource: 'mobile',
          date: '2025-02-24',
          end: 910,
          isConflicting: false,
          positionId: '-Npz5aRQ6EVN-uC2Tmt_',
          rate: 18,
          shiftId: '4c24b110-fb37-4848-8732-eb3dc93ecb8d',
          start: 421,
          subpositionId: '-Npz5aRQ6EVN-uC2Tmta',
          type: 'hourly',
          isConflictingShift: false,
          shiftLengthHours: 8.25,
          shiftEndRounded: 915,
          shiftStartRounded: 420,
          shiftPeriods: [],
          isClockInDifferent: false,
          isClockOutDiffrent: false,
          notClockedOut: false,
          isWorking: false,
          isConfirmed: false,
          isStartOverlapping: false,
          isEndOverlapping: false,
          scheduledShift: {
            end: 900,
            onHold: false,
            start: 420,
            uid: '4c24b110-fb37-4848-8732-eb3dc93ecb8d',
            positionId: '-Npz5aRQ6EVN-uC2Tmt_'
          },
          overtimeDuration: 0,
          salary: 148.5,
          totalSales: 0,
          cashSales: 0,
          totalDue: 0,
          cutToPay: 0,
          cutToReceive: 0,
          tips: 0,
          cutPercentageToShare: 0,
          weekPeriod: 2,
          paymentSectionCode: '001'
        }
      }
    },
    '2025-02-25': {
      '-NqgSbSkHRB_FlLljzg3': {
        '-OJwzxgmEtlP_9eonwTJ': {
          additionalSalary: 0,
          clockInSource: 'mobile',
          clockOutSource: 'mobile',
          date: '2025-02-25',
          end: 903,
          isConflicting: false,
          positionId: '-Npz5aRQ6EVN-uC2Tmt_',
          rate: 18,
          shiftId: '344117ee-66e9-431a-92b8-c5b5454569e6',
          start: 425,
          subpositionId: '-Npz5aRQ6EVN-uC2Tmta',
          type: 'hourly',
          isConflictingShift: false,
          shiftLengthHours: 8,
          shiftEndRounded: 900,
          shiftStartRounded: 420,
          shiftPeriods: [],
          isClockInDifferent: false,
          isClockOutDiffrent: false,
          notClockedOut: false,
          isWorking: false,
          isConfirmed: false,
          isStartOverlapping: false,
          isEndOverlapping: false,
          scheduledShift: {
            end: 900,
            onHold: false,
            start: 420,
            uid: '344117ee-66e9-431a-92b8-c5b5454569e6',
            positionId: '-Npz5aRQ6EVN-uC2Tmt_'
          },
          overtimeDuration: 0,
          salary: 144,
          totalSales: 0,
          cashSales: 0,
          totalDue: 0,
          cutToPay: 0,
          cutToReceive: 0,
          tips: 0,
          cutPercentageToShare: 0,
          weekPeriod: 2,
          paymentSectionCode: '001'
        }
      }
    },
    '2025-02-26': {
      '-NqgSbSkHRB_FlLljzg3': {
        '-OK1FtxI-09gUoPOom3e': {
          additionalSalary: 0,
          clockInSource: 'mobile',
          clockOutSource: 'mobile',
          date: '2025-02-26',
          end: 920,
          isConflicting: false,
          positionId: '-Npz5aRQ6EVN-uC2Tmt_',
          rate: 18,
          shiftId: 'e81b960e-578c-4268-acc8-42b0ebf57818',
          start: 458,
          subpositionId: '-Npz5aRQ6EVN-uC2Tmta',
          type: 'hourly',
          isConflictingShift: false,
          shiftLengthHours: 7.5,
          shiftEndRounded: 915,
          shiftStartRounded: 465,
          shiftPeriods: [],
          isClockInDifferent: false,
          isClockOutDiffrent: false,
          notClockedOut: false,
          isWorking: false,
          isConfirmed: false,
          isStartOverlapping: false,
          isEndOverlapping: false,
          scheduledShift: {
            end: 900,
            onHold: false,
            start: 420,
            uid: 'e81b960e-578c-4268-acc8-42b0ebf57818',
            positionId: '-Npz5aRQ6EVN-uC2Tmt_'
          },
          overtimeDuration: 0,
          salary: 135,
          totalSales: 0,
          cashSales: 0,
          totalDue: 0,
          cutToPay: 0,
          cutToReceive: 0,
          tips: 0,
          cutPercentageToShare: 0,
          weekPeriod: 2,
          paymentSectionCode: '001'
        }
      },

      '-OHTna58_3MIPkBStAYP': {
        '-OK3Uu8u_IR-JSyaCmW3': {
          additionalSalary: 0,
          clockInSource: 'mobile',
          clockOutSource: 'mobile',
          date: '2025-02-26',
          end: 1366,
          isConflicting: false,
          positionId: '-Npz2TiWCgjIOtRNCriZ',
          rate: 12.6,
          shiftId: '9c13f8a3-4ccc-479f-b815-ea351ef0d061',
          start: 1082,
          subpositionId: '-Npz2TiWCgjIOtRNCri_',
          type: 'hourly',
          isConflictingShift: false,
          shiftLengthHours: 4.75,
          shiftEndRounded: 1365,
          shiftStartRounded: 1080,
          shiftPeriods: [],
          isClockInDifferent: false,
          isClockOutDiffrent: false,
          notClockedOut: false,
          isWorking: false,
          isConfirmed: false,
          isStartOverlapping: false,
          isEndOverlapping: false,
          scheduledShift: {
            end: 0,
            onHold: false,
            start: 1080,
            uid: '9c13f8a3-4ccc-479f-b815-ea351ef0d061',
            positionId: '-Npz2TiWCgjIOtRNCriZ'
          },
          overtimeDuration: 0,
          salary: 59.85,
          totalSales: 0,
          cashSales: 0,
          totalDue: 0,
          cutToPay: 0,
          cutToReceive: 0,
          tips: 0,
          cutPercentageToShare: 0,
          weekPeriod: 2,
          paymentSectionCode: '001'
        }
      }
    },
    '2025-02-27': {
      '-NqgSbSkHRB_FlLljzg3': {
        '-OK6Gu_kjLFx6zKKN9m9': {
          additionalSalary: 0,
          clockInSource: 'mobile',
          clockOutSource: 'mobile',
          date: '2025-02-27',
          end: 922,
          isConflicting: false,
          positionId: '-Npz5aRQ6EVN-uC2Tmt_',
          rate: 18,
          shiftId: '5eec346c-9240-4e8f-9366-9180caeb68c0',
          start: 420,
          subpositionId: '-Npz5aRQ6EVN-uC2Tmta',
          type: 'hourly',
          isConflictingShift: false,
          shiftLengthHours: 8.25,
          shiftEndRounded: 915,
          shiftStartRounded: 420,
          shiftPeriods: [],
          isClockInDifferent: false,
          isClockOutDiffrent: false,
          notClockedOut: false,
          isWorking: false,
          isConfirmed: false,
          isStartOverlapping: false,
          isEndOverlapping: false,
          scheduledShift: {
            end: 900,
            onHold: false,
            start: 420,
            uid: '5eec346c-9240-4e8f-9366-9180caeb68c0',
            positionId: '-Npz5aRQ6EVN-uC2Tmt_'
          },
          overtimeDuration: 0,
          salary: 148.5,
          totalSales: 0,
          cashSales: 0,
          totalDue: 0,
          cutToPay: 0,
          cutToReceive: 0,
          tips: 0,
          cutPercentageToShare: 0,
          weekPeriod: 2,
          paymentSectionCode: '001'
        }
      }
    },
    '2025-02-28': {
      '-NqgSbSkHRB_FlLljzg3': {
        '-OKBPga-D38c9GcDTpSL': {
          additionalSalary: 0,
          clockInSource: 'mobile',
          clockOutSource: 'mobile',
          date: '2025-02-28',
          end: 921,
          isConflicting: false,
          positionId: '-Npz5aRQ6EVN-uC2Tmt_',
          rate: 18,
          shiftId: '83c33899-2b3d-4d1f-8859-effd437b0a2f',
          start: 417,
          subpositionId: '-Npz5aRQ6EVN-uC2Tmta',
          type: 'hourly',
          isConflictingShift: false,
          shiftLengthHours: 8.25,
          shiftEndRounded: 915,
          shiftStartRounded: 420,
          shiftPeriods: [],
          isClockInDifferent: false,
          isClockOutDiffrent: false,
          notClockedOut: false,
          isWorking: false,
          isConfirmed: false,
          isStartOverlapping: false,
          isEndOverlapping: false,
          scheduledShift: {
            end: 900,
            onHold: false,
            start: 420,
            uid: '83c33899-2b3d-4d1f-8859-effd437b0a2f',
            positionId: '-Npz5aRQ6EVN-uC2Tmt_'
          },
          overtimeDuration: 0.25,
          salary: 150.75,
          totalSales: 0,
          cashSales: 0,
          totalDue: 0,
          cutToPay: 0,
          cutToReceive: 0,
          tips: 0,
          cutPercentageToShare: 0,
          weekPeriod: 2,
          paymentSectionCode: '001'
        }
      }
    },
    '2025-03-01': {
      '-OHTna58_3MIPkBStAYP': {
        '-OKIcPnGRCo0LoJ_T_7M': {
          additionalSalary: 0,
          clockInSource: 'mobile',
          clockOutSource: 'mobile',
          date: '2025-03-01',
          end: 30,
          isConfirmed: true,
          isConflicting: true,
          positionId: '-Npz2TiWCgjIOtRNCriZ',
          rate: 12.6,
          shiftId: 'b6cf835b-6670-4ea0-ad3b-6a36589bf9e5',
          start: 1110,
          subpositionId: '-Npz2X_kUFPLeTu_wHyd',
          type: 'hourly',
          isConflictingShift: false,
          shiftLengthHours: 6,
          shiftEndRounded: 30,
          shiftStartRounded: 1110,
          shiftPeriods: [],
          isClockInDifferent: false,
          isClockOutDiffrent: false,
          notClockedOut: false,
          isWorking: false,
          isStartOverlapping: false,
          isEndOverlapping: false,
          scheduledShift: {
            end: 30,
            onHold: false,
            start: 1110,
            uid: 'b6cf835b-6670-4ea0-ad3b-6a36589bf9e5',
            positionId: '-Npz2TiWCgjIOtRNCriZ'
          },
          overtimeDuration: 0,
          salary: 75.6,
          totalSales: 0,
          cashSales: 0,
          totalDue: 0,
          cutToPay: 0,
          cutToReceive: 0,
          tips: 0,
          cutPercentageToShare: 0,
          weekPeriod: 2,
          paymentSectionCode: '001'
        }
      }
    }
  },
  startAsDate: '2025-02-16',
  positionSettings: {
    '-NpyzVn03DO9WNfp2PaS': {
      salaryExport: '001'
    },
    '-Npz13rxEiQ3U4pj4x3R': {
      salaryExport: '001'
    },
    '-Npz1tlFK3OX4H62jocS': {
      salaryExport: '001'
    },
    '-Npz256bV0CTbNuNrcr1': {
      salaryExport: '001'
    },
    '-Npz2TiWCgjIOtRNCriZ': {
      salaryExport: '001'
    },
    '-Npz2Zaol3HdmlNSM-Zs': {
      salaryExport: '001'
    },
    '-Npz2dqj7Ahe13dIcfPC': {
      salaryExport: '001'
    },
    '-Npz2i_Lw6ORvCmGkd_G': {
      salaryExport: '200'
    },
    '-Npz2uScvu8SncBWUQ9N': {
      salaryExport: '200'
    },
    '-Npz4JVw0XqwVebXnwih': {
      salaryExport: '200'
    },
    '-Npz4zD1cEREikzDqrVH': {
      salaryExport: '200'
    },
    '-Npz5YX2TViS67P1UuZ1': {
      cutFormat: '',
      percentage: '',
      salaryExport: '200',
      salaryType: 'wage'
    },
    '-Npz5aRQ6EVN-uC2Tmt_': {
      salaryExport: '001'
    },
    '-Npz5gIXQji1bVzBmZCT': {
      salaryExport: '001'
    },
    '-Nu16PYzy8oSeqYSd9ec': {
      salaryExport: '001'
    },
    '-Nvs8pDLJFHd_VScbnmY': {
      salaryExport: '001'
    }
  },
  CO_NUMBER: '00239100',
  overtimeSection: '043',
  endAsDate: '2025-03-01',
  isTwoWeekPayPeriod: true,
  companyId: '-NpyzByw2JNlmHPJlemx'
}
