import dayjs from 'dayjs'
import isBetween from 'dayjs/plugin/isBetween'

import {
  NethrisEmployeesShifts,
  generateExcelContentForClassicRestaurants,
  generateNethrisExport,
  generateNethrisExportProps,
  positionsCodesWithoutRateAndQuantity
} from '../generateNethrisExport'
import { mockDataFormatted } from './data/generate-export-classic.mock'
import { rawMockData } from './data/generate-nethris-export.mock'
import { SalaryRateUnit } from '../../../../utils/constants'
import { IEmployee } from '../../../../types/employee'

dayjs.extend(isBetween) // mandatory for the tests

describe('generateNethrisExport', () => {
  describe('generateExcelContentForClassicRestaurants', () => {
    it('should generate correct excel content for classic restaurants', () => {
      // Prepare test data
      const employeesShifts: NethrisEmployeesShifts =
        mockDataFormatted.employeesShifts as unknown as NethrisEmployeesShifts
      const CO_NUMBER = mockDataFormatted.CO_NUMBER
      const endAsDate = mockDataFormatted.endAsDate
      const positionSettingsFlat = new Set(
        mockDataFormatted.positionSettingsFlat
      )
      const isTwoWeekPayPeriod = mockDataFormatted.isTwoWeekPayPeriod
      const overtimeSection = mockDataFormatted.overtimeSection
      const maxHoursPerWeek = mockDataFormatted.maxHoursPerWeek

      // Execute function
      const result = generateExcelContentForClassicRestaurants(
        employeesShifts,
        CO_NUMBER,
        endAsDate,
        positionSettingsFlat,
        isTwoWeekPayPeriod,
        overtimeSection,
        maxHoursPerWeek
      )

      const empIdWithHourlyRate = '-NqgSbSkHRB_FlLljzg3'
      const empMockData = mockDataFormatted.employeesShifts[empIdWithHourlyRate]

      // Assertions
      expect(result).toHaveLength(4)
      // Field 0: Company number
      expect(result[0][0].value).toBe(CO_NUMBER)
      // Field 1: Employee payroll ID
      expect(result[0][1].value).toBe(empMockData.infos.payrollId)
      // Field 2: Employee name (surname, name)
      expect(result[0][2].value).toBe(
        `${empMockData.infos.surname}, ${empMockData.infos.name}`
      )
      // Field 3: Department code
      expect(result[0][3].value).toBe('0')
      // Field 4: End date
      expect(result[0][4].value).toBe(endAsDate)

      // Position data fields for first week
      // Field 5: Week index
      expect(result[0][5].value).toBe('1')

      const firstWeekShift = Object.values(empMockData.pay.firstWeek)[0]!

      const expectedHours = (
        firstWeekShift.regularHours / 60 -
        firstWeekShift.overtimeDuration
      ).toFixed(2)
      // Field 6: Hours worked
      expect(result[0][6].value).toBe(expectedHours)
      // Field 7: Hourly rate
      expect(result[0][7].value).toBe(firstWeekShift.rate.toFixed(2))

      // Overtime data fields for first week
      let overtimeIndex = 5 // Start after the first 5 columns
      
      // Count columns for each position code in positionSettingsFlat
      for (const posCode of [...positionSettingsFlat]) {
        // Add columns based on whether the position needs quantity or not
        const columnIncrement = positionsCodesWithoutRateAndQuantity.includes(posCode) ? 2 : 3;
        overtimeIndex += columnIncrement;
      }

      // Overtime field: Overtime duration
      expect(result[0][overtimeIndex + 1].value).toBe(
        firstWeekShift.overtimeDuration.toString()
      )
      // Overtime field: Hourly rate
      expect(result[0][overtimeIndex + 2].value).toBe(
        firstWeekShift.rate.toFixed(2)
      )

      // Second week row fields
      // Field 0: Company number
      expect(result[1][0].value).toBe(CO_NUMBER)
      // Field 1: Employee payroll ID
      expect(result[1][1].value).toBe(empMockData.infos.payrollId)
      // Field 2: Employee name (surname, name)
      expect(result[1][2].value).toBe(
        `${empMockData.infos.surname}, ${empMockData.infos.name}`
      )
      // Field 3: Department code
      expect(result[1][3].value).toBe('0')
      // Field 4: End date
      expect(result[1][4].value).toBe(endAsDate)

      // Position data fields for second week
      // Field 5: Week index
      expect(result[1][5].value).toBe('2')

      const secondWeekShift = Object.values(empMockData.pay.secondWeek)[0]!

      const expectedHoursSecondWeek = (
        secondWeekShift.regularHours / 60 -
        secondWeekShift.overtimeDuration
      ).toFixed(2)

      // Field 6: Hours worked
      expect(result[1][6].value).toBe(expectedHoursSecondWeek)
      // Field 7: Hourly rate
      expect(result[1][7].value).toBe(secondWeekShift.rate.toFixed(2))
    })

    it('should handle yearly salary type correctly', () => {
      const result = generateExcelContentForClassicRestaurants(
        mockDataFormatted.employeesShifts as unknown as NethrisEmployeesShifts,
        mockDataFormatted.CO_NUMBER,
        mockDataFormatted.endAsDate,
        mockDataFormatted.positionSettingsFlat,
        mockDataFormatted.isTwoWeekPayPeriod,
        mockDataFormatted.overtimeSection,
        mockDataFormatted.maxHoursPerWeek
      )
      const emplIdWithYearlySalary = '-OHTna58_3MIPkBStAYP'
      const empMockData =
        mockDataFormatted.employeesShifts[emplIdWithYearlySalary]

      // Assertions
      expect(result).toHaveLength(4)

      const firstWeekShifts = Object.values(empMockData.pay.firstWeek)

      // Position data fields for yearly salary
      // Field 6: Hours (fixed at 40 for yearly salary)
      expect(result[2][6].value).toBe('40')

      // Field 7: Hourly rate (calculated from yearly salary)
      expect(result[2][7].value).toBe(
        (firstWeekShifts[0].rate / 52 / 40).toFixed(2)
      )
    })

    it('should handle multiple rates for the same employee in a week', () => {
      const mockEmployee: Partial<IEmployee> = {
        uid: 'employee-123',
        name: 'Jane',
        surname: 'Doe',
        payrollId: '12345',
        companyId: 'test-company',
        userId: 'user-123',
        email: '<EMAIL>',
        phone: '*********',
        phoneCode: '+1'
      }

      const employeeWithMultipleRates: NethrisEmployeesShifts = {
        'employee-123': {
          infos: mockEmployee as IEmployee,
          shifts: {
            firstWeek: {},
            secondWeek: {}
          },
          pay: {
            firstWeek: {},
            secondWeek: {
              'position1_rate1': {
                regularHours: 600,
                overtimeDuration: 0,
                rate: 13,
                date: '2023-05-01',
                additionalSalary: 0,
                type: SalaryRateUnit.SALARY_TYPE_HOURLY,
                paymentSectionCode: '1',
                numberOfShifts: 1
              },
              'position1_rate2': {
                regularHours: 600,
                overtimeDuration: 0,
                rate: 13.5,
                date: '2023-05-03',
                additionalSalary: 0,
                type: SalaryRateUnit.SALARY_TYPE_HOURLY,
                paymentSectionCode: '1',
                numberOfShifts: 1
              }
            }
          }
        }
      }

      const positionSettingsFlat = new Set(['1'])
      const CO_NUMBER = 'TEST123'
      const endAsDate = '2023-05-07'
      const isTwoWeekPayPeriod = true
      const overtimeSection = '232'
      const maxHoursPerWeek = 40

      // Call the function
      const result = generateExcelContentForClassicRestaurants(
        employeeWithMultipleRates,
        CO_NUMBER,
        endAsDate,
        positionSettingsFlat,
        isTwoWeekPayPeriod,
        overtimeSection,
        maxHoursPerWeek
      )

      // We should have two rows - one for each rate
      expect(result).toHaveLength(2)

      // Verify first rate row
      expect(result[0][0].value).toBe(CO_NUMBER)
      expect(result[0][1].value).toBe('12345')
      expect(result[0][2].value).toBe('Doe, Jane')
      expect(result[0][5].value).toBe('2')
      expect(result[0][6].value).toBe('10.00')
      expect(result[0][7].value).toBe('13.00')

      // Verify second rate row
      expect(result[1][0].value).toBe(CO_NUMBER)
      expect(result[1][1].value).toBe('12345')
      expect(result[1][2].value).toBe('Doe, Jane')
      expect(result[1][5].value).toBe('2')
      expect(result[1][6].value).toBe('10.00')
      expect(result[1][7].value).toBe('13.50')
    })
  })

  describe('generateNethrisExport', () => {
    it('should generate the correct export data', () => {
      const result = generateNethrisExport(
        rawMockData as unknown as generateNethrisExportProps
      )
      expect(result).toBeDefined()
      // Add more specific assertions here
    })

    describe('match snapshot', () => {
      it('should match snapshot', () => {
        const result = generateNethrisExport(
          rawMockData as unknown as generateNethrisExportProps
        )
        expect(result).toMatchSnapshot()
      })
    })
  })
})
