// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`generateNethrisExport generateNethrisExport match snapshot should match snapshot 1`] = `
[
  [
    {
      "type": "string",
      "value": "company",
    },
    {
      "type": "string",
      "value": "employee_no",
    },
    {
      "type": "string",
      "value": "last_name, first_name",
    },
    {
      "type": "string",
      "value": "record_no",
    },
    {
      "type": "Date",
      "value": "period",
    },
    {
      "type": "string",
      "value": "G001(week)",
    },
    {
      "type": "string",
      "value": "G001(quantity)",
    },
    {
      "type": "string",
      "value": "G001(rate)",
    },
    {
      "type": "string",
      "value": "G200(week)",
    },
    {
      "type": "string",
      "value": "G200(amount)",
    },
    {
      "type": "string",
      "value": "G043(week)",
    },
    {
      "type": "string",
      "value": "G043(quantity)",
    },
    {
      "type": "string",
      "value": "G043(rate)",
    },
    {
      "type": "string",
      "value": "G21(quantity)",
    },
    {
      "type": "string",
      "value": "G21(rate)",
    },
    {
      "type": "string",
      "value": "G517(amount)",
    },
    {
      "type": "string",
      "value": "G211(week)",
    },
    {
      "type": "string",
      "value": "G211(amount)",
    },
    {
      "type": "string",
      "value": "G809(week)",
    },
    {
      "type": "string",
      "value": "G809(amount)",
    },
    {
      "type": "string",
      "value": "G212(week)",
    },
    {
      "type": "string",
      "value": "G212(amount)",
    },
  ],
  [
    {
      "type": "string",
      "value": "00239100",
    },
    {
      "type": "string",
      "value": "358",
    },
    {
      "type": "string",
      "value": "Omende, Adam",
    },
    {
      "type": "string",
      "value": "0",
    },
    {
      "type": "string",
      "value": "2025-03-01",
    },
    {
      "type": "string",
      "value": "1",
    },
    {
      "type": "string",
      "value": "40.00",
    },
    {
      "type": "string",
      "value": "18.00",
    },
    {
      "type": "string",
      "value": "",
    },
    {
      "type": "string",
      "value": "",
    },
    {
      "type": "string",
      "value": "1",
    },
    {
      "type": "string",
      "value": "1",
    },
    {
      "type": "string",
      "value": "18.00",
    },
  ],
  [
    {
      "type": "string",
      "value": "00239100",
    },
    {
      "type": "string",
      "value": "358",
    },
    {
      "type": "string",
      "value": "Omende, Adam",
    },
    {
      "type": "string",
      "value": "0",
    },
    {
      "type": "string",
      "value": "2025-03-01",
    },
    {
      "type": "string",
      "value": "2",
    },
    {
      "type": "string",
      "value": "40.00",
    },
    {
      "type": "string",
      "value": "18.00",
    },
    {
      "type": "string",
      "value": "",
    },
    {
      "type": "string",
      "value": "",
    },
    {
      "type": "string",
      "value": "2",
    },
    {
      "type": "string",
      "value": "0.25",
    },
    {
      "type": "string",
      "value": "18.00",
    },
  ],
  [
    {
      "type": "string",
      "value": "00239100",
    },
    {
      "type": "string",
      "value": "487",
    },
    {
      "type": "string",
      "value": "Mercieca, Adèle",
    },
    {
      "type": "string",
      "value": "0",
    },
    {
      "type": "string",
      "value": "2025-03-01",
    },
    {
      "type": "string",
      "value": "1",
    },
    {
      "type": "string",
      "value": "19.75",
    },
    {
      "type": "string",
      "value": "12.60",
    },
    {
      "type": "string",
      "value": "",
    },
    {
      "type": "string",
      "value": "",
    },
    {
      "type": "string",
      "value": "",
    },
    {
      "type": "string",
      "value": "",
    },
    {
      "type": "string",
      "value": "",
    },
  ],
  [
    {
      "type": "string",
      "value": "00239100",
    },
    {
      "type": "string",
      "value": "487",
    },
    {
      "type": "string",
      "value": "Mercieca, Adèle",
    },
    {
      "type": "string",
      "value": "0",
    },
    {
      "type": "string",
      "value": "2025-03-01",
    },
    {
      "type": "string",
      "value": "2",
    },
    {
      "type": "string",
      "value": "16.50",
    },
    {
      "type": "string",
      "value": "12.60",
    },
    {
      "type": "string",
      "value": "",
    },
    {
      "type": "string",
      "value": "",
    },
    {
      "type": "string",
      "value": "",
    },
    {
      "type": "string",
      "value": "",
    },
    {
      "type": "string",
      "value": "",
    },
  ],
]
`;
