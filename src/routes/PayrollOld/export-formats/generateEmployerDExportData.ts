import { Dayjs } from 'dayjs'
import _, { round } from 'lodash'

import { getCutAmount } from '../payrollUtils'

import { SALARY_TYPE_HOURLY, SALARY_TYPE_YEARLY } from 'utils/constants'

import {
  IAttendanceEnhancedShifts as AttendanceEnhancedShifts,
  AttendanceSettings
} from 'types/attendance'
import { IEmployee as Employee } from 'types/employee'

const addWhiteSpaces = (
  str: string | number,
  requiredLength: number,
  side = 'right'
) => {
  let strFormatted =
    typeof str === 'string'
      ? str
      : typeof str === 'number'
        ? str.toString()
        : ''

  if (strFormatted.length > requiredLength) {
    throw new Error(
      `String length is greater than required length. String: ${strFormatted}, required length: ${requiredLength}`
    )
  }
  const whiteSpaces = ' '.repeat(requiredLength - strFormatted.length)
  const isOnTheLeft = side === 'left'
  return isOnTheLeft
    ? `${whiteSpaces}${strFormatted}`
    : `${strFormatted}${whiteSpaces}`
}

const getWhiteSpaces = (requiredLength: number) => {
  return ' '.repeat(requiredLength)
}

interface GenerateEmployerDExportDataParams {
  employees: Employee[]
  startOfPeriod: Dayjs
  CO_NUMBER: string
  attendanceDataEnhanced: AttendanceEnhancedShifts
  salesPerShift: any
  weeklyData: any
  attendanceSettings: AttendanceSettings
  cutDistribution: any
  dailyCuts: any
  cutGroups: any
  cutRoundingValue: any
  cutRoundingType: any
  endAsDate: string
  maxHoursPerWeek: number
}

export const generateEmployerDExportData = ({
  employees,
  startOfPeriod,
  CO_NUMBER,
  attendanceDataEnhanced,
  salesPerShift,
  weeklyData,
  attendanceSettings,
  cutDistribution,
  dailyCuts,
  cutGroups,
  cutRoundingValue,
  cutRoundingType,
  endAsDate,
  maxHoursPerWeek
}: GenerateEmployerDExportDataParams) => {
  const {
    positionSettings,
    tipsDeclaration = 0,
    cutExport,
    declarationExport,
    overtimeExport,
    cutPercentage
  } = attendanceSettings

  const cutToPayPercentage = +cutPercentage || 0

  const statementNumber = 0

  const data: string[] = []

  const week1 = startOfPeriod.format('YYYY-MM-DD')
  const week2 = startOfPeriod.clone().add(1, 'week').format('YYYY-MM-DD')

  employees.forEach((employee, i) => {
    const groupedByRate: {
      [key: string]: {
        [key: string]: number
      }
    } = {}
    const groupedByRateTotal: {
      [key: string]: {
        [key: string]: number
      }
    } = {}
    const weeklyTips: {
      [key: string]: number
    } = {}
    const weeklyCutToReceive: {
      [key: string]: number
    } = {}
    const weeklySales: {
      [key: string]: number
    } = {}
    const weeklyBonuses: {
      [key: string]: number
    } = {}
    const weeklyOvertime: {
      [key: string]: {
        [key: string]: number
      }
    } = {}
    const overtimeTotal: {
      [key: string]: number
    } = {}

    const emplNumber = employee.payrollId || 'NO_EMPL_#'

    _.forEach(attendanceDataEnhanced, (dayEmployees, date) => {
      _.forEach(dayEmployees[employee.uid], (shift, shiftKey) => {
        const {
          shiftLengthHours: shiftLengthHoursRaw,
          overtimeDuration: overtimeDurationRaw,
          additionalSalary = 0,
          shiftPeriods = [],
          type = SALARY_TYPE_HOURLY,
          rate: shiftRate = 0
        } = shift
        const rate = round(
          type === SALARY_TYPE_YEARLY
            ? Number(shiftRate) / 52 / maxHoursPerWeek
            : Number(shiftRate),
          2
        )
        const shiftLengthHours =
          type === SALARY_TYPE_YEARLY
            ? maxHoursPerWeek / 7
            : shiftLengthHoursRaw

        const overtimeDuration =
          type === SALARY_TYPE_YEARLY ? 0 : overtimeDurationRaw || 0

        const startOfWeek = date < week2 ? week1 : week2

        const { salaryType = 'wage' } = positionSettings[shift.positionId] || {}

        let tips = 0
        let cutToPay = 0

        if (salaryType === 'wage_tips') {
          tips = salesPerShift[date]?.[employee.uid]?.[shiftKey]?.tips || 0
          cutToPay = tips
            ? round(
                (cutToPayPercentage / 100) *
                  (salesPerShift[date]?.[employee.uid]?.[shiftKey]?.sales || 0),
                2
              )
            : 0

          if (!weeklySales[startOfWeek]) {
            weeklySales[startOfWeek] = 0
          }
          weeklySales[startOfWeek] +=
            salesPerShift[date]?.[employee.uid]?.[shiftKey]?.sales || 0
        }

        let cutToReceive = 0

        if (salaryType === 'wage_cut') {
          shiftPeriods.forEach(period => {
            cutToReceive += getCutAmount(cutDistribution, {
              weeklyData,
              dailyCuts,
              startOfWeek,
              date,
              positionId: shift.positionId,
              shiftLengthHours: period.overlapHours,
              cutGroups,
              positionSettings,
              cutRoundingValue,
              cutRoundingType,
              period: period.key
            })
          })
        }

        if (!groupedByRate[startOfWeek]) {
          groupedByRate[startOfWeek] = {}
        }

        if (!groupedByRate[startOfWeek][rate]) {
          groupedByRate[startOfWeek][rate] = 0
        }

        if (!groupedByRateTotal[shift.positionId]) {
          groupedByRateTotal[shift.positionId] = {}
        }

        if (!groupedByRateTotal[shift.positionId][rate]) {
          groupedByRateTotal[shift.positionId][rate] = 0
        }

        groupedByRateTotal[shift.positionId][rate] +=
          shiftLengthHours - overtimeDuration

        groupedByRate[startOfWeek][rate] += shiftLengthHours - overtimeDuration
        if (overtimeDuration) {
          // const overtimeRate = Math.round(rate * 1.5 * 100) / 100
          // if (!groupedByRate[startOfWeek][overtimeRate]) {
          //   groupedByRate[startOfWeek][overtimeRate] = 0
          // }
          // groupedByRate[startOfWeek][overtimeRate] += overtimeDuration

          if (!overtimeTotal[rate]) {
            overtimeTotal[rate] = 0
          }

          overtimeTotal[rate] += overtimeDuration

          if (!weeklyOvertime[startOfWeek]) {
            weeklyOvertime[startOfWeek] = {}
          }
          if (!weeklyOvertime[startOfWeek][rate]) {
            weeklyOvertime[startOfWeek][rate] = 0
          }
          weeklyOvertime[startOfWeek][rate] += overtimeDuration
        }

        // -NMA6bcJXkTZkd9spP0N
        if (salaryType === 'wage_tips') {
          if (!weeklyTips[startOfWeek]) {
            weeklyTips[startOfWeek] = 0
          }
          weeklyTips[startOfWeek] += tips - cutToPay
        }

        if (salaryType === 'wage_cut') {
          if (!weeklyCutToReceive[startOfWeek]) {
            weeklyCutToReceive[startOfWeek] = 0
          }
          weeklyCutToReceive[startOfWeek] += cutToReceive
        }

        if (!weeklyBonuses[startOfWeek]) {
          weeklyBonuses[startOfWeek] = 0
        }

        weeklyBonuses[startOfWeek] +=
          (additionalSalary && +additionalSalary) || 0
      })
    })

    // weeks.forEach((week, i) => {
    // const date = dayjs(week, 'YYYY-MM-DD')
    //   .add(6, 'days')
    //   .format('YYYY-MM-DD') // use the end date of the current period
    // const weekNumber = i + 1

    // Object.keys(groupedByRate[week] || {}).forEach(rate => {
    //   const hours = Math.round((groupedByRate[week][rate] || 0) * 100) / 100
    //   const amount = Math.round(hours * rate * 100) / 100
    Object.entries(groupedByRateTotal).forEach(([positionId, rates]) => {
      Object.entries(rates).forEach(([rate, hoursRaw]) => {
        const hours = round(hoursRaw || 0, 2)
        const amount = round(hours * Number(rate), 2)
        const { salaryExport = '' } = positionSettings[positionId] || {}

        data.push(
          [
            addWhiteSpaces(CO_NUMBER, 8), //Company 1-8
            getWhiteSpaces(1),
            addWhiteSpaces(emplNumber, 9), //  employee number 10-18
            getWhiteSpaces(1),
            addWhiteSpaces(statementNumber, 2),
            addWhiteSpaces('G' + salaryExport, 4), // Statement number + Transaction type + Earning code 20-25
            getWhiteSpaces(4),
            addWhiteSpaces(hours, 8), // Quantity 30-37
            getWhiteSpaces(2),
            addWhiteSpaces(rate, 8), // Rate 40-47
            getWhiteSpaces(3),
            addWhiteSpaces(amount, 9), // Amount 51-59
            getWhiteSpaces(2),
            addWhiteSpaces(0, 1), // week number 1 or 2 62
            getWhiteSpaces(28),
            endAsDate // date 91-100
          ].join('')
        )
      })
    })

    const totalCut = Object.values(weeklyCutToReceive).reduce(
      (acc, val) => acc + val,
      0
    )

    const cut = round(totalCut, 2)
    // let cut = Math.round((weeklyCutToReceive[week] || 0) * 100) / 100

    const totalSales = Object.values(weeklySales).reduce(
      (acc, val) => acc + val,
      0
    )
    // const weeklySalesAmount = weeklySales[week] || 0
    const weeklySalesAmount = totalSales

    const totalBonus = Object.values(weeklyBonuses).reduce(
      (acc, val) => acc + val,
      0
    )
    // const weeklyBonus = weeklyBonuses[week] || 0
    const weeklyBonus = totalBonus

    // get all sales * decl percentage
    // TODO: check sales that used used if there are
    let declaration = round(
      weeklySalesAmount * (Number(tipsDeclaration) / 100),
      2
    )

    // if (tips > 0) {
    //   data.push(
    //     [
    //       addWhiteSpaces(CO_NUMBER, 8), //Company 1-8
    //       getWhiteSpaces(1),
    //       addWhiteSpaces(emplNumber, 9), //  employee number 10-18
    //       getWhiteSpaces(1),
    //       addWhiteSpaces(statementNumber, 2),
    //       'G' + tipsExport, // Statement number + Transaction type + Earning code 20-25
    //       getWhiteSpaces(4),
    //       addWhiteSpaces('', 8), // Quantity 30-37
    //       getWhiteSpaces(2),
    //       addWhiteSpaces('', 8), // Rate 40-47
    //       getWhiteSpaces(3),
    //       addWhiteSpaces(tips, 9), // Amount 51-59
    //       getWhiteSpaces(2),
    //       weekNumber, // week number 1 or 2 62
    //       getWhiteSpaces(29),
    //       date // date 91-100
    //     ].join('')
    //   )
    // }

    // if (weeklyOvertime[week]) {
    // Object.keys(weeklyOvertime[week]).forEach(rate => {
    //   const hours = Math.round((weeklyOvertime[week][rate] || 0) * 100) / 100
    //   const amount = Math.round(hours * rate * 100) / 100
    Object.keys(overtimeTotal).forEach(rate => {
      const hours = round(overtimeTotal[rate] || 0, 2)
      const amount = round(hours * Number(rate), 2)

      data.push(
        [
          addWhiteSpaces(CO_NUMBER, 8), //Company 1-8
          getWhiteSpaces(1),
          addWhiteSpaces(emplNumber, 9), //  employee number 10-18
          getWhiteSpaces(1),
          addWhiteSpaces(statementNumber, 2),
          addWhiteSpaces('G' + overtimeExport, 4), // Statement number + Transaction type + Earning code 20-25
          getWhiteSpaces(4),
          addWhiteSpaces(hours, 8), // Quantity 30-37
          getWhiteSpaces(2),
          addWhiteSpaces(rate, 8), // Rate 40-47
          getWhiteSpaces(3),
          addWhiteSpaces(amount, 9), // Amount 51-59
          getWhiteSpaces(2),
          addWhiteSpaces(0, 1), // week number 1 or 2 62
          getWhiteSpaces(28),
          endAsDate // date 91-100
        ].join('')
      )
    })
    // }

    if (cut > 0) {
      data.push(
        [
          addWhiteSpaces(CO_NUMBER, 8), //Company 1-8
          getWhiteSpaces(1),
          addWhiteSpaces(emplNumber, 9), //  employee number 10-18
          getWhiteSpaces(1),
          addWhiteSpaces(statementNumber, 2),
          addWhiteSpaces('G' + cutExport, 4), // Statement number + Transaction type + Earning code 20-25
          getWhiteSpaces(4),
          addWhiteSpaces('', 8), // Quantity 30-37
          getWhiteSpaces(2),
          addWhiteSpaces('', 8), // Rate 40-47
          getWhiteSpaces(3),
          addWhiteSpaces(cut, 9), // Amount 51-59
          getWhiteSpaces(2),
          addWhiteSpaces(0, 2), // week number 1 or 2 62
          getWhiteSpaces(27),
          endAsDate // date 91-100
        ].join('')
      )
    }

    if (weeklyBonus) {
      data.push(
        [
          addWhiteSpaces(CO_NUMBER, 8), //Company 1-8
          getWhiteSpaces(1),
          addWhiteSpaces(emplNumber, 9), //  employee number 10-18
          getWhiteSpaces(1),
          addWhiteSpaces(statementNumber, 2),
          addWhiteSpaces('G' + cutExport, 4), // Statement number + Transaction type + Earning code 20-25
          getWhiteSpaces(4),
          addWhiteSpaces('', 8), // Quantity 30-37
          getWhiteSpaces(2),
          addWhiteSpaces('', 8), // Rate 40-47
          getWhiteSpaces(3),
          addWhiteSpaces(weeklyBonus, 9), // Amount 51-59
          getWhiteSpaces(2),
          addWhiteSpaces(0, 2), // week number 1 or 2 62
          getWhiteSpaces(27),
          endAsDate // date 91-100
        ].join('')
      )
    }

    if (declaration > 0) {
      data.push(
        [
          addWhiteSpaces(CO_NUMBER, 8), //Company 1-8
          getWhiteSpaces(1),
          addWhiteSpaces(emplNumber, 9), //  employee number 10-18
          getWhiteSpaces(1),
          addWhiteSpaces(statementNumber, 2),
          addWhiteSpaces('G' + declarationExport, 4), // Statement number + Transaction type + Earning code 20-25
          getWhiteSpaces(4),
          addWhiteSpaces('', 8), // Quantity 30-37
          getWhiteSpaces(2),
          addWhiteSpaces('', 8), // Rate 40-47
          getWhiteSpaces(3),
          addWhiteSpaces(declaration, 9), // Amount 51-59
          getWhiteSpaces(2),
          addWhiteSpaces(0, 2), // week number 1 or 2 62
          getWhiteSpaces(27),
          endAsDate // date 91-100
        ].join('')
      )
    }
  })
  // })

  // console.log(data)

  return data.join('\n')
}
