import React, { useEffect, useState } from 'react'
import Modal from 'react-bootstrap/Modal'
import { connect } from 'react-redux'
import { I18n } from 'react-redux-i18n'

import dayjs from 'dayjs'
import firebase from 'firebase/app'
import 'firebase/database'
import { cloneDeep } from 'lodash'
import filter from 'lodash/filter'
import forEach from 'lodash/forEach'
import styled from 'styled-components'
import { theme } from 'styles/theme'
import toastr from 'toastr'

import { OutlineButton } from 'components/ui/OutlineButton'

import PayrollConflictModalRoleDropdown from './PayrollConflictModalRoleDropdown'
import PayrollConflictModalTimeBlock from './PayrollConflictModalTimeBlock'

import { fetchAndGetEmployeeRate } from 'utils/employees'
import { minutesToHours } from 'utils/time'

import type { AppState } from 'types/appState'
import type { Company } from 'types/company'
import type { IEmployee } from 'types/employee'

import CloseIcon from 'img/IconsHover/CloseFilled'
import checkGreenIcon from 'img/icons/checkGreenFillIcon.svg'
import closeFilledRedIcon from 'img/icons/closeFilledIcon.svg'
import exclamationOrange from 'img/icons/exclamationOrange.svg'
import exclamationRed from 'img/icons/exclamationRed.svg'
import defaultAvatar from 'img/icons/logo_pivot.png'

interface IProps {
  showModal: boolean
  onClose: () => void
  currentCompany: Company | undefined
  conflicts: any
  employees: IEmployee[]
  roundingTime: number
  companyId: string
  jobs: Company['jobs']
}

interface TablePositionsItemProps {
  activePosition?: boolean
  disabled?: boolean
  children: React.ReactNode
}

interface TableStyledProps {
  disableOverflow?: boolean
  children: React.ReactNode
}

interface TableItemCardTimeStyledProps {
  isDifferenteTime?: boolean
  notClocked?: boolean
}

interface ExclamationIconStyledProps {
  orange?: boolean
}

interface TableItemCardClockBlockStyledProps {
  notSingle?: boolean
  shiftConfirmed?: boolean
}
type Updates = {
  [key: string]: any
}

function PayrollConflictModal({
  showModal,
  onClose,
  currentCompany,
  conflicts = {},
  employees = [],
  roundingTime,
  companyId,
  jobs
}: IProps) {
  const [conflictsCopy, setConflictsCopy] = useState<any>({})
  const [initialDataSet, setInitialDataSet] = useState(false)

  useEffect(() => {
    if (showModal) {
      if (!initialDataSet) {
        setInitialDataSet(true)
        setConflictsCopy(conflicts)
      }
    } else {
      setConflictsCopy({})
    }
  }, [showModal, conflicts, initialDataSet])

  const [selectedPositionId, setSelectedPositionId] = useState('')

  let numberOfConflicts = 0
  const positions: string[] = []
  const numberOfConflictsByPosition: any = {}

  forEach(conflictsCopy, (dayEmployees, date) => {
    forEach(dayEmployees, (employeeShifts, employeeId) => {
      forEach(employeeShifts, (shift, shiftKey) => {
        if (!shift.isConfirmed) {
          if (shift.positionId) {
            if (!positions.includes(shift.positionId)) {
              positions.push(shift.positionId)
            }
            if (!numberOfConflictsByPosition[shift.positionId]) {
              numberOfConflictsByPosition[shift.positionId] = 1
            } else {
              numberOfConflictsByPosition[shift.positionId] += 1
            }
          }
          numberOfConflicts++
        }
      })
    })
  })

  const onApply = (
    shiftKey: number,
    employeeId: string,
    date: string,
    newValue: any
  ) => {
    setConflictsCopy((state: any) => ({
      ...state,
      [date]: {
        ...state[date],
        [employeeId]: {
          ...state[date][employeeId],
          [shiftKey]: {
            ...state[date][employeeId][shiftKey],
            ...newValue
          }
        }
      }
    }))
  }

  const onConfirm = async (shift: any) => {
    const {
      shiftKey,
      employeeId,
      date,
      shiftStartRounded,
      shiftEndRounded,
      positionId,
      subpositionId = null
    } = shift

    const path = `Attendance/${companyId}/${date}/${employeeId}`

    const { rate, additionalSalary, type } = await fetchAndGetEmployeeRate({
      companyId: currentCompany?.key || '',
      employeeId,
      positionId,
      jobs
    })

    const payload = {
      start: shiftStartRounded,
      end: shiftEndRounded,
      positionId,
      subpositionId,
      isConfirmed: true,
      rate,
      additionalSalary,
      type
    }

    const updates: Updates = {}

    Object.keys(payload).forEach(key => {
      updates[`${path}/${shiftKey}/${key}`] =
        payload[key as keyof typeof payload]
    })

    firebase
      .database()
      .ref()
      .update(updates)
      .then(() => {
        onApply(shiftKey, employeeId, date, {
          isConfirmed: true,
          rate,
          additionalSalary
        })
      })
      .catch(() => {
        toastr.error('Error', 'Something went wrong.')
      })
  }

  const onDelete = (shiftKey: string, employeeId: string, date: string) => {
    const copy = cloneDeep(conflictsCopy)
    const shift = copy[date][employeeId][shiftKey]
    delete copy[date][employeeId][shiftKey]
    setConflictsCopy(copy)

    const path = `Attendance/${companyId}/${date}/${employeeId}/${shiftKey}`

    firebase
      .database()
      .ref(path)
      .set(null)
      .catch(() => {
        toastr.error('Error', 'Something went wrong.')
        copy[date][employeeId][shiftKey] = shift
        setConflictsCopy(copy)
      })
  }

  return (
    <ModalStyled
      show={showModal}
      onHide={onClose}
    >
      <ModalHeaderStyled>
        <span>{numberOfConflicts}</span>
        {I18n.t('attendance.conflictingShifts')}
        <button onClick={onClose}>
          <CloseIcon />
        </button>
      </ModalHeaderStyled>
      <ModalBodyStyled>
        <TablePositionsStyled>
          <ul>
            <TablePositionsItemStyled
              activePosition={!selectedPositionId}
              onClick={() => setSelectedPositionId('')}
            >
              <div>
                {I18n.t('attendance.allPositions')}
                <span>{numberOfConflicts}</span>
              </div>
            </TablePositionsItemStyled>

            {Object.keys(jobs)
              .sort((a, b) => {
                const aValue = numberOfConflictsByPosition[a]
                const bValue = numberOfConflictsByPosition[b]

                if (!aValue && bValue) {
                  return 1
                }
                if (aValue && !bValue) {
                  return -1
                }

                return 0
              })
              .map(positionId => {
                const isDisabled = !numberOfConflictsByPosition[positionId]

                return (
                  <TablePositionsItemStyled
                    disabled={isDisabled}
                    key={positionId}
                    activePosition={selectedPositionId === positionId}
                    onClick={() => {
                      if (!isDisabled) {
                        setSelectedPositionId(positionId)
                      }
                    }}
                  >
                    <div>
                      {jobs?.[positionId]?.name}
                      {!isDisabled && (
                        <span>{numberOfConflictsByPosition[positionId]}</span>
                      )}
                    </div>
                  </TablePositionsItemStyled>
                )
              })}
          </ul>
        </TablePositionsStyled>
        <TableStyled>
          {Object.keys(conflictsCopy)
            .sort((a, b) => (a < b ? -1 : 1))
            .map(date => {
              const dateFormatted = dayjs(date).format('ddd D')
              const dayConflits: any = []

              forEach(conflictsCopy[date], (employeeShifts, employeeId) => {
                forEach(employeeShifts, (shift, shiftKey) => {
                  const employee = employees.find(c => c.uid === employeeId)
                  if (
                    employee &&
                    (!selectedPositionId ||
                      selectedPositionId === shift.positionId)
                  ) {
                    dayConflits.push({
                      ...shift,
                      employeeId,
                      shiftKey,
                      employee,
                      date
                    })
                  }
                })
              })

              if (!dayConflits.length) {
                return null
              }

              return (
                <TableRowStyled key={date}>
                  <TableRowDateStyled>{dateFormatted}</TableRowDateStyled>
                  <TableRowContainer>
                    {dayConflits.map((shift: any, i: number) => {
                      const {
                        employeeId,
                        shiftKey,
                        isClockInDifferent = false,
                        shiftStartRounded,
                        shiftEndRounded,
                        isClockOutDiffrent,
                        neverClockedOut,
                        positionId = '',
                        scheduledShift,
                        employee,
                        isConfirmed,
                        missingPosition
                      } = shift
                      return (
                        <TableItemStyled key={`${employeeId}-${shiftKey}`}>
                          <TableItemHeaderStyled>
                            <img
                              src={employee?.avatar || defaultAvatar}
                              alt=''
                            />
                            {employee?.name} {employee?.surname}
                          </TableItemHeaderStyled>
                          <TableItemCardStyled>
                            {neverClockedOut ? (
                              <CloseIconStyled />
                            ) : isClockOutDiffrent ? (
                              <ExclamationIconStyled orange={false} />
                            ) : isClockInDifferent ? (
                              <ExclamationIconStyled orange={true} />
                            ) : null}

                            {scheduledShift ? (
                              <TableItemCardShiftStyled>
                                <p>{I18n.t('attendance.scheduledShift')}</p>
                                <div>
                                  {/* Different time */}
                                  <TableItemCardTimeStyled
                                    isDifferenteTime={isClockInDifferent}
                                  >
                                    {minutesToHours(
                                      scheduledShift.start,
                                      false
                                    )}
                                  </TableItemCardTimeStyled>
                                  &nbsp;-&nbsp;
                                  {/* never clocked && not different time */}
                                  <TableItemCardTimeStyled
                                    notClocked={neverClockedOut}
                                  >
                                    {scheduledShift.end !== null
                                      ? minutesToHours(scheduledShift.end)
                                      : '-'}
                                  </TableItemCardTimeStyled>
                                </div>
                                <div>
                                  {jobs?.[scheduledShift.positionId]?.name}{' '}
                                </div>
                              </TableItemCardShiftStyled>
                            ) : (
                              <TableItemCardShiftStyled>
                                <p>{I18n.t('attendance.nonScheduledShift')}</p>
                              </TableItemCardShiftStyled>
                            )}

                            <TableItemCardClockBlockStyled
                              notSingle={
                                isClockInDifferent &&
                                (neverClockedOut || isClockOutDiffrent)
                              }
                              shiftConfirmed={isConfirmed}
                            >
                              {isClockInDifferent && (
                                <PayrollConflictModalTimeBlock
                                  title={I18n.t('attendance.clockedIn')}
                                  roundingTime={roundingTime}
                                  initialValue={shiftStartRounded}
                                  onApply={newValue =>
                                    onApply(shiftKey, employeeId, date, {
                                      shiftStartRounded: newValue
                                    })
                                  }
                                  isConfirmed={isConfirmed}
                                  isDifferentTime={true}
                                />
                              )}
                              {(isClockOutDiffrent || neverClockedOut) && (
                                <PayrollConflictModalTimeBlock
                                  title={I18n.t('attendance.clockedOut')}
                                  roundingTime={roundingTime}
                                  initialValue={shiftEndRounded}
                                  onApply={newValue =>
                                    onApply(shiftKey, employeeId, date, {
                                      shiftEndRounded: newValue
                                    })
                                  }
                                  isConfirmed={isConfirmed}
                                  isDifferentTime={isClockOutDiffrent}
                                />
                              )}
                              {missingPosition && (
                                <PayrollConflictModalRoleDropdown
                                  title={I18n.t('attendance.role')}
                                  roundingTime={roundingTime}
                                  initialValue={positionId}
                                  onApply={newValue =>
                                    onApply(
                                      shiftKey,
                                      employeeId,
                                      date,
                                      newValue
                                    )
                                  }
                                  employee={employee}
                                  jobs={jobs}
                                  isConfirmed={isConfirmed}
                                />
                              )}
                            </TableItemCardClockBlockStyled>
                            {isConfirmed ? (
                              <TableItemCardConfirmedStyled>
                                <div>
                                  <img
                                    src={checkGreenIcon}
                                    alt=''
                                  />
                                  {I18n.t('attendance.shiftConfirmed')}
                                </div>
                                <button
                                  onClick={() =>
                                    onApply(shiftKey, employeeId, date, {
                                      isConfirmed: false
                                    })
                                  }
                                >
                                  {I18n.t('attendance.changeDecision')}
                                </button>
                              </TableItemCardConfirmedStyled>
                            ) : (
                              <TableItemCardButtonsStyled>
                                <OutlineButtonStyled
                                  color='red'
                                  onClick={() => {
                                    onDelete(shiftKey, employeeId, date)
                                  }}
                                >
                                  {I18n.t('attendance.delete')}
                                </OutlineButtonStyled>
                                <OutlineButtonStyled
                                  color='green'
                                  disabled={shiftEndRounded === null}
                                  onClick={() => {
                                    onConfirm(shift)
                                  }}
                                >
                                  {I18n.t('attendance.confirm')}
                                </OutlineButtonStyled>
                              </TableItemCardButtonsStyled>
                            )}
                          </TableItemCardStyled>
                        </TableItemStyled>
                      )
                    })}
                  </TableRowContainer>
                </TableRowStyled>
              )
            })}
        </TableStyled>
      </ModalBodyStyled>
    </ModalStyled>
  )
}

const mapStateToProps = (state: AppState) => ({
  currentCompany: state.companies.find(
    company => company.key === state.currentCompanyId
  ),
  employees: filter(state.employees.allEmployees, employee => !employee.isAdmin)
    .sort((a, b) => {
      const aSurname = a.surname || ''
      const bSurname = b.surname || ''
      return aSurname.localeCompare(bSurname)
    })
    .map(employee => {
      if (employee.positions) {
        return employee
      }
      return {
        ...employee,
        positions: employee.lastPositions
      }
    })
})

export default connect(mapStateToProps)(PayrollConflictModal)

const ModalStyled = styled(Modal)`
  .modal-content {
    width: 65vw;
    max-width: 65rem;
    margin: auto;
  }
`

const ModalHeaderStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;

  width: 100%;
  padding: ${theme.rem(10)} ${theme.rem(15)};

  position: relative;

  background-color: #f9b63a;
  border-radius: 0.8rem 0.8rem 0 0;

  color: #69748f;
  font-family: ${theme.fonts.bold};
  font-size: ${theme.remFont(17)};

  span {
    display: flex;
    justify-content: center;
    align-items: center;

    height: ${theme.rem(34)};
    width: ${theme.rem(34)};
    margin-right: ${theme.rem(10)};

    background-color: #fdeacd;
    border-radius: 6px;

    color: #f9b63a;
    font-size: ${theme.remFont(17)};
  }

  button {
    display: flex;

    background: none;
    border: none;
    padding: 0;

    position: absolute;
    right: ${theme.rem(15)};

    svg {
      height: ${theme.rem(28)};
      width: ${theme.rem(28)};
    }

    &:hover {
      svg {
        fill: #fef7ed;
      }
    }
  }
`
const ModalBodyStyled = styled.div`
  display: flex;

  width: 100%;
`

const TablePositionsStyled = styled.div`
  display: flex;

  width: 25%;
  ul {
    width: 100%;
    margin: 0;
    padding: 0;
    max-height: ${theme.rem(660)};

    background-color: #ecf1f5;
    border-radius: 0 0 0 14px;

    list-style: none;
    overflow: auto;
    &::-webkit-scrollbar {
      display: none;
    }
  }
  @media (max-width: 1400px) {
    width: 22%;
  }
`
const TablePositionsItemStyled = styled.li<TablePositionsItemProps>`
  display: flex;
  align-items: center;
  justify-content: flex-start;

  width: 100%;
  padding: ${theme.rem(8)} 0 ${theme.rem(8)} ${theme.rem(8)};

  cursor: ${({ disabled }) => (disabled ? 'default' : 'pointer')};

  div {
    display: flex;
    justify-content: space-between;
    padding: ${theme.rem(12)} ${theme.rem(20)} ${theme.rem(12)} ${theme.rem(20)};
    width: 100%;

    position: relative;

    background-color: ${({ activePosition }) =>
      activePosition ? `${theme.colors.midGrey600}!important` : ''};
    border-radius: 14px 0 0 14px;

    color: ${({ activePosition }) =>
      activePosition ? '#fff' : theme.colors.midGrey600};
    font-size: ${theme.remFont(16)};
    font-family: ${theme.fonts.bold};
    text-align: left;
    opacity: ${({ disabled }) => (disabled ? '0.3' : '')};

    span {
      color: ${({ activePosition }) =>
        activePosition ? '#fff' : theme.colors.midGrey600};
    }

    &:after {
      content: ${({ activePosition }) => (activePosition ? "''" : 'unset')};

      width: 4px;
      height: 100%;

      position: absolute;
      right: 0px;
      top: 0px;

      background-color: ${theme.colors.blue};
    }
    &:hover {
      background-color: ${({ disabled }) => (disabled ? '' : '#dbe3eb')};
    }
  }
`
const TableStyled = styled.div<TableStyledProps>`
  display: flex;
  flex-direction: column;

  flex: 1;
  padding: ${theme.rem(20)} ${theme.rem(25)};
  max-height: ${theme.rem(660)};

  position: relative;

  overflow: auto;
  scroll-behavior: smooth;
`

const TableRowStyled = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  width: 100%;
  padding-top: ${theme.rem(15)};

  &:not(:nth-child(2)) {
    padding-bottom: ${theme.rem(15)};
  }
`
const TableRowDateStyled = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;

  padding: 0.3rem 0.5rem;
  min-width: 7rem;

  background-color: ${theme.colors.midGrey600};
  border-radius: 0.6rem 0.6rem 0 0;

  color: #fff;
  font-family: ${theme.fonts.bold};
  font-size: ${theme.remFont(15)};
`

const TableRowContainer = styled.div`
  display: flex;
  flex-wrap: wrap;

  gap: ${theme.rem(10)} ${theme.rem(25)};
  width: 100%;
  padding: ${theme.rem(10)} ${theme.rem(25)} ${theme.rem(20)};

  background-color: #dbe3eb;
  border-radius: 12px;
`

const TableItemStyled = styled.div`
  display: flex;
  flex-direction: column;

  width: calc((100% - ${theme.rem(50)}) / 3);
`
const TableItemHeaderStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: flex-start;

  padding: ${theme.rem(8)} 0;
  width: 100%;

  font-size: ${theme.remFont(15)};
  color: ${theme.colors.darkGrey};
  font-family: ${theme.fonts.bold};
  text-align: left;
  line-height: 1;

  img {
    flex-shrink: 0;
    width: ${theme.rem(34)};
    height: ${theme.rem(34)};
    margin-right: ${theme.rem(10)};
    object-fit: cover;

    border-radius: 50%;
  }
`
const TableItemCardStyled = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  width: 100%;

  position: relative;

  background-color: #ecf1f5;
  border-radius: 12px;
`

const TableItemCardShiftStyled = styled.div`
  display: flex;
  flex-direction: column;

  padding: 0.5rem 0;
  gap: 0.3rem;

  div {
    color: ${theme.colors.darkGrey};
    line-height: normal;
    font-family: ${theme.fonts.heavy};
    font-size: ${theme.remFont(13)};
  }
  p {
    color: ${theme.colors.midGrey600};
    font-family: ${theme.fonts.boldItalic};
    font-size: ${theme.remFont(12)};
  }
`

const TableItemCardTimeStyled = styled.span<TableItemCardTimeStyledProps>`
  color: ${props =>
    props.isDifferenteTime
      ? '#f9b63a'
      : props.notClocked
        ? '#ec758a'
        : theme.colors.darkGrey};
`

const ExclamationIconStyled = styled.div<ExclamationIconStyledProps>`
  display: flex;
  justify-content: center;
  align-items: center;

  width: ${theme.rem(26)};
  height: ${theme.rem(26)};

  position: absolute;
  right: -${theme.rem(13)};
  top: -${theme.rem(13)};

  background-image: url(${({ orange }) =>orange ? exclamationOrange : exclamationRed});
  background-color: ${({ orange }) => (orange ? '#f9b63a' : '#ec758a')};
  background-size: ${theme.rem(2.5)};
  background-position: center;
  background-repeat: no-repeat;
  border-radius: 50%;
`

const TableItemCardClockBlockStyled = styled.div<TableItemCardClockBlockStyledProps>`
  display: flex;
  flex-wrap: wrap;
  justify-content: ${({ notSingle }) =>
    notSingle ? 'space-between' : 'center'};

  width: 100%;
  padding: ${theme.rem(10)} ${theme.rem(16)};

  position: relative;

  background-color: ${({ shiftConfirmed }) =>
    shiftConfirmed ? 'transparent' : '#fff'};
  border-radius: 12px;

  > div {
    width: calc(50% - ${theme.rem(8)});

    p {
      color: ${theme.colors.midGrey600};
      font-family: ${theme.fonts.boldItalic};
      line-height: normal;
      font-size: ${theme.remFont(12)};
    }
  }
`

const TableItemCardButtonsStyled = styled.div`
  display: flex;
  justify-content: space-between;

  gap: 0.6rem;
  width: 100%;
  padding: 0.6rem;
`

const OutlineButtonStyled = styled(OutlineButton)`
  height: 1.75rem;

  box-shadow: none;
  border-radius: 0.5rem;

  font-size: ${theme.remFont(14)};
`

const CloseIconStyled = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;

  width: ${theme.rem(26)};
  height: ${theme.rem(26)};

  position: absolute;
  right: -${theme.rem(13)};
  top: -${theme.rem(13)};

  background-image: url(${closeFilledRedIcon});
  background-size: 100%;
  background-position: center;
  background-repeat: no-repeat;
  border-radius: 50%;
`

const TableItemCardConfirmedStyled = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  gap: 0.3rem;

  padding: ${theme.rem(3)} ${theme.rem(10)} ${theme.rem(10)};

  div {
    display: flex;
    align-items: center;
    gap: 0.4rem;
    font-size: 0.85rem;
    color: ${theme.colors.green};
    font-family: ${theme.fonts.bold};

    img {
      width: 1rem;
      height: 1rem;
    }
  }
  button {
    padding: 0;

    background-color: transparent;
    border: none;

    color: ${theme.colors.midGrey600};
    font-size: 0.8rem;
    font-family: ${theme.fonts.normal};
    text-decoration: underline;

    &:hover,
    &:focus {
      color: ${theme.colors.darkGrey};
    }
  }
`
