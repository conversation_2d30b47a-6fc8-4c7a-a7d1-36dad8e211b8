import React from 'react'
import Mo<PERSON> from 'react-bootstrap/Modal'
import OverlayTrigger from 'react-bootstrap/OverlayTrigger'
import Popover from 'react-bootstrap/Popover'
import { I18n } from 'react-redux-i18n'

import dayjs from 'dayjs'
import styled from 'styled-components'
import { theme } from 'styles/theme'

import NumberInput from 'components/ui/NumberInput'
import CustomSelect from 'components/ui/Select'

import { roundTime } from '../payrollUtils'

import { minutesToHours } from 'utils/time'

import {
  AttendanceSettings,
  CutDistribution,
  CutFormat,
  EmployeeOrder,
  OvertimeCalculationMode,
  PayrollFrequency,
  SalaryType,
  TipsDistribution
} from 'types/attendance'
import { Option } from 'types/selectOption'

import closeIcon from 'img/icons/closeWhite.svg'
import triangleIcon from 'img/icons/triangleDarkIcon.svg'

// FIX TODO
// for each input with % value - add type='number' + '%' to value, or leave type='text' but add certain check for numbers only!
// remove any disabled and $isDisabled props from inputs if needed

type OptionType<T> = {
  label: string
  value: T
  disabled?: boolean
}

const PopoverHintStyled = styled(Popover)`
  max-width: 17rem;
  width: 17rem;
  padding: 1rem !important;

  border: solid 1px #dbdddd;
  border-radius: 16px;
  margin-left: 1rem;

  z-index: 1051;
`

const HintPopoverTextStyled = styled.p<{ isItalic?: boolean }>`
  text-align: center;
  color: ${theme.colors.midGrey600};
  font-size: ${theme.remFont(15)};
  font-family: ${({ isItalic }) =>
    isItalic ? theme.fonts.boldItalic : theme.fonts.bold};
  span {
    color: ${theme.colors.darkGrey};
    font-family: ${theme.fonts.heavy};
  }
`
const PopoverMarkStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;

  width: ${theme.rem(22)};
  height: ${theme.rem(22)};
  margin-left: ${theme.rem(12)};

  background-color: #fff;
  border-radius: 50%;

  font-size: ${theme.remFont(14)};
  color: ${theme.colors.midGrey600};
  font-style: normal;
  cursor: pointer;
  &:hover {
    box-shadow: 0px 0px 10px -3px rgba(0, 0, 0, 0.4);
  }
`

const HintPopoverExampleBlockStyled = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  margin: ${theme.rem(15)} ${theme.rem(15)} 0;
  padding: ${theme.rem(10)};

  background-color: #ecf1f5;
  border-radius: 1rem;
`

const HintPopoverTimeBlockStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;

  div {
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;

    flex: 1;
    padding: ${theme.rem(3)} ${theme.rem(6)};
    min-width: ${theme.rem(85)};

    span {
      display: block;
      font-style: normal;
    }
  }
`

const TimeTriangleIconStyled = styled.img`
  display: flex;
  align-self: flex-end;

  width: ${theme.rem(12)};
  height: ${theme.rem(12)};
  margin-bottom: ${theme.rem(8)};
  @media (max-width: 1400px) {
    width: ${theme.rem(11)};
    height: ${theme.rem(11)};
  }
`

const RoundTimeHintPopover = React.forwardRef<
  HTMLDivElement,
  { value: number }
>(({ value, ...rest }, ref) => (
  <PopoverHintStyled
    {...rest}
    ref={ref}
    id='payrol-setting-hint-popover'
  >
    <HintPopoverTextStyled>
      {I18n.t('attendance.roundningTimeIs')}
    </HintPopoverTextStyled>
    <HintPopoverExampleBlockStyled>
      <HintPopoverTextStyled isItalic>
        {I18n.t('attendance.example')}:
      </HintPopoverTextStyled>
      <HintPopoverTextStyled>
        {I18n.t('attendance.roundingTimeAt')} <span>{value} min</span>
      </HintPopoverTextStyled>
      <HintPopoverTimeBlockStyled>
        <div>
          <HintPopoverTextStyled isItalic>
            {I18n.t('attendance.clockAt')}
            <span>8:03</span>
          </HintPopoverTextStyled>
        </div>
        <TimeTriangleIconStyled
          src={triangleIcon}
          alt=''
        />
        <div>
          <HintPopoverTextStyled isItalic>
            {I18n.t('attendance.rounded')}
            <span>{minutesToHours(roundTime(8 * 60 + 3, value))}</span>
          </HintPopoverTextStyled>
        </div>
      </HintPopoverTimeBlockStyled>
    </HintPopoverExampleBlockStyled>
  </PopoverHintStyled>
))

type PayrollSettingsModalProps = {
  showModal: boolean
  onClose: () => void
  jobs: Record<string, { name: string }>
  attendanceSettings: AttendanceSettings
  setAttendanceSettings: React.Dispatch<
    React.SetStateAction<AttendanceSettings>
  >
  maxHoursPerWeek: number
}

function PayrollSettingsModal({
  showModal,
  onClose,
  jobs,
  attendanceSettings,
  setAttendanceSettings,
  maxHoursPerWeek
}: PayrollSettingsModalProps) {
  const {
    roundingTime = 5,
    positionSettings = {},
    startingWeek,
    tipsDeclaration,
    tipsDistribution,
    cutDistribution,
    tipsExport,
    cutExport,
    declarationExport,
    cutPercentage,
    cutRoundingValue,
    cutRoundingType,
    overtimeExport,
    payrollFrequency,
    overtimeCalculationMode = 'weekly',
    employeeOrder = 'surname'
  } = attendanceSettings

  const weeks = []
  for (let i = 0; i < 3; i++) {
    const startOfWeek = dayjs().startOf('week').add(i, 'weeks')

    weeks.push({
      value: startOfWeek.format('YYYY-MM-DD'),
      label: `${startOfWeek.format('MMM')} ${startOfWeek.format(
        'D'
      )}-${startOfWeek.clone().endOf('week').format('D')}`
    })
  }

  const payrollFrequencyOptions: OptionType<PayrollFrequency>[] = [
    { value: 'biweekly', label: I18n.t('attendance.biweekly') },
    { value: 'weekly', label: I18n.t('attendance.weekly') }
  ]

  const overtimeOptions: OptionType<OvertimeCalculationMode>[] = [
    {
      value: 'not-calculated',
      label: I18n.t('attendance.overtime_not_calculated')
    },
    {
      value: 'weekly',
      label: `${I18n.t('attendance.overtime_weekly')} (+${maxHoursPerWeek}h)`
    },
    {
      value: 'biweekly',
      label: `${I18n.t('attendance.overtime_biweekly')} (+${maxHoursPerWeek * 2}h)`
    }
  ]

  const tipsDistributionOptions: OptionType<TipsDistribution>[] = [
    // {
    //   value: 'time-period',
    //   label: I18n.t('attendance.byTimePeriod')
    // },
    {
      value: 'daily',
      label: I18n.t('attendance.byTimePeriod')
    }
  ]

  const cutDistributionOptions: OptionType<CutDistribution>[] = [
    {
      value: 'daily',
      label: I18n.t('attendance.daily')
    },
    {
      value: 'weekly',
      label: I18n.t('attendance.weekly')
    },
    {
      value: 'biweekly',
      label: I18n.t('attendance.biweekly'),
      disabled: true
    }
  ]

  const salaryTypeOptions: OptionType<SalaryType>[] = [
    {
      value: 'wage',
      label: I18n.t('attendance.wageOnly')
    },
    {
      value: 'wage_tips',
      label: I18n.t('attendance.wageTips')
    },
    { value: 'wage_cut', label: I18n.t('attendance.wageCut') }
  ]

  const cutFormatOptions: OptionType<CutFormat>[] = [
    {
      value: 'cash',
      label: I18n.t('attendance.inCash')
    },
    {
      value: 'pay',
      label: I18n.t('attendance.deductedFromPay')
    }
  ]

  const sortingOptions: OptionType<EmployeeOrder>[] = [
    {
      value: 'surname',
      label: I18n.t('attendance.last_name')
    },
    {
      value: 'name',
      label: I18n.t('attendance.first_name')
    },
    {
      value: 'employee-number',
      label: I18n.t('attendance.employee_number')
    }
  ]

  // Action type: increase | decrease
  const updateRoundedTime = (action: 'increase' | 'decrease') => {
    if (action === 'increase') {
      setAttendanceSettings(state => ({
        ...state,
        roundingTime: state.roundingTime === 1 ? 5 : state.roundingTime + 5
      }))
    } else {
      setAttendanceSettings(state => ({
        ...state,
        roundingTime: state.roundingTime > 5 ? state.roundingTime - 5 : 1
      }))
    }
  }

  return (
    <ModalStyled
      show={showModal}
      onHide={onClose}
    >
      <ModalHeaderStyled>
        {I18n.t('attendance.payrollSettings')}
        <CloseButtonStyled onClick={onClose}>
          <img
            src={closeIcon}
            alt=''
          />
        </CloseButtonStyled>
      </ModalHeaderStyled>

      <ModalBodyStyled>
        <CardStyled>
          <CardTitleStyled>{I18n.t('attendance.general')}</CardTitleStyled>

          <SettingsContainerRowStyled>
            <SettingsBlockStyled>
              <SettingsLabelStyled>
                {I18n.t('attendance.payrollFrequency')}
              </SettingsLabelStyled>
              <CustomSelect
                options={payrollFrequencyOptions}
                value={payrollFrequencyOptions.find(
                  option => option.value === payrollFrequency
                )}
                placeholder={I18n.t('schedule.selectWeek')}
                components={{
                  IndicatorSeparator: () => null
                }}
                onChange={option => {
                  const newValue = option as OptionType<PayrollFrequency>
                  setAttendanceSettings(state => ({
                    ...state,
                    payrollFrequency: newValue.value
                  }))
                }}
              />
            </SettingsBlockStyled>

            <SettingsBlockStyled>
              <SettingsLabelStyled>
                {I18n.t('attendance.nextStartingWeek')}
              </SettingsLabelStyled>
              <CustomSelect
                options={weeks}
                value={weeks.find(({ value }) => value === startingWeek)}
                placeholder={I18n.t('schedule.selectWeek')}
                onChange={e => {
                  const newValue = e as Option

                  setAttendanceSettings(state => ({
                    ...state,
                    startingWeek: newValue.value
                  }))
                }}
                components={{
                  IndicatorSeparator: () => null
                }}
              />
            </SettingsBlockStyled>

            <SettingsBlockStyled>
              <SettingsLabelStyled>
                {I18n.t('attendance.roundingTime')}
                <OverlayTrigger
                  trigger={['hover', 'focus']}
                  placement='right'
                  overlay={<RoundTimeHintPopover value={roundingTime} />}
                >
                  <PopoverMarkStyled>?</PopoverMarkStyled>
                </OverlayTrigger>
              </SettingsLabelStyled>
              <NumberInputStyled
                value={`${roundingTime} min`}
                disabledMinus={roundingTime === 1}
                disabledPlus={roundingTime === 15}
                onIncrease={() => updateRoundedTime('increase')}
                onDecrease={() => updateRoundedTime('decrease')}
                isInverse
              />
            </SettingsBlockStyled>
            {/* <SettingsBlockStyled>
              <SettingsLabelStyled>
                {I18n.t('attendance.tipsFormat')}
              </SettingsLabelStyled>
              <CustomSelect
                isDisabled={true}
                options={[
                  { value: 'normal', label: I18n.t('attendance.normal') },
                  { value: 'split', label: I18n.t('attendance.splitTip') }
                ]}
                value={{ value: 'normal', label: I18n.t('attendance.normal') }}
                placeholder={I18n.t('attendance.normal')}
                onChange={() => {}}
                components={{
                  IndicatorSeparator: () => null
                }}
              />
            </SettingsBlockStyled> */}
            <SettingsBlockStyled>
              <SettingsLabelStyled>
                {I18n.t('attendance.employee_order')}
              </SettingsLabelStyled>
              <CustomSelect
                options={sortingOptions}
                value={sortingOptions.find(
                  option => option.value === employeeOrder
                )}
                onChange={option => {
                  const newValue = option as OptionType<EmployeeOrder>
                  setAttendanceSettings(state => ({
                    ...state,
                    employeeOrder: newValue.value
                  }))
                }}
                components={{
                  IndicatorSeparator: () => null
                }}
              />
            </SettingsBlockStyled>
            <SettingsBlockStyled>
              <SettingsLabelStyled>
                {I18n.t('attendance.overtime_paid')}
              </SettingsLabelStyled>
              <CustomSelect
                options={overtimeOptions}
                value={overtimeOptions.find(
                  option => option.value === overtimeCalculationMode
                )}
                components={{
                  IndicatorSeparator: () => null
                }}
                onChange={option => {
                  const newValue = option as OptionType<OvertimeCalculationMode>
                  setAttendanceSettings(state => ({
                    ...state,
                    overtimeCalculationMode: newValue.value
                  }))
                }}
                isOptionDisabled={option => {
                  const optionValue =
                    option as OptionType<OvertimeCalculationMode>

                  return (
                    optionValue.value === 'biweekly' &&
                    payrollFrequency === 'weekly'
                  )
                }}
              />
            </SettingsBlockStyled>
          </SettingsContainerRowStyled>
        </CardStyled>

        <CardStyled>
          <CardTitleStyled>{I18n.t('attendance.tipsAndCut')}</CardTitleStyled>
          <SettingsContainerRowStyled>
            <SettingsBlockStyled>
              <SettingsLabelStyled>
                {I18n.t('attendance.tipsDeclaration')}
              </SettingsLabelStyled>
              <ExportInputStyled>
                <ExportInputLabelStyled $isOnRight>%</ExportInputLabelStyled>
                <SettingsInputStyled
                  value={tipsDeclaration}
                  $withLabel
                  type='number'
                  onChange={e => {
                    const value = e.target.value
                    setAttendanceSettings(state => ({
                      ...state,
                      tipsDeclaration: value
                    }))
                  }}
                />
              </ExportInputStyled>
            </SettingsBlockStyled>
            <SettingsBlockStyled>
              <SettingsLabelStyled>
                {I18n.t('attendance.tipsDistribution')}
              </SettingsLabelStyled>
              <CustomSelect
                isDisabled={true}
                options={tipsDistributionOptions}
                value={tipsDistributionOptions.find(
                  ({ value }) => value === tipsDistribution
                )}
                onChange={e => {
                  const newValue = e as OptionType<TipsDistribution>

                  setAttendanceSettings(state => ({
                    ...state,
                    tipsDistribution: newValue.value
                  }))
                }}
                components={{
                  IndicatorSeparator: () => null
                }}
              />
            </SettingsBlockStyled>

            <SettingsBlockStyled>
              <SettingsLabelStyled>
                {I18n.t('attendance.cutRounding')}
              </SettingsLabelStyled>

              <PairedInputWrapStyled>
                <ExportInputStyled>
                  <SettingsInputStyled
                    value={cutPercentage}
                    onChange={e => {
                      const value = e.target.value

                      if (!value || +value <= 100) {
                        setAttendanceSettings(state => ({
                          ...state,
                          cutPercentage: value
                        }))
                      }
                    }}
                    max='100'
                    type='number'
                  />
                  <PairedInputLabelStyled>%</PairedInputLabelStyled>
                </ExportInputStyled>

                <ExportInputStyled>
                  <SettingsInputStyled
                    value={cutRoundingValue}
                    onChange={e => {
                      const value = e.target.value

                      setAttendanceSettings(state => ({
                        ...state,
                        cutRoundingValue: value
                      }))
                    }}
                    type='number'
                    $isEmpty={false}
                  />
                  <PairedInputLabeExtraStyled $isEmpty={false}>
                    $
                  </PairedInputLabeExtraStyled>
                  <RoundingSwitchStyled $isDisabled={!cutRoundingValue}>
                    <RoundingTopButtonStyled
                      onClick={() => {
                        setAttendanceSettings(state => ({
                          ...state,
                          cutRoundingType: 'up'
                        }))
                      }}
                      $isActive={
                        !(cutRoundingValue && cutRoundingType === 'up')
                      }
                    >
                      <ArrowTopStyled />
                    </RoundingTopButtonStyled>
                    <RoundingDownButtonStyled
                      onClick={() => {
                        setAttendanceSettings(state => ({
                          ...state,
                          cutRoundingType: 'down'
                        }))
                      }}
                      $isActive={
                        !(cutRoundingValue && cutRoundingType === 'down')
                      }
                    >
                      <ArrowDownStyled />
                    </RoundingDownButtonStyled>
                  </RoundingSwitchStyled>
                </ExportInputStyled>
              </PairedInputWrapStyled>
            </SettingsBlockStyled>

            <SettingsBlockStyled>
              <SettingsLabelStyled>
                {I18n.t('attendance.cutDistribution')}
              </SettingsLabelStyled>
              <CustomSelect
                options={cutDistributionOptions}
                value={cutDistributionOptions.find(
                  ({ value }) => value === cutDistribution
                )}
                onChange={e => {
                  const newValue = e as OptionType<CutDistribution>

                  setAttendanceSettings(state => ({
                    ...state,
                    cutDistribution: newValue.value
                  }))
                }}
                components={{
                  IndicatorSeparator: () => null
                }}
              />
            </SettingsBlockStyled>
          </SettingsContainerRowStyled>
        </CardStyled>

        <CardStyled>
          <CardTitleStyled>{I18n.t('attendance.export')}</CardTitleStyled>
          <SettingsContainerRowStyled>
            <SettingsBlockStyled>
              <SettingsLabelStyled>
                {I18n.t('attendance.overtime_export')}
              </SettingsLabelStyled>
              <ExportInputStyled>
                <ExportInputLabelStyled>G-</ExportInputLabelStyled>
                <SettingsInputStyled
                  value={overtimeExport}
                  onChange={e => {
                    const value = e.target.value

                    if (!value || value.length <= 3) {
                      setAttendanceSettings(state => ({
                        ...state,
                        overtimeExport: value
                      }))
                    }
                  }}
                  readOnly={false}
                  type='number'
                  $withLabel
                />
              </ExportInputStyled>
            </SettingsBlockStyled>

            <SettingsBlockStyled>
              <SettingsLabelStyled>
                {I18n.t('attendance.tipsExport')}
              </SettingsLabelStyled>
              <ExportInputStyled>
                <ExportInputLabelStyled>G-</ExportInputLabelStyled>
                <SettingsInputStyled
                  value={tipsExport}
                  onChange={e => {
                    const value = e.target.value

                    if (!value || value.length <= 3) {
                      setAttendanceSettings(state => ({
                        ...state,
                        tipsExport: value
                      }))
                    }
                  }}
                  readOnly={false}
                  type='number'
                  $withLabel
                />
              </ExportInputStyled>
            </SettingsBlockStyled>

            <SettingsBlockStyled>
              <SettingsLabelStyled>
                {I18n.t('attendance.cutExport')}
              </SettingsLabelStyled>
              <ExportInputStyled>
                <ExportInputLabelStyled>G-</ExportInputLabelStyled>
                <SettingsInputStyled
                  value={cutExport}
                  onChange={e => {
                    const value = e.target.value

                    if (!value || value.length <= 3) {
                      setAttendanceSettings(state => ({
                        ...state,
                        cutExport: value
                      }))
                    }
                  }}
                  readOnly={false}
                  type='number'
                  $withLabel
                />
              </ExportInputStyled>
            </SettingsBlockStyled>

            <SettingsBlockStyled>
              <SettingsLabelStyled>
                {I18n.t('attendance.declarationExport')}
              </SettingsLabelStyled>
              <ExportInputStyled>
                <ExportInputLabelStyled>G-</ExportInputLabelStyled>
                <SettingsInputStyled
                  // readOnly={false}
                  value={declarationExport}
                  onChange={e => {
                    const value = e.target.value

                    if (!value || value.length <= 3) {
                      setAttendanceSettings(state => ({
                        ...state,
                        declarationExport: value
                      }))
                    }
                  }}
                  type='number'
                  $withLabel
                />
              </ExportInputStyled>
            </SettingsBlockStyled>
          </SettingsContainerRowStyled>
        </CardStyled>

        <CardStyled>
          <CardTitleStyled>{I18n.t('attendance.export')}</CardTitleStyled>
          <RolesHeaderStyled>
            <p>{I18n.t('attendance.roles')}</p>
            <p>{I18n.t('attendance.salaryExport')}</p>
            <p>{I18n.t('attendance.salaryType')}</p>
            <p>{I18n.t('attendance.value')} %</p>
            <p>{I18n.t('attendance.cutFormat')}</p>
          </RolesHeaderStyled>

          <RolesTableStyled>
            {Object.keys(jobs).map(positionId => {
              const { name } = jobs?.[positionId]

              const {
                salaryType = 'wage',
                percentage = '',
                cutFormat = '',
                salaryExport = ''
              } = positionSettings[positionId] || {}

              return (
                <RolesRowStyled key={positionId}>
                  <RolesRowNameStyled>{name}</RolesRowNameStyled>
                  <SettingsBlockStyled>
                    <ExportInputStyled>
                      <ExportInputLabelStyled>G-</ExportInputLabelStyled>
                      <SettingsInputStyled
                        value={salaryExport}
                        onChange={e => {
                          const value = e.target.value

                          if (!value || value.length <= 3) {
                            setAttendanceSettings(state => ({
                              ...state,
                              positionSettings: {
                                ...state.positionSettings,
                                [positionId]: {
                                  ...state.positionSettings[positionId],
                                  salaryExport: value
                                }
                              }
                            }))
                          }
                        }}
                        readOnly={false}
                        type='number'
                        $withLabel
                        $fullWidth
                      />
                    </ExportInputStyled>
                  </SettingsBlockStyled>
                  <CustomSelect
                    options={salaryTypeOptions}
                    value={salaryTypeOptions.find(
                      ({ value }) => value === salaryType
                    )}
                    onChange={option => {
                      const newValue = option as OptionType<SalaryType>

                      const updates: {
                        salaryType: SalaryType
                        percentage: string
                        cutFormat: CutFormat
                      } = {
                        salaryType: newValue.value,
                        cutFormat,
                        percentage
                      }
                      if (newValue.value !== 'wage_cut') {
                        updates.percentage = ''
                      }
                      if (newValue.value !== 'wage_tips') {
                        updates.cutFormat = ''
                      }
                      if (newValue.value === 'wage_tips' && !cutFormat) {
                        updates.cutFormat = 'cash'
                      }
                      setAttendanceSettings(state => ({
                        ...state,
                        positionSettings: {
                          ...state.positionSettings,
                          [positionId]: {
                            ...state.positionSettings[positionId],
                            ...updates
                          }
                        }
                      }))
                    }}
                    components={{
                      IndicatorSeparator: () => null
                    }}
                  />
                  <SettingsInputStyled
                    disabled={salaryType !== 'wage_cut'}
                    value={percentage}
                    placeholder='-'
                    onChange={e => {
                      const value = e.target.value

                      setAttendanceSettings(state => ({
                        ...state,
                        positionSettings: {
                          ...state.positionSettings,
                          [positionId]: {
                            ...state.positionSettings[positionId],
                            percentage: value
                          }
                        }
                      }))
                    }}
                    type='number'
                    max='100'
                  />
                  <CustomSelectStyled
                    isDisabled={salaryType !== 'wage_tips'}
                    options={cutFormatOptions}
                    value={cutFormatOptions.find(
                      ({ value }) => value === cutFormat
                    )}
                    onChange={option => {
                      const newValue = option as OptionType<CutFormat>

                      setAttendanceSettings(state => ({
                        ...state,
                        positionSettings: {
                          ...state.positionSettings,
                          [positionId]: {
                            ...state.positionSettings[positionId],
                            cutFormat: newValue.value
                          }
                        }
                      }))
                    }}
                    placeholder='-'
                    components={{
                      IndicatorSeparator: () => null
                    }}
                  />
                </RolesRowStyled>
              )
            })}
          </RolesTableStyled>
        </CardStyled>
      </ModalBodyStyled>
    </ModalStyled>
  )
}

export default PayrollSettingsModal

const ModalStyled = styled(Modal)`
  .modal-content {
    width: 55vw;
    max-width: 1200px;
    min-width: 600px;
  }
`

const ModalHeaderStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;

  width: 100%;
  padding: 0.8rem 1rem;

  position: relative;

  background-color: #69748f;
  border-radius: 0.8rem 0.8rem 0 0;

  color: #fff;
  font-family: ${theme.fonts.bold};
  font-size: ${theme.remFont(19)};
`
const CloseButtonStyled = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;

  height: ${theme.rem(30)};
  width: ${theme.rem(30)};
  padding: 0;

  position: absolute;
  right: ${theme.rem(15)};

  background-color: ${theme.colors.midGrey600};
  border: none;
  border-radius: 50%;
  img {
    height: ${theme.rem(13)};
    width: ${theme.rem(13)};
  }
  &:hover,
  &:focus {
    box-shadow: 0px 0px 10px -3px rgba(0, 0, 0, 0.4);
  }
`
const ModalBodyStyled = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;

  width: 100%;
  padding: 1.2rem;
  gap: 1rem;
  min-height: ${theme.rem(400)};
  max-height: 80vh;
  overflow: auto;
  &::-webkit-scrollbar {
    display: none;
  }
`

const CardStyled = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  width: 100%;
  padding: 0 1.2rem 1.2rem;

  background-color: #ecf1f5;
  border-radius: 0.8rem;
`
const CardTitleStyled = styled.div`
  min-width: 7rem;
  padding: 0.1rem 0.4rem;
  margin-bottom: 0.4rem;

  background-color: #d2dfe8;
  border-radius: 0 0 0.8rem 0.8rem;

  font-family: ${theme.fonts.bold};
  font-size: ${theme.remFont(16)};
  color: ${theme.colors.midGrey600};
`

const SettingsContainerRowStyled = styled.div`
  display: flex;
  justify-content: center;
  align-items: flex-start;

  gap: 0.75rem;
  width: 100%;
`

const SettingsBlockStyled = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;

  flex: 0.25;
`

const SettingsLabelStyled = styled.div`
  display: flex;
  align-items: center;

  margin-bottom: 0.1rem;
  margin-left: 0.5rem;

  color: ${theme.colors.midGrey600};
  font-size: ${theme.remFont(14)};
  font-family: ${theme.fonts.boldItalic};
`

const SettingsInputStyled = styled.input<{
  $withLabel?: boolean
  $fullWidth?: boolean
  $isInvalid?: boolean
  disabled?: boolean
  $isEmpty?: boolean
}>`
  width: 100%;
  height: ${theme.rem(45)};
  padding: 0
    ${({ $withLabel, $fullWidth }) =>
      $fullWidth ? '0.3rem 0 1.6rem' : $withLabel ? '2.2rem' : '0.4rem'};

  border: ${({ $isInvalid }) => `1px solid ${$isInvalid ? 'red' : '#c6d4dd'}`};
  border-radius: ${theme.rem(12)};
  /* background-color: #e9eff3; */
  background-color: #fff;
  opacity: ${({ disabled, $isEmpty }) => (disabled || $isEmpty ? 0.6 : null)};
  pointer-events: ${({ disabled }) => (disabled ? 'none' : null)};
  transition: 0.3s all;

  text-align: center;
  font-family: ${theme.fonts.bold};
  font-size: ${theme.remFont(16)};
  &:hover,
  &:focus {
    box-shadow: 0px 0px 10px -3px rgba(0, 0, 0, 0.4);
  }
  ::placeholder {
    font-family: ${theme.fonts.bold};
  }
`
const ExportInputStyled = styled.div`
  display: flex;
  align-items: center;
  position: relative;
`
const ExportInputLabelStyled = styled.div<{ $isOnRight?: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;

  width: 2.2rem;

  position: absolute;
  left: ${({ $isOnRight }) => ($isOnRight ? null : 0)};
  right: ${({ $isOnRight }) => ($isOnRight ? 0 : null)};
  pointer-events: none;

  font-family: ${theme.fonts.bold};
  font-size: ${theme.remFont(15)};
  color: ${theme.colors.midGrey600};
`

const NumberInputStyled = styled(NumberInput)`
  width: 100%;
  padding: ${theme.rem(6)};
  height: calc(${theme.rem(45)} + 1.5px);

  border: 1px solid #c6d4dd;
  border-radius: ${theme.rem(12)};
  background-color: #fff;
  /* background-color: #e9eff3; */
  box-shadow: unset;
  input {
    width: 100%;
    line-height: unset;
    font-size: ${theme.remFont(15)};
    font-family: ${theme.fonts.bold};
    opacity: 1;
  }

  div {
    border: none;
    span {
      width: ${theme.rem(32)} !important;
      height: ${theme.rem(32)} !important;
      padding: ${theme.rem(3)};
      margin-left: ${theme.rem(3)};

      background-color: #dbe3eb;
      border-radius: 6px;
      border: none;

      font-family: ${theme.fonts.bold};
    }
  }
`

const RolesHeaderStyled = styled.div`
  display: grid;
  align-items: center;

  width: 100%;
  grid-template-columns: 0.275fr 0.15fr 0.3fr 0.15fr 0.3fr;
  grid-gap: 0.75rem;
  padding: 0 1.5rem 0.1rem 1.5rem;

  p {
    color: ${theme.colors.midGrey600};
    font-family: ${theme.fonts.boldItalic};
    font-size: ${theme.remFont(15)};
    text-align: left;

    &:not(:first-child) {
      padding-left: 1rem;
    }
  }
  > *:first-child {
    text-align: left;
  }
`

const RolesTableStyled = styled.div`
  display: flex;
  align-items: flex-start;
  flex-direction: column;

  width: 100%;
  gap: 1rem;
`

const RolesRowStyled = styled(RolesHeaderStyled)`
  padding: 0.4rem 1rem 0.4rem 1.5rem;
  background: #d2dfe8;
  border-radius: ${theme.rem(16)};
`

const RolesRowNameStyled = styled.div`
  color: ${theme.colors.darkGrey};
  font-family: ${theme.fonts.heavy};
  font-size: ${theme.remFont(17)};
`

const PairedInputWrapStyled = styled.div`
  display: flex;

  height: 2.25rem;
  border: 1px solid #c6d4dd;
  border-radius: 0.6rem;
  background-color: #fff;
  ${ExportInputStyled} {
    &:first-child ${SettingsInputStyled} {
      border-right: 1px solid #c6d4dd;
      border-radius: 0.6rem 0 0 0.6rem;
    }
  }

  ${SettingsInputStyled} {
    height: 100%;
    padding: 0 2.2rem 0 1rem;

    background-color: transparent;
    border: none;
    border-radius: 0 0.6rem 0.6rem 0;
  }
`
const PairedInputLabelStyled = styled.div`
  position: absolute;
  right: 0.4rem;
  pointer-events: none;

  font-family: ${theme.fonts.bold};
  font-size: ${theme.remFont(15)};
  color: ${theme.colors.midGrey600};
`
const PairedInputLabeExtraStyled = styled(PairedInputLabelStyled)<{
  $isEmpty: boolean
}>`
  right: 1.8rem;
  opacity: ${({ $isEmpty }) => ($isEmpty ? 0.6 : null)};
`

const ArrowTopStyled = styled.div`
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 0 0.24rem 0.4rem 0.24rem;
  border-color: transparent transparent #baced8 transparent;
`

const RoundingTopButtonStyled = styled.div<{ $isActive: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2px;
  width: 0.8rem;
  height: 0.8rem;

  border: none;
  border-radius: 50%;
  background-color: ${({ $isActive }) =>
    $isActive ? theme.colors.green : 'transparent'};

  ${ArrowTopStyled} {
    border-bottom-color: ${({ $isActive }) => ($isActive ? '#b5f6e4' : null)};
  }
`

const ArrowDownStyled = styled.div`
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 0.4rem 0.24rem 0 0.24rem;
  border-color: #baced8 transparent transparent transparent;
`
const RoundingDownButtonStyled = styled.div<{ $isActive: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;

  padding: 2px;
  width: 0.8rem;
  height: 0.8rem;

  border: none;
  border-radius: 50%;
  background-color: ${({ $isActive }) =>
    $isActive ? theme.colors.red : 'transparent'};
  ${ArrowDownStyled} {
    border-top-color: ${({ $isActive }) => ($isActive ? '#ffe0e8' : null)};
  }
`

const RoundingSwitchStyled = styled.button<{ $isDisabled: boolean }>`
  display: flex;
  flex-direction: column;

  padding: 2px;

  position: absolute;
  right: 0.4rem;

  border: none;
  border-radius: 1rem;
  background-color: #ecf1f5;

  pointer-events: ${({ $isDisabled }) => ($isDisabled ? 'none' : null)};
`
const CustomSelectStyled = styled(CustomSelect)`
  .Select__placeholder {
    text-align: center;
  }
`
