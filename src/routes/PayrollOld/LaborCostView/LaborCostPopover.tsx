import React, { forwardRef, useEffect, useState } from 'react'
import Popover, { PopoverProps } from 'react-bootstrap/Popover'
import { I18n } from 'react-redux-i18n'

import dayjs from 'dayjs'
import _ from 'lodash'
import forEach from 'lodash/forEach'
import isEmpty from 'lodash/isEmpty'
import styled from 'styled-components'
import { theme } from 'styles/theme'

import { OutlineButton } from 'components/ui/OutlineButton'

import { roundTime } from '../payrollUtils'

import { SALARY_TYPE_HOURLY, SALARY_TYPE_YEARLY } from 'utils/constants'
import { getOverlapDuration, minutesToHours } from 'utils/time'

import type {
  AttendanceSettings,
  DailyCuts,
  IAllConflictingAttendanceShifts,
  IAttendanceEnhancedShift,
  SalesPerShift,
  WeeklyData
} from 'types/attendance'
import { IPositions } from 'types/company'
import { IEmployee } from 'types/employee'
import { ScheduleRaw, Shift } from 'types/schedule'

import checkIcon from 'img/icons/checkGreenRounded.svg'
import closeIcon from 'img/icons/closeWhite.svg'
import exclamationOrange from 'img/icons/exclamationOrange.svg'

type Props = PopoverProps & {
  onClose: () => void
  shifts: {
    [key: string]: IAttendanceEnhancedShift
  }
  jobs: IPositions
  date: string
  employee: IEmployee
  companyId: string
  shiftKey: string
  onDeleteShift: (shiftKey: string) => void
  setPopoverType: (type: string) => void
  attendanceSettings: AttendanceSettings
  defaultDuration: number
  allConflictingShifts: IAllConflictingAttendanceShifts
  salesPerShift: SalesPerShift
  weeklyData: WeeklyData
  dailyCuts: DailyCuts
  schedule: ScheduleRaw
  cutDistribution: AttendanceSettings['cutDistribution']
  cutGroups: AttendanceSettings['cutGroups']
  cutRoundingValue: AttendanceSettings['cutRoundingValue']
  cutRoundingType: AttendanceSettings['cutRoundingType']
  id: string
  isOnLeft: boolean
}

const LaborCostPopover = forwardRef<HTMLDivElement, Props>((props, ref) => {
  const {
    onClose,
    shifts,
    jobs,
    date,
    employee,
    companyId,
    shiftKey,
    onDeleteShift,
    setPopoverType,
    attendanceSettings,
    defaultDuration,
    allConflictingShifts,
    salesPerShift,
    weeklyData,
    dailyCuts,
    schedule,
    cutDistribution,
    cutGroups,
    cutRoundingValue,
    cutRoundingType,
    id,
    isOnLeft,
    ...rest
  } = props

  const [selectedShiftKey, setSelectedShiftKey] = useState<string | null>(null)
  const [copyOfShifts, setcopyOfShifts] = useState<{
    [key: string]: IAttendanceEnhancedShift
  }>({})

  useEffect(() => {
    if (shiftKey && shifts[shiftKey]) {
      setSelectedShiftKey(shiftKey)
      setcopyOfShifts(shifts)
    }
  }, [shiftKey, shifts])

  if (!employee || !selectedShiftKey) {
    return (
      <Popover
        id={id}
        ref={ref}
        {...rest}
      />
    )
  }

  const shift = copyOfShifts[selectedShiftKey]
  const {
    positionId = '',
    shiftLengthHours,
    overtimeDuration,
    salary,
    rate = 0,
    type = SALARY_TYPE_HOURLY,
    additionalSalary,
    totalDue,
    cutToReceive,
    totalSales,
    cashSales,
    tips,
    cutPercentageToShare
  } = shift

  const positionOptions = []
  const positions = employee.positions || employee.lastPositions || []
  _.uniqBy(positions, 'categoryId').forEach(position => {
    if (jobs && jobs[position.categoryId] && jobs[position.categoryId].name) {
      positionOptions.push({
        label: jobs[position.categoryId].name,
        value: position.categoryId
      })
    }
  })

  const shiftKeys = Object.keys(copyOfShifts)
  const shiftIndex = shiftKeys.indexOf(selectedShiftKey)

  let { roundingTime, positionSettings, cutPercentage } = attendanceSettings
  const tipsDeclaration = +attendanceSettings.tipsDeclaration || 0
  const cutToPayPercentage = +cutPercentage || 0
  const salaryType = positionSettings[positionId]?.salaryType || 'wage'

  const isPayingCut = salaryType === 'wage_tips'
  const isReceivingCut = salaryType === 'wage_cut'

  const { name = '' } = jobs?.[positionId] || {}
  const notClockedOut = shift.end === undefined

  const isToday = dayjs(date).isSame(dayjs(), 'day')

  const shiftStartRounded = roundTime(shift.start, roundingTime)
  const shiftEndRounded = notClockedOut
    ? null
    : roundTime(shift.end, roundingTime)
  const {
    isClockInDifferent = false,
    isClockOutDiffrent = false,
    neverClockedOut = false
  } = allConflictingShifts?.[date]?.[employee.uid]?.[selectedShiftKey] || {}
  const isWorking = isToday && !neverClockedOut && !shift.isConfirmed
  const isConflictingShift =
    isClockInDifferent ||
    isClockOutDiffrent ||
    !shift.positionId ||
    (neverClockedOut && !isWorking)
  let cutToPay = 0

  if (isPayingCut) {
    cutToPay = Math.round((cutToPayPercentage / 100) * totalSales * 100) / 100
  }

  const scheduledPositions = schedule?.[date]?.[employee.uid] || {}
  let scheduledShift: Shift | null = null

  if (!isEmpty(scheduledPositions)) {
    const dayShifts: Shift[] = []

    forEach(scheduledPositions, (subpositions, positionId) => {
      forEach(subpositions, (subpositionShifts, subcategoryId) => {
        forEach(subpositionShifts, (scheduledShift, shiftKey) => {
          dayShifts.push({
            ...scheduledShift,
            positionId,
            subcategoryId,
            shiftKey
          })
        })
      })
    })

    // find a shift from dayShifts that has highest overlap duration with current shift
    // using getOverlapDuration function to get the duration
    const shiftWithHighestOverlap = dayShifts.reduce(
      (
        acc: {
          shift: Shift | null
          overlapDuration: number
        },
        shift2
      ) => {
        if (shift.start === undefined) {
          return acc
        }
        const overlapDuration = getOverlapDuration(
          {
            ...shift,
            start: shift.start as number
          },
          shift2,
          defaultDuration
        )

        if (overlapDuration > acc.overlapDuration) {
          return {
            shift: shift2,
            overlapDuration
          }
        }

        return acc
      },
      {
        shift: null,
        overlapDuration: 0
      }
    )
    if (shiftWithHighestOverlap.overlapDuration > 0) {
      scheduledShift = shiftWithHighestOverlap.shift
    }
  }

  const totalSalary = salary
  let totalCut = 0

  if (isReceivingCut) {
    totalCut = Math.round(cutToReceive * 100) / 100
  }

  return (
    <PopoverStyled
      id={id}
      ref={ref}
      {...rest}
      $isOnLeft={isOnLeft}
    >
      <EditShiftTitleStyled>
        <DateStyled>
          {dayjs.locale() === 'fr'
            ? dayjs(date, 'YYYY-MM-DD').format('dddd, DD MMM')
            : dayjs(date, 'YYYY-MM-DD').format('dddd, MMM DD')}
        </DateStyled>
        <TitleStyled>
          {employee.name} {employee.surname}
        </TitleStyled>
        <button onClick={onClose}>
          <img
            src={closeIcon}
            alt=''
          />
        </button>
      </EditShiftTitleStyled>
      <EditShiftNumberStyled>
        {/* FIX add shift time */}
        {shiftKeys.length
          ? shiftKeys.map((shiftKey, i) => {
              const shift = copyOfShifts[shiftKey]
              const shiftStartRounded = roundTime(shift.start, roundingTime)
              const notClockedOut =
                shift.end === undefined || shift.end === null
              const shiftEndRounded = notClockedOut
                ? null
                : roundTime(shift.end, roundingTime)
              const {
                isClockInDifferent = false,
                isClockOutDiffrent = false,
                neverClockedOut = false
              } = allConflictingShifts?.[date]?.[employee.uid]?.[shiftKey] || {}

              const isWorking =
                isToday && !neverClockedOut && !shift.isConfirmed
              const isConflictingShift =
                isClockInDifferent ||
                isClockOutDiffrent ||
                !shift.positionId ||
                (neverClockedOut && !isWorking)
              return (
                <EditShiftNumberShiftStyled
                  isConflicting={false}
                  notClocked={false}
                  activeShift={shiftIndex === i}
                  onClick={() => setSelectedShiftKey(shiftKey)}
                  key={shiftKey}
                  $isDensed={shiftKeys.length > 2}
                >
                  {minutesToHours(shiftStartRounded)} -{' '}
                  {!neverClockedOut && shiftEndRounded !== null
                    ? minutesToHours(shiftEndRounded)
                    : ''}
                  {isConflictingShift && <ExclamationIconStyled />}
                  {neverClockedOut && (
                    <DeleteButtonStyled>
                      <CloseIconStyled
                        src={closeIcon}
                        alt=''
                      />
                    </DeleteButtonStyled>
                  )}
                </EditShiftNumberShiftStyled>
              )
            })
          : null}
      </EditShiftNumberStyled>

      <CardStyled>
        <CardHeaderStyled>
          {scheduledShift && (
            <>
              <HeaderBlockStyled>
                <ScheduledLabel>
                  {I18n.t('attendance.scheduledShift')}
                </ScheduledLabel>
                <ScheduledTime>
                  {minutesToHours(scheduledShift.start)} -{' '}
                  {scheduledShift.end !== null
                    ? minutesToHours(scheduledShift.end)
                    : ''}
                </ScheduledTime>
              </HeaderBlockStyled>
              <TriangleStyled />
            </>
          )}
          <HeaderBlockStyled
            isConflicting={isConflictingShift}
            notClocked={neverClockedOut}
          >
            <LabelStyled>{I18n.t('attendance.clockedShift')}</LabelStyled>
            <ValueStyled>
              {minutesToHours(shiftStartRounded)} -{' '}
              {!neverClockedOut && shiftEndRounded !== null
                ? minutesToHours(shiftEndRounded)
                : ''}
            </ValueStyled>
            {neverClockedOut ? (
              <DeleteButtonStyled>
                <CloseIconStyled
                  src={closeIcon}
                  alt=''
                />
              </DeleteButtonStyled>
            ) : isConflictingShift ? (
              <ExclamationIconStyled />
            ) : (
              <CheckIconStyled
                src={checkIcon}
                alt=''
              />
            )}
          </HeaderBlockStyled>
        </CardHeaderStyled>
        <CardBodyStyled>
          <BodyRowStyled>
            <RowLabelStyled>{I18n.t('attendance.role')}</RowLabelStyled>
            <RowValueStyled>{name}</RowValueStyled>
          </BodyRowStyled>
          <BodyRowStyled>
            <RowLabelStyled>{I18n.t('attendance.salary')}</RowLabelStyled>
            <RowValueStyled>
              {rate}$/
              {type === SALARY_TYPE_YEARLY
                ? I18n.t('common.year')
                : I18n.t('common.hour_shorten')}
            </RowValueStyled>
          </BodyRowStyled>
          <BodyRowStyled>
            <RowLabelStyled>{I18n.t('attendance.workedHours')}</RowLabelStyled>
            <RowValueStyled empty={!shiftLengthHours}>
              {shiftLengthHours}
              {type === SALARY_TYPE_HOURLY && Boolean(overtimeDuration) && (
                <span
                  style={{
                    color: '#FF0000',
                    width: 'unset',
                    fontSize: '0.8rem'
                  }}
                >
                  &nbsp;
                  {`(${overtimeDuration})`}
                  &nbsp;
                </span>
              )}
              h
            </RowValueStyled>
          </BodyRowStyled>
          <BodyRowStyled>
            <RowLabelStyled>{I18n.t('attendance.salaryMade')}</RowLabelStyled>
            <RowValueStyled empty={!salary}>
              {salary ? `${salary - +(additionalSalary || 0)}$` : '-'}
            </RowValueStyled>
          </BodyRowStyled>
          {Boolean(additionalSalary) && (
            <BodyRowStyled>
              <RowLabelStyled>{I18n.t('attendance.bonus')}</RowLabelStyled>
              <RowValueStyled empty={false}>{additionalSalary}$</RowValueStyled>
            </BodyRowStyled>
          )}
          {isPayingCut && (
            <>
              <BodyRowStyled>
                <RowLabelStyled>{I18n.t('attendance.sales')}</RowLabelStyled>
                <RowValueStyled>
                  {Math.round(totalSales * 100) / 100}$
                </RowValueStyled>
              </BodyRowStyled>
              <BodyRowStyled>
                <RowLabelStyled>
                  {I18n.t('attendance.cashSales')}
                </RowLabelStyled>
                <RowValueStyled>
                  {Math.round(cashSales * 100) / 100}$
                </RowValueStyled>
              </BodyRowStyled>
              <BodyRowStyled>
                <RowLabelStyled>{I18n.t('attendance.tips')}</RowLabelStyled>
                <RowValueStyled>
                  {tips > 0 && '-'}
                  {Math.round(tips * 100) / 100}$
                </RowValueStyled>
              </BodyRowStyled>
              <BodyRowStyled>
                <RowLabelStyled>
                  {I18n.t('attendance.cut')} ({cutToPayPercentage}%)
                </RowLabelStyled>
                <RowValueStyled>{cutToPay}$</RowValueStyled>
              </BodyRowStyled>
              <BodyRowStyled>
                <RowLabelStyled>
                  {I18n.t('attendance.declarationShort')} ({tipsDeclaration}%)
                </RowLabelStyled>
                <RowValueStyled>
                  {Math.round(totalSales * (tipsDeclaration / 100) * 100) / 100}
                  $
                </RowValueStyled>
              </BodyRowStyled>
            </>
          )}
          {isReceivingCut && (
            <>
              <BodyRowStyled>
                <RowLabelStyled>
                  {I18n.t('attendance.cut')} ({cutPercentageToShare}%)
                </RowLabelStyled>
                <RowValueStyled>{cutToReceive}$</RowValueStyled>
              </BodyRowStyled>
            </>
          )}
        </CardBodyStyled>

        <CardFooterStyled>
          <FooterBlockStyled>
            <LabelStyled>{I18n.t('attendance.totalSalary')}</LabelStyled>
            <ValueStyled notClocked={!totalSalary}>
              {salary ? `${totalSalary}$` : '-'}
            </ValueStyled>
          </FooterBlockStyled>
          {isPayingCut && (
            <FooterBlockStyled>
              <LabelStyled>{I18n.t('attendance.totalDue')}</LabelStyled>
              <ValueStyled>({totalDue}$)</ValueStyled>
            </FooterBlockStyled>
          )}
          {Boolean(totalCut) && (
            <FooterBlockStyled>
              <LabelStyled>{I18n.t('attendance.totalCut')}</LabelStyled>
              <ValueStyled>{totalCut}$</ValueStyled>
            </FooterBlockStyled>
          )}
        </CardFooterStyled>
      </CardStyled>
      <FooterStyled>
        <OutlineButton
          onClick={() => setPopoverType('timesheet')}
          color='blue'
        >
          {I18n.t('attendance.viewShift')}
        </OutlineButton>
      </FooterStyled>
    </PopoverStyled>
  )
})

export default LaborCostPopover

const PopoverStyled = styled(Popover)`
  width: 24rem;
  max-width: 24rem;
  padding: 1rem 0.75rem;

  /* hardocded absolute positioning */
  transform: unset !important;
  margin-right: ${({ $isOnLeft }) => ($isOnLeft ? '110%' : null)} !important;
  margin-left: ${({ $isOnLeft }) => ($isOnLeft ? null : '110%')} !important;
  inset: unset !important;
  top: 0 !important;
  left: ${({ $isOnLeft }) => ($isOnLeft ? null : '0')} !important;
  right: ${({ $isOnLeft }) => ($isOnLeft ? '0' : null)} !important;

  border: none;
  border-radius: 1rem;
  background-color: #69748f;
  &.bs-popover-left .arrow {
    top: 2rem !important;
    transform: unset !important;
    ::before,
    ::after {
      border-left-color: #69748f;
    }
  }
  &.bs-popover-right .arrow {
    top: 2rem !important;
    transform: unset !important;
    ::before,
    ::after {
      border-right-color: #69748f;
    }
  }
`

const EditShiftTitleStyled = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  position: relative;
  button {
    display: flex;
    align-items: center;
    justify-content: center;

    width: ${theme.rem(24)};
    height: ${theme.rem(24)};

    position: absolute;
    right: ${theme.rem(4)};
    top: ${theme.rem(-6)};

    border-radius: 50%;
    border: none;
    background-color: ${theme.colors.midGrey600};

    img {
      width: ${theme.rem(10)};
      height: ${theme.rem(10)};
    }
  }
`
const DateStyled = styled.span`
  color: #d9e1e9;
  font-size: ${theme.remFont(15)};
  font-family: ${theme.fonts.boldItalic};
`
const TitleStyled = styled.p`
  color: #fff;
  font-size: ${theme.remFont(19)};
  font-family: ${theme.fonts.bold};
  line-height: normal;
`

const EditShiftNumberStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;

  width: 100%;
  margin-top: 0.75rem;
  padding-inline: 0.6rem;
  gap: 0.5rem;
  position: relative;
`

const ExclamationIconStyled = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;

  width: 0.8rem;
  height: 0.8rem;

  position: absolute;
  top: 0.3rem;
  right: 0.3rem;

  background-image: url(${exclamationOrange});
  background-color: ${theme.colors.orange};
  background-size: 0.1rem;
  background-position: center;
  background-repeat: no-repeat;
  border-radius: 50%;
`

const DeleteButtonStyled = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;

  padding: 0.2rem;

  position: absolute;
  top: 0.3rem;
  right: 0.3rem;

  border: none;
  border-radius: 50%;
  background-color: ${theme.colors.red};
  /* &:focus {
    box-shadow: 0px 0px 7px rgba(205, 77, 81, 0.7);
    background-color: ${theme.colors.darkRed};
  } */
`

const EditShiftNumberShiftStyled = styled.div<{
  activeShift: boolean
  isConflicting: boolean
  notClocked: boolean
  $isDensed: boolean
}>`
  display: flex;
  align-items: center;
  justify-content: center;

  padding: 0.4rem ${({ $isDensed }) => ($isDensed ? '0.9rem' : '1.2rem')};

  position: relative;
  border-radius: 0.6rem 0.6rem 0 0;
  background-color: ${props =>
    props.activeShift && props.isConflicting
      ? theme.colors.lightOrange
      : props.activeShift && props.notClocked
        ? theme.colors.lightRed
        : props.activeShift
          ? theme.colors.midGrey600
          : 'transparent'};

  color: ${props =>
    props.activeShift && props.isConflicting
      ? theme.colors.orange
      : props.activeShift && props.notClocked
        ? theme.colors.red
        : props.activeShift
          ? '#fff'
          : '#d9e1e9'};
  font-family: ${theme.fonts.heavy};
  font-size: ${({ $isDensed }) => ($isDensed ? '0.85rem' : '0.9rem')};
  text-align: center;
  cursor: pointer;
  line-height: ${({ $isDensed }) => ($isDensed ? 'normal' : null)};
  &:after {
    content: ${({ activeShift }) => (activeShift ? "''" : 'unset')};
    width: 100%;
    height: 4px;
    position: absolute;
    bottom: 0px;
    background-color: ${theme.colors.blue};
  }
  &:hover {
    &:after {
      content: '';
    }
  }

  ${ExclamationIconStyled} {
    top: -0.45rem;
    right: unset;
  }
  ${DeleteButtonStyled} {
    top: 0.1rem;
    right: 0.1rem;
  }
`

const CloseIconStyled = styled.img`
  width: 0.4rem;
  height: 0.4rem;
`
const CheckIconStyled = styled.img`
  width: 0.8rem;
  height: 0.8rem;

  position: absolute;
  top: 0.3rem;
  right: 0.3rem;
`

const CardStyled = styled.div`
  display: flex;
  align-items: center;
  flex-direction: column;

  width: 100%;
  margin-bottom: 1rem;
  flex: 1;

  background-color: #e7eef4;
  border-radius: 1rem;
`

const CardHeaderStyled = styled.div`
  display: flex;
  align-items: center;

  width: 100%;
  padding: 0.75rem;
  min-height: 4.8rem;

  background-color: #d3e0e9;
  border-radius: 1rem;
`

const ScheduledLabel = styled.span`
  color: #9da8b9;
  font-size: ${theme.remFont(15)};
  font-family: ${theme.fonts.boldItalic};
`
const ScheduledTime = styled.p`
  color: #727e98;
  font-size: ${theme.remFont(16)};
  font-family: ${theme.fonts.heavyItalic};
  line-height: normal;
`
const LabelStyled = styled.span`
  color: #6e798d;
  font-size: ${theme.remFont(15)};
  font-family: ${theme.fonts.bold};
`
const ValueStyled = styled.p<{ notClocked?: boolean }>`
  color: ${({ notClocked }) =>
    notClocked ? theme.colors.red : theme.colors.darkGrey};
  font-size: ${theme.remFont(16)};
  font-family: ${theme.fonts.heavy};
  line-height: normal;
`
const HeaderBlockStyled = styled.div<{
  isConflicting?: boolean
  notClocked?: boolean
}>`
  display: flex;
  flex-direction: column;
  align-items: center;

  padding: 0.4rem;
  flex: 1;
  position: relative;

  border-radius: 0.8rem;
  &:last-child {
    background-color: ${props =>
      props.notClocked
        ? theme.colors.lightRed
        : props.isConflicting
          ? theme.colors.lightOrange
          : '#dde7f0'};
    ${ValueStyled} {
      color: ${props =>
        props.notClocked
          ? theme.colors.red
          : props.isConflicting
            ? theme.colors.orange
            : null};
    }
  }
`

const TriangleStyled = styled.div`
  margin: 0 0.4rem;
  width: 0;
  height: 0;

  border-style: solid;
  border-width: 0.4rem 0 0.4rem 0.6rem;
  border-color: transparent transparent transparent #9da8b9;
`
const CardBodyStyled = styled.div`
  display: flex;
  flex-direction: column;

  width: 100%;
  padding: 1rem;
`
const BodyRowStyled = styled.div`
  display: flex;
  align-items: center;

  width: 100%;
  gap: 1.75rem;

  &:nth-child(4) {
    margin-bottom: 0.75rem;
  }
`
const RowLabelStyled = styled.div`
  display: flex;
  justify-content: flex-end;
  flex: 1;

  color: #6e798d;
  font-size: ${theme.remFont(15)};
  font-family: ${theme.fonts.bold};
  line-height: normal;
`
const RowValueStyled = styled.p<{ empty?: boolean }>`
  display: flex;
  justify-content: flex-start;
  flex: 1;

  color: ${({ empty }) => (empty ? theme.colors.red : theme.colors.darkGrey)};
  font-size: ${theme.remFont(16)};
  font-family: ${theme.fonts.bold};
  line-height: normal;
`
const CardFooterStyled = styled(CardHeaderStyled)<{ empty?: boolean }>`
  ${ValueStyled} {
    color: ${({ empty }) => (empty ? theme.colors.red : null)};
  }
`
const FooterBlockStyled = styled.div`
  display: flex;
  align-items: center;
  flex-direction: column;

  flex: 1;
  height: 100%;

  &:not(:first-of-type) {
    border-left: 2px solid #9da8b9;
  }
`

const FooterStyled = styled.div`
  display: flex;
  justify-content: center;
  width: 100%;
`
