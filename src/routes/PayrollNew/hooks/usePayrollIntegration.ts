import { useEffect, useState } from 'react'

import { database } from '../../../index'

interface UsePayrollIntegrationReturn {
  coNumber: string
  integrationType: string
  isLoading: boolean
}

export const usePayrollIntegration = (
  companyKey: string
): UsePayrollIntegrationReturn => {
  const [coNumber, setCoNumber] = useState('')
  const [integrationType, setIntegrationType] = useState('')
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const fetchCoNumber = async (companyId: string) => {
      setIsLoading(true)

      const integrations = [
        { type: 'NETHRIS', path: `NethrisSettings/${companyId}/CO_NUMBER` },
        { type: 'EMPLOYEURD', path: `EmployerDSettings/${companyId}/CO_NUMBER` }
      ]

      try {
        for (const integration of integrations) {
          const coNumber = await database
            .ref(integration.path)
            .get()
            .then(snapshot => snapshot.val())

          if (coNumber) {
            setCoNumber(coNumber)
            setIntegrationType(integration.type)
            break
          }
        }
      } catch (error) {
        console.error('Failed to fetch CO_NUMBER:', error)
      } finally {
        setIsLoading(false)
      }
    }

    if (companyKey) {
      fetchCoNumber(companyKey)
    } else {
      setIsLoading(false)
    }
  }, [companyKey])

  return {
    coNumber,
    integrationType,
    isLoading
  }
}
