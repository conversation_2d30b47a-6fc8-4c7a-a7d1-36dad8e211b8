import { useEffect, useRef, useState } from 'react'

import { database } from '../../../index'

import { getDefaultStartOfPeriod } from 'utils/payroll/payrollPeriodUtils'

import { AttendanceSettings, OvertimeCalculationMode } from 'types/attendance'

interface UsePayrollSettingsReturn {
  attendanceSettings: AttendanceSettings
  setAttendanceSettings: React.Dispatch<
    React.SetStateAction<AttendanceSettings>
  >
  isLoading: boolean
}

export const usePayrollSettings = (
  companyKey: string,
  payrollStartingDay: string
): UsePayrollSettingsReturn => {
  const settingsLoaded = useRef(false)
  const [isLoading, setIsLoading] = useState(true)

  const [attendanceSettings, setAttendanceSettings] =
    useState<AttendanceSettings>({
      roundingTime: 5,
      positionSettings: {},
      startingWeek: getDefaultStartOfPeriod(payrollStartingDay),
      tipsDeclaration: '',
      tipsDistribution: 'daily',
      tipsExport: '',
      cutExport: '',
      overtimeExport: '',
      declarationExport: '',
      cutDistribution: 'daily' as AttendanceSettings['cutDistribution'],
      cutPercentage: '',
      cutGroups: [],
      cutRoundingValue: '',
      cutRoundingType: 'down' as AttendanceSettings['cutRoundingType'],
      payrollFrequency: 'biweekly',
      overtimeCalculationMode: 'weekly' as OvertimeCalculationMode
    })

  // Load attendance settings from database
  useEffect(() => {
    if (companyKey) {
      setIsLoading(true)
      database
        .ref('AttendanceSettings/' + companyKey)
        .once('value')
        .then(s => {
          const settings = s.val()

          if (settings) {
            setAttendanceSettings(state => ({
              ...state,
              ...settings
            }))
          }
          settingsLoaded.current = true
          setIsLoading(false)
        })
        .catch(() => {
          setIsLoading(false)
        })
    }
  }, [companyKey])

  // Persist attendance settings changes to database
  useEffect(() => {
    if (companyKey && settingsLoaded.current) {
      database
        .ref('AttendanceSettings/' + companyKey)
        .set(attendanceSettings)
        .catch(error => {
          console.error('Failed to save attendance settings:', error)
        })
    }
  }, [attendanceSettings, companyKey])

  return {
    attendanceSettings,
    setAttendanceSettings,
    isLoading
  }
}
