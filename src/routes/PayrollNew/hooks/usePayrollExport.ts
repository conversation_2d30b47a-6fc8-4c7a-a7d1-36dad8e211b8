import { useCallback } from 'react'

import { PayrollExportService } from '../services/payrollExportService'

import { ExportOption } from '../types/payrollTypes'

interface UsePayrollExportParams {
  coNumber: string
  integrationType: string
  onExportComplete?: () => void
}

interface UsePayrollExportReturn {
  handleExport: (
    option: string,
    format?: 'simplified' | 'detailed' | 'xls' | 'txt'
  ) => Promise<void>
  isExporting: boolean
}

export const usePayrollExport = ({
  coNumber,
  integrationType,
  onExportComplete
}: UsePayrollExportParams): UsePayrollExportReturn => {
  const handleExport = useCallback(
    async (
      option: string,
      exportFormat?: 'simplified' | 'detailed' | 'xls' | 'txt'
    ) => {
      try {
        await PayrollExportService.export({
          option: option as ExportOption['type'],
          format: exportFormat,
          coNumber,
          integrationType
        })

        onExportComplete?.()
      } catch (error) {
        console.error('Export error:', error)
        const errorMessage =
          error instanceof Error ? error.message : 'Unknown error occurred'
        throw new Error('Export failed: ' + errorMessage)
      }
    },
    [coNumber, integrationType, onExportComplete]
  )

  return {
    handleExport,
    isExporting: false // This could be enhanced with actual loading state
  }
}
