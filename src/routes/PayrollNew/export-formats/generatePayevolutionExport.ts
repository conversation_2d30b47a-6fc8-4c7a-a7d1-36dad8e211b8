import { formatShiftsPerWeek } from './utils/format-shifts-per-week'
import { splitShiftsPerWeek } from './utils/split-shifts-per-week'
import dayjs from 'dayjs'
import { IEmployee } from '../../../types/employee'
import {
  ExportDataRowType,
  OvertimeCalculationMode,
  IAttendanceEnhancedShifts
} from '../../../types/attendance'

type GeneratePayevolutionExportProps = {
  startAsDate: string
  employees: IEmployee[]
  shiftsByDay: IAttendanceEnhancedShifts
  isTwoWeekPayPeriod: boolean
}

export const generatePayevolutionExport = ({
  employees,
  shiftsByDay,
  startAsDate,
  isTwoWeekPayPeriod,
}: GeneratePayevolutionExportProps) => {
  const middleDate = dayjs(startAsDate, 'YYYY-MM-DD').add(7, 'day')

  const { shiftsOfFirstWeek, shiftsOfLastWeek } = splitShiftsPerWeek(
    shiftsByDay,
    middleDate
  )

  let employeesShiftsOfFirstWeek = formatShiftsPerWeek(
    employees,
    shiftsOfFirstWeek,
    1,
  )
  let employeesShiftsOfLastWeek = formatShiftsPerWeek(
    employees,
    shiftsOfLastWeek,
    2,
  )

  // Create table headers
  const formatedData: [ExportDataRowType[]] = [
    [
      {
        value: 'EmployeeID',
        type: 'string'
      },
      {
        value: 'TimeCode',
        type: 'string'
      },
      {
        value: 'TotalHours',
        type: 'string'
      },
      {
        value: 'HourRate',
        type: 'string'
      },
      {
        value: 'Total',
        type: 'string'
      },
      {
        value: 'WorkDate',
        type: 'string'
      },
      {
        value: 'WorkDateEnd',
        type: 'string'
      },
      {
        value: 'TimeIn',
        type: 'string'
      },
      {
        value: 'TimeOut',
        type: 'string'
      },
      {
        value: 'Authorized',
        type: 'string'
      },
      {
        value: 'Additional salary',
        type: 'string'
      }
    ]
  ]

  // Push each pay line to the table
  const pushPayLineToExcelExport = (
    employeeId: string,
    timeCode: 'R' | 'O',
    shiftInMinutes: number,
    date: string,
    rate: number,
    additionalSalary: number,
    authorized = true
  ) => {
    formatedData.push([
      {
        value: employeeId,
        type: 'string'
      },
      {
        value: timeCode,
        type: 'string'
      },
      {
        value: (shiftInMinutes / 60).toFixed(2),
        type: 'string'
      },
      {
        value: timeCode === 'R' ? rate : rate * 1.5,
        type: 'string'
      },
      {
        value: (
          (shiftInMinutes / 60) *
          (timeCode === 'R' ? rate : rate * 1.5)
        ).toFixed(2),
        type: 'number'
      },
      {
        value: date,
        type: 'string'
      },
      {
        value: '',
        type: 'string'
      },
      {
        value: '',
        type: 'string'
      },
      {
        value: '',
        type: 'string'
      },
      {
        value: authorized ? 'Y' : 'N',
        type: 'string'
      },
      {
        value: additionalSalary,
        type: 'string'
      }
    ])
  }

  // Loop for each employee and push the pay lines to the table
  const pushPayLinesPerWeek = (employeesShifts: any, date: string) => {
    for (const employeeId in employeesShifts) {
      const employeeData = employeesShifts[employeeId]
      for (const positionId in employeeData.pay) {
        const pay = employeeData.pay[positionId]

        pay.regularHours !== 0 &&
          pushPayLineToExcelExport(
            employeeData.payEvolutionId ?? '',
            'R',
            pay.regularHours,
            date,
            pay.rate,
            pay.additionalSalary,
            true
          )
        pay.overtimeHours !== 0 &&
          pushPayLineToExcelExport(
            employeeData.payEvolutionId ?? '',
            'O',
            pay.overtimeHours,
            date,
            pay.rate,
            pay.additionalSalary,
            true
          )
      }
    }
  }
  const formatedMiddleDate = middleDate.toISOString().split('T')[0]

  pushPayLinesPerWeek(employeesShiftsOfFirstWeek, startAsDate.toString())
  if (isTwoWeekPayPeriod) {
    pushPayLinesPerWeek(
      employeesShiftsOfLastWeek,
      formatedMiddleDate.toString()
    )
  }

  return formatedData
}
