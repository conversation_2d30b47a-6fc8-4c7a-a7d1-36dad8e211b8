import { SalaryRateUnit } from '../../../../../utils/constants'

export const mockDataFormatted = {
  employeesShifts: {
    // employee with hourly rate
    '-NqgSbSkHRB_FlLljzg3': {
      pay: {
        firstWeek: {
          '-Npz5aRQ6EVN-uC2Tmt_': {
            regularHours: 2460,
            overtimeDuration: 1,
            rate: 18,
            date: '2025-02-17',
            additionalSalary: 0,
            type: 'hourly' as SalaryRateUnit,
            paymentSectionCode: '001',
            numberOfShifts: 5,
            overtimeSectionCode: '043'
          }
        },
        secondWeek: {
          '-Npz5aRQ6EVN-uC2Tmt_': {
            regularHours: 2415,
            overtimeDuration: 0.25,
            rate: 18,
            date: '2025-02-24',
            additionalSalary: 0,
            type: 'hourly' as SalaryRateUnit,
            paymentSectionCode: '001',
            numberOfShifts: 5,
            overtimeSectionCode: '043'
          }
        }
      },
      shifts: {
        firstWeek: {
          '-OJKi2P33d3YCjhny__w': {
            additionalSalary: 0,
            date: '2025-02-17',
            type: 'hourly',
            shiftLengthHours: 8.25,
            shiftEndRounded: 15,
            shiftStartRounded: 960,
            overtimeDuration: 0,
            salary: 148.5,
            weekPeriod: 1,
            paymentSectionCode: '001'
          },
          '-OJOQ41Rt19XBeIhXrga': {
            additionalSalary: 0,
            rate: 18,
            type: 'hourly',
            overtimeDuration: 0,
            salary: 144,
            weekPeriod: 1,
            paymentSectionCode: '001'
          },
          '-OJTVqiknhaIs9nBqvpg': {
            additionalSalary: 0,
            date: '2025-02-19',
            rate: 18,
            type: 'hourly',
            shiftLengthHours: 8.25,
            shiftEndRounded: 1035,
            shiftStartRounded: 540,
            weekPeriod: 1,
            paymentSectionCode: '001'
          },
          '-OJYfFhXoJClUWJ9JwJB': {
            additionalSalary: 0,
            date: '2025-02-20',
            rate: 18,
            type: 'hourly',
            shiftLengthHours: 8.25,
            shiftEndRounded: 1035,
            shiftStartRounded: 540,
            weekPeriod: 1,
            paymentSectionCode: '001'
          },
          '-OJcoSI6VwIDN8S5Xgn8': {
            additionalSalary: 0,
            date: '2025-02-21',
            rate: 18,
            type: 'hourly',
            shiftLengthHours: 8.25,
            shiftEndRounded: 1035,
            shiftStartRounded: 540,
            weekPeriod: 1,
            paymentSectionCode: '001'
          },
          '-OJwzxgmEtlP_9eonwTJ': {
            additionalSalary: 0,
            date: '2025-02-25',
            rate: 18,
            type: 'hourly',
            shiftLengthHours: 8.25,
            shiftEndRounded: 915,
            shiftStartRounded: 420,
            weekPeriod: 1,
            paymentSectionCode: '001'
          }
        },
        secondWeek: {
          '-OJrpK8ssJv7B6WnXsHu': {
            additionalSalary: 0,
            date: '2025-02-24',
            type: 'hourly',
            shiftLengthHours: 8.25,
            shiftEndRounded: 915,
            shiftStartRounded: 420,
            weekPeriod: 1,
            paymentSectionCode: '001'
          },
          '-OK1FtxI-09gUoPOom3e': {
            additionalSalary: 0,
            date: '2025-02-26',
            rate: 18,
            type: 'hourly',
            shiftLengthHours: 7.5,
            shiftEndRounded: 915,
            shiftStartRounded: 465,
            weekPeriod: 2,
            paymentSectionCode: '001'
          },
          '-OK6Gu_kjLFx6zKKN9m9': {
            additionalSalary: 0,
            date: '2025-02-27',
            rate: 18,
            type: 'hourly',
            shiftLengthHours: 8.25,
            shiftEndRounded: 915,
            shiftStartRounded: 420,
            weekPeriod: 2,
            paymentSectionCode: '001'
          },
          '-OKBPga-D38c9GcDTpSL': {
            additionalSalary: 0,
            date: '2025-02-28',
            rate: 18,
            type: 'hourly',
            shiftLengthHours: 8.25,
            shiftEndRounded: 915,
            shiftStartRounded: 420,
            weekPeriod: 2,
            paymentSectionCode: '001'
          }
        }
      },
      infos: {
        companyId: '-NpyzByw2JNlmHPJlemx',
        customEmplNumber: '91',
        name: 'Adam',
        surname: 'Omende',
        customId: '91',
        payrollId: '358'
      }
    },
    // employee with yearly salary
    '-OHTna58_3MIPkBStAYP': {
      pay: {
        firstWeek: {
          '-Npz2TiWCgjIOtRNCriZ': {
            regularHours: 1185,
            overtimeDuration: 0,
            rate: 50000,
            date: '2025-02-16',
            additionalSalary: 0,
            type: 'yearly' as SalaryRateUnit,
            paymentSectionCode: '001',
            numberOfShifts: 4,
            overtimeSectionCode: '043'
          }
        },
        secondWeek: {
          '-Npz2TiWCgjIOtRNCriZ': {
            regularHours: 990,
            overtimeDuration: 0,
            rate: 50000,
            date: '2025-02-23',
            additionalSalary: 0,
            type: 'yearly' as SalaryRateUnit,
            paymentSectionCode: '001',
            numberOfShifts: 3,
            overtimeSectionCode: '043'
          }
        }
      },
      shifts: {
        firstWeek: {
          '-OJFlrQUa5TFH3t6Nm91': {
            additionalSalary: 0,
            date: '2025-02-16',
            rate: 50000,
            shiftLengthHours: 7.5,
            shiftEndRounded: 915,
            shiftStartRounded: 465,
            overtimeDuration: 0,
            salary: 135,
            weekPeriod: 2,
            paymentSectionCode: '001'
          },
          '-OK6Gu_kjLFx6zKKN9m9': {
            additionalSalary: 0,
            date: '2025-02-27',
            rate: 50000,
            shiftLengthHours: 8.25,
            shiftEndRounded: 915,
            shiftStartRounded: 420,
            overtimeDuration: 0,
            salary: 148.5,
            weekPeriod: 2,
            paymentSectionCode: '001'
          },
          '-OKBPga-D38c9GcDTpSL': {
            additionalSalary: 0,
            date: '2025-02-28',
            rate: 50000,
            shiftLengthHours: 8.25,
            shiftEndRounded: 915,
            shiftStartRounded: 420,
            overtimeDuration: 0,
            salary: 150.75,
            weekPeriod: 2,
            paymentSectionCode: '001'
          }
        }
      },
      infos: {
        companyId: '-NpyzByw2JNlmHPJlemx',
        customEmplNumber: '91',
        name: 'Ben',
        surname: 'Test',
        customId: '91',
        payrollId: '358'
      }
    },
  },
  CO_NUMBER: '00239100',
  endAsDate: '2025-03-01',
  positionSettingsFlat: new Set(['001', '200']),
  isTwoWeekPayPeriod: true,
  overtimeSection: '043',
  maxHoursPerWeek: 40
}
