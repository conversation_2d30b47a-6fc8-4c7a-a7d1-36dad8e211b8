import { getShiftInMinute } from './get-shift-in-minutes'

import { SALARY_TYPE_YEARLY, SalaryRateUnit } from '../../../../utils/constants'

import {
  AttendanceSettings,
  EmployeesExportData,
  PayInterface,
  ShiftsPerWeek
} from '../../../../types/attendance'
import { IEmployee } from '../../../../types/employee'

export const formatShiftsPerWeek = (
  employees: IEmployee[],
  shiftsPerWeek: ShiftsPerWeek,
  week: 1 | 2,
  paymentSections?: AttendanceSettings['positionSettings'],
  overtimeSection?: string
): EmployeesExportData => {
  // Create a list of employees by their uid
  let employeesShifts: EmployeesExportData = {}
  employees.forEach((employee: IEmployee) => {
    employeesShifts[employee.uid] = {
      pay: {},
      shifts: {},
      payEvolutionId:
        employee.customId ?? `${employee.name} - ${employee.surname}`,
      infos: {
        name: employee.name,
        surname: employee.surname,
        companyId: employee.companyId,
        customId: employee.customId
      }
    }
  })

  // Assign shifts to employees
  Object.entries(shiftsPerWeek).forEach(([date, shifts]) => {
    for (const employeeId in shifts) {
      const employeeShifts = shifts[employeeId]

      // Add date to each employee shift children
      for (const shift in employeeShifts) {
        employeeShifts[shift].date = date
        employeeShifts[shift].weekPeriod = week
        employeeShifts[shift].paymentSectionCode =
          (employeeShifts?.[shift]?.positionId &&
            paymentSections?.[employeeShifts[shift].positionId]
              ?.salaryExport) ||
          ''
      }

      if (!employeesShifts[employeeId]) {
        employeesShifts[employeeId] = {
          pay: {},
          shifts: {}
        }
      }
      if (employeesShifts[employeeId].shifts) {
        Object.assign(employeesShifts[employeeId].shifts, employeeShifts)
      }
    }
  })

  // Find latest yearly salary rates for each employee and position
  const latestYearlySalaries: {
    [employeeId: string]: { [positionId: string]: number }
  } = {}

  // Track the latest dates for each employee and position
  const latestSalaryDates: {
    [employeeId: string]: { [positionId: string]: string }
  } = {}

  for (const employeeId in employeesShifts) {
    latestYearlySalaries[employeeId] = {}
    latestSalaryDates[employeeId] = {}

    for (const shiftId in employeesShifts[employeeId].shifts) {
      const shift = employeesShifts[employeeId].shifts[shiftId]

      if (shift.type === SALARY_TYPE_YEARLY && shift.positionId && shift.date) {
        // Check if we haven't seen this position yet or if this shift's date is more recent
        if (
          !latestSalaryDates[employeeId][shift.positionId] ||
          shift.date > latestSalaryDates[employeeId][shift.positionId]
        ) {
          // Update to the latest salary rate by date
          latestYearlySalaries[employeeId][shift.positionId] = +(
            shift.rate || 0
          )
          latestSalaryDates[employeeId][shift.positionId] = shift.date
        }
      }
    }
  }

  // Loop for each employee and calculate the total of hours worked
  for (const employeeId in employeesShifts) {
    const payByJob: PayInterface = {}

    // First pass: update yearly salaries to the latest value
    for (const shiftId in employeesShifts[employeeId].shifts) {
      const shift = employeesShifts[employeeId].shifts[shiftId]

      if (
        shift.type === SALARY_TYPE_YEARLY &&
        shift.positionId &&
        latestYearlySalaries[employeeId][shift.positionId]
      ) {
        // Update to the latest salary rate for the week
        shift.rate = latestYearlySalaries[employeeId][shift.positionId]
      }
    }

    // Second pass: process shifts with updated rates
    for (const shiftId in employeesShifts[employeeId].shifts) {
      const shift = employeesShifts[employeeId].shifts[shiftId]

      // For yearly salary type, use the positionId as the key instead of positionId_rate
      // This ensures all yearly shifts for the same position are grouped together
      const positionRateKey =
        shift.type === SALARY_TYPE_YEARLY
          ? shift.positionId
          : `${shift.positionId}_${shift.rate}`

      if (!payByJob.hasOwnProperty(positionRateKey)) {
        payByJob[positionRateKey] = {
          regularHours: 0,
          overtimeDuration: 0,
          rate: +(shift.rate || 0),
          date: shift.date,
          additionalSalary: 0,
          type: shift.type as SalaryRateUnit,
          paymentSectionCode: '',
          numberOfShifts: 0
        }
      }
      payByJob[positionRateKey].additionalSalary +=
        shift.additionalSalary as unknown as number

      if (paymentSections)
        payByJob[positionRateKey].paymentSectionCode =
          paymentSections[shift.positionId].salaryExport ?? ''

      if (overtimeSection)
        payByJob[positionRateKey].overtimeSectionCode = overtimeSection || ''

      const shiftInMinutes = getShiftInMinute(shift)

      payByJob[positionRateKey].regularHours += shiftInMinutes
      // Don't accumulate overtime for yearly salary employees
      payByJob[positionRateKey].overtimeDuration += 
        shift.type === SALARY_TYPE_YEARLY ? 0 : shift.overtimeDuration

      payByJob[positionRateKey].numberOfShifts++
    }

    Object.assign(employeesShifts[employeeId].pay, payByJob)
  }
  return employeesShifts
}
