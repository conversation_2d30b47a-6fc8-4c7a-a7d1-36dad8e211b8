import { IEmployee } from 'types/employee'

type payrollShifts = {
  [key: string]: {
    pay: { firstWeek: any; secondWeek: any }
    shifts: { firstWeek: {}; secondWeek: {} }
    infos: IEmployee
  }
}

export const groupShiftsByEmployees = (
  employees: IEmployee[],
  firstWeek: any,
  secondWeek: any
): payrollShifts => {
  const employeesShifts: payrollShifts = {}

  employees.forEach(employee => {
    const firstWeekData = firstWeek[employee.uid]
    const secondWeekData = secondWeek[employee.uid]

    const isFirstWeekNotEmpty = Object.keys(firstWeekData.shifts).length > 0
    const isSecondWeekNotEmpty = Object.keys(secondWeekData.shifts).length > 0

    if (isFirstWeekNotEmpty || isSecondWeekNotEmpty) {
      const pay = {
        firstWeek: firstWeekData.pay,
        secondWeek: secondWeekData.pay
      }
      const shifts = {
        firstWeek: firstWeekData.shifts,
        secondWeek: secondWeekData.shifts
      }

      employeesShifts[employee.uid] = {
        pay,
        shifts,
        infos: employee
      }
    }
  })
  return employeesShifts
}
