import dayjs from 'dayjs'

import { formatShiftsPerWeek } from './format-shifts-per-week'
import { getShiftsForThePeriod } from './get-shifts-for-the-period'
import { groupShiftsByEmployees } from './group-shifts-by-employees'
import { splitShiftsPerWeek } from './split-shifts-per-week'

import { SalaryRateUnit } from 'utils/constants'

import {
  AttendanceSettings,
  IAttendanceEnhancedShifts,
  PayInterfaceContent
} from 'types/attendance'
import { IEmployee } from 'types/employee'

export type ProcessedShiftsData = {
  [employeeUid: string]: {
    pay: { firstWeek: any; secondWeek: any }
    shifts: { firstWeek: {}; secondWeek: {} }
    infos: IEmployee
  }
}

export type GroupedShift = {
  totalRegularHours: number
  totalOvertimeDuration: number
  totalAdditionalSalary: number
  rate: number
  type: SalaryRateUnit
  paymentSectionCode: string
  // Additional properties for backward compatibility with Nethris export
  regularHours: number
  overtimeDuration: number
  additionalSalary: number
}

/**
 * Common export processing pipeline used by both Powerpay and Nethris exports
 * This extracts the duplicated logic from both files
 */
export const processShiftsForExport = ({
  employees,
  shiftsByDay,
  startAsDate,
  isTwoWeekPayPeriod,
  positionSettings,
  overtimeSection
}: {
  employees: IEmployee[]
  shiftsByDay: IAttendanceEnhancedShifts
  startAsDate: string
  isTwoWeekPayPeriod: boolean
  positionSettings: AttendanceSettings['positionSettings']
  overtimeSection: string
}): ProcessedShiftsData => {
  const middleDate = dayjs(startAsDate, 'YYYY-MM-DD').add(7, 'day')

  const shiftsForThePeriod = getShiftsForThePeriod(
    shiftsByDay,
    startAsDate,
    isTwoWeekPayPeriod
  )

  const { shiftsOfFirstWeek, shiftsOfLastWeek } = splitShiftsPerWeek(
    shiftsForThePeriod,
    middleDate
  )

  const employeesShiftsOfFirstWeek = formatShiftsPerWeek(
    employees,
    shiftsOfFirstWeek,
    1,
    positionSettings,
    overtimeSection
  )

  const employeesShiftsOfLastWeek = formatShiftsPerWeek(
    employees,
    shiftsOfLastWeek,
    2,
    positionSettings,
    overtimeSection
  )

  return groupShiftsByEmployees(
    employees,
    employeesShiftsOfFirstWeek,
    employeesShiftsOfLastWeek
  )
}

/**
 * Groups shifts by position code and rate to avoid duplicate entries
 * This logic is duplicated in both Powerpay and Nethris exports
 */
export const groupShiftsByPositionAndRate = (
  shiftsOfTheWeek: any
): Record<string, GroupedShift> => {
  const groupedShifts: Record<string, GroupedShift> = {}

  // First pass: Group shifts by position and rate
  for (const shiftKey in shiftsOfTheWeek) {
    const shiftPayInfos = shiftsOfTheWeek[shiftKey] as PayInterfaceContent

    // Create a composite key from position code and rate
    const groupKey = `${shiftPayInfos.paymentSectionCode}_${shiftPayInfos.rate}`

    if (!groupedShifts[groupKey]) {
      groupedShifts[groupKey] = {
        totalRegularHours: 0,
        totalOvertimeDuration: 0,
        totalAdditionalSalary: 0,
        rate: shiftPayInfos.rate,
        type: shiftPayInfos.type as SalaryRateUnit,
        paymentSectionCode: shiftPayInfos.paymentSectionCode,
        // Set backward compatibility properties to match totals
        regularHours: 0,
        overtimeDuration: 0,
        additionalSalary: 0
      }
    }

    // Sum up the hours, overtime, and additional salary
    groupedShifts[groupKey].totalRegularHours += shiftPayInfos.regularHours
    groupedShifts[groupKey].totalOvertimeDuration +=
      shiftPayInfos.overtimeDuration || 0
    groupedShifts[groupKey].totalAdditionalSalary += +(
      shiftPayInfos.additionalSalary || 0
    )
  }

  // Set backward compatibility properties to match totals
  for (const groupKey in groupedShifts) {
    const group = groupedShifts[groupKey]
    group.regularHours = group.totalRegularHours
    group.overtimeDuration = group.totalOvertimeDuration
    group.additionalSalary = group.totalAdditionalSalary
  }

  return groupedShifts
}

/**
 * Get the weeks to iterate based on pay period type
 * Common logic used in both exports
 */
export const getWeeksToIterate = (
  isTwoWeekPayPeriod: boolean
): Array<'firstWeek' | 'secondWeek'> => {
  return isTwoWeekPayPeriod ? ['firstWeek', 'secondWeek'] : ['firstWeek']
}
