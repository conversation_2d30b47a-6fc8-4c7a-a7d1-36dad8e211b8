import { IAttendanceEnhancedShift } from '../../../../types/attendance'

export const getShiftInMinute = (shift: IAttendanceEnhancedShift) => {
  if (shift.shiftLengthHours !== 0) {
    return shift.shiftLengthHours * 60
  }
  if (shift.shiftStartRounded === undefined || shift.shiftEndRounded === undefined) {
    return 0
  }
  if (shift.shiftEndRounded > shift.shiftStartRounded) {
    return shift.shiftEndRounded - shift.shiftStartRounded
  }
  if (shift.shiftEndRounded === shift.shiftStartRounded) {
    return 0
  }
  return 1440 - shift.shiftStartRounded + shift.shiftEndRounded
}