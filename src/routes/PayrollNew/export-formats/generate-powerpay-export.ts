import { I18n } from 'react-redux-i18n'
import { toast } from 'react-toastify'

import dayjs from 'dayjs'
import { XMLBuilder } from 'fast-xml-parser'

import {
  getWeeksToIterate,
  groupShiftsByPositionAndRate,
  processShiftsForExport
} from './utils/common-export-processing'
import { getSalaryRate } from './utils/get-salary-rate'
import { POWERPAY_EARNING_MODE } from 'utils/constants'

import { AttendanceSettings, IAttendanceEnhancedShifts } from 'types/attendance'
import { IEmployee } from 'types/employee'

type GeneratePowerpayExportProps = {
  employees: IEmployee[]
  shiftsByDay: IAttendanceEnhancedShifts
  startAsDate: string
  endAsDate: string
  isTwoWeekPayPeriod: boolean
  positionSettings: AttendanceSettings['positionSettings']
  overtimeSection: string
  maxHoursPerWeek: number
  payrollNumber: string
  branchId: string
  additionalSalarySection?: string
  powerpayEarningModePerPosition: Record<string, POWERPAY_EARNING_MODE>
}

const escapeXml = (unsafe: string): string => {
  return unsafe
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;')
}

const formatDate = (date: string): string => {
  return dayjs(date).format('YYYY-MM-DD')
}

const formatDateTime = (date: string): string => {
  return dayjs(date).format('YYYY-MM-DDTHH:mm:ss')
}

export const generatePowerpayExport = ({
  employees,
  shiftsByDay,
  startAsDate,
  endAsDate,
  isTwoWeekPayPeriod,
  positionSettings,
  overtimeSection,
  additionalSalarySection = 'X1',
  maxHoursPerWeek,
  powerpayEarningModePerPosition,
  payrollNumber,
  branchId
}: GeneratePowerpayExportProps): string => {
  const currentDateTime = formatDateTime(dayjs().toISOString())
  const endDateFormatted = formatDate(endAsDate)

  const paymentSectionToPositionId: Record<string, string> = {}
  for (const [positionId, settings] of Object.entries(positionSettings)) {
    if (settings.salaryExport) {
      paymentSectionToPositionId[settings.salaryExport] = positionId
    }
  }

  const groupedShiftsByEmployees = processShiftsForExport({
    employees,
    shiftsByDay,
    startAsDate,
    isTwoWeekPayPeriod,
    positionSettings,
    overtimeSection
  })

  // Check if all employees with shifts have a powerpayId
  const employeesWithoutPowerpayId = employees.filter(
    employee => !employee.powerpayId
  )

  const allEmployeesHavePowerpayId = employeesWithoutPowerpayId.length === 0
  if (!allEmployeesHavePowerpayId) {
    const errorText = I18n.t(
      'attendance.powerpay.all_employees_must_have_a_powerpay_id'
    )
    if (employeesWithoutPowerpayId.length <= 5) {
      toast.error(
        `${errorText} Employees without powerpayId: ${employeesWithoutPowerpayId
          .map(employee => employee.name)
          .join(', ')}`
      )
    } else {
      toast.error(errorText)
    }
    return ''
  }

  // Create XML builder with proper configuration
  const builder = new XMLBuilder({
    ignoreAttributes: false,
    attributeNamePrefix: '@_',
    suppressEmptyNode: true,
    format: true,
    indentBy: '    '
  })

  // Build the XML structure
  const xmlObj: any = {
    'import:payroll_import': {
      '@_xmlns:import': 'http://www.ceridian.ca/payrollimport',
      '@_xmlns:xsi': 'http://www.w3.org/2001/XMLSchema-instance',
      '@_xsi:schemaLocation':
        'http://www.ceridian.ca/payrollimport payrollImport.xsd',
      source: {
        created_by: 'PIVOT',
        creation_date: currentDateTime,
        error_email: '<EMAIL>'
      },
      target: {
        product: 'PPAY'
      },
      payroll: {
        branch_id: escapeXml(branchId),
        payroll_number: escapeXml(payrollNumber)
      },
      payperiod: {
        end_date: endDateFormatted,
        payperiod_type: 'R'
      },
      employee: []
    }
  }

  // Generate employee entries
  const weeksToIterate = getWeeksToIterate(isTwoWeekPayPeriod)

  for (const employeeUid in groupedShiftsByEmployees) {
    const employeeShift = groupedShiftsByEmployees[employeeUid]
    const employee = employeeShift.infos

    const employeeObj: any = {
      employee_number: escapeXml(employee.payrollId || employee.customId || ''),
      timesheet: {
        applicable_period: '1',
        timesheet_entry: []
      }
    }

    // Process shifts for each week
    for (const weekKey of weeksToIterate) {
      const currentWeek = weekKey as 'firstWeek' | 'secondWeek'
      const shiftsOfTheWeek = employeeShift.pay[currentWeek]

      // Use common grouping logic
      const groupedShifts = groupShiftsByPositionAndRate(shiftsOfTheWeek)

      // Second pass: Create timesheet entries for each group
      for (const groupKey in groupedShifts) {
        const group = groupedShifts[groupKey]
        const currentPaymentSectionCode = group.paymentSectionCode
        const shiftDuration = group.totalRegularHours
          ? group.totalRegularHours / 60
          : 0

        const salaryRate = getSalaryRate(
          group.type,
          group.rate,
          maxHoursPerWeek
        )

        // Regular hours entry
        const hasRegularHours = group.totalRegularHours > 0
        const hasValidRate = group.rate !== 0
        if (hasRegularHours && hasValidRate) {
          const regularHours =
            group.type === 'yearly'
              ? maxHoursPerWeek
              : shiftDuration - group.totalOvertimeDuration / 60

          if (regularHours > 0) {
            const regularEntry: any = {
              earning_code: currentPaymentSectionCode,
              for_pay_period_ending: endDateFormatted
            }

            // Get the position ID from the payment section code
            const positionId =
              paymentSectionToPositionId[currentPaymentSectionCode]
            const earningMode = positionId
              ? powerpayEarningModePerPosition[positionId]
              : undefined

            if (earningMode === POWERPAY_EARNING_MODE.AMOUNT) {
              regularEntry['amount'] = (regularHours * salaryRate).toFixed(2)
            } else {
              regularEntry['hours'] = regularHours.toFixed(2)

              // Only add exception_rate when using hours mode
              if (group.rate !== salaryRate) {
                regularEntry['exception_rate'] = salaryRate.toFixed(2)
              }
            }

            employeeObj.timesheet.timesheet_entry.push(regularEntry)
          }
        }

        // Overtime entry
        const hasOvertime = group.totalOvertimeDuration > 0
        if (hasOvertime && hasValidRate) {
          const overtimeHours = group.totalOvertimeDuration / 60
          const overtimeRate = salaryRate * 1.5

          const overtimeEntry: any = {
            earning_code: overtimeSection,
            for_pay_period_ending: endDateFormatted
          }

          // Get the position ID from the payment section code
          const positionId =
            paymentSectionToPositionId[currentPaymentSectionCode]
          const earningMode = positionId
            ? powerpayEarningModePerPosition[positionId]
            : undefined

          if (earningMode === POWERPAY_EARNING_MODE.AMOUNT) {
            overtimeEntry['amount'] = (overtimeHours * overtimeRate).toFixed(2)
          } else {
            overtimeEntry['hours'] = overtimeHours.toFixed(2)
            overtimeEntry['exception_rate'] = overtimeRate.toFixed(2)
          }

          employeeObj.timesheet.timesheet_entry.push(overtimeEntry)
        }

        // Additional salary entry
        if (group.totalAdditionalSalary > 0) {
          employeeObj.timesheet.timesheet_entry.push({
            earning_code: additionalSalarySection,
            amount: group.totalAdditionalSalary.toFixed(2),
            for_pay_period_ending: endDateFormatted
          })
        }
      }
    }

    xmlObj['import:payroll_import'].employee.push(employeeObj)
  }

  // Generate the XML string
  const xml = builder.build(xmlObj)
  return xml
}
