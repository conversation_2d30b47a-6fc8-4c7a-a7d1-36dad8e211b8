import { I18n } from 'react-redux-i18n'

import { SalaryRateUnit } from '../../../utils/constants'
import {
  ProcessedShiftsData,
  getWeeksToIterate,
  groupShiftsByPositionAndRate,
  processShiftsForExport
} from './utils/common-export-processing'
import { getSalaryRate } from './utils/get-salary-rate'
import PadLeft from './utils/pad-left'

import {
  AttendanceSettings,
  ExportDataRowType
} from '../../../types/attendance'
import { IEmployee } from '../../../types/employee'

export const positionsCodesWithoutRateAndQuantity = ['200', '232', '514'] // 514: self-employed, 232: overtime

export type generateNethrisExportProps = {
  employees: IEmployee[]
  shiftsByDay: {}
  startAsDate: string
  positionSettings: AttendanceSettings['positionSettings']
  CO_NUMBER: string
  overtimeSection: string
  endAsDate: string
  isTwoWeekPayPeriod: boolean
  companyId: string
  maxHoursPerWeek: number
}

export const generateNethrisExport = ({
  employees,
  shiftsByDay,
  startAsDate,
  positionSettings,
  CO_NUMBER = 'TEST',
  overtimeSection,
  endAsDate,
  isTwoWeekPayPeriod,
  companyId,
  maxHoursPerWeek
}: generateNethrisExportProps) => {
  const positionSettingsFlat = new Set<string>()

  Object.values(positionSettings).forEach(section => {
    section.salaryExport && positionSettingsFlat.add(section.salaryExport)
  })

  if (CO_NUMBER.length < 8) {
    CO_NUMBER = PadLeft(CO_NUMBER)
  }

  const isRandolphCompany = () => {
    const randolphCompanies = [
      '-NSMXau2N31RgOxwTzO0',
      '-NfCbOmDWXnHFjTuHdKI',
      '-NshkTMgZZJNulf0FMRI',
      '-Nshos7eyi05pEGv7CKK',
      '-NshnzsaGrnGsgwPpNPL',
      '-NshmUC5QzCBSz_SgQsL',
      '-NeJ12Us3gNcOEDoee8M',
      '-Ne3yXa_JiM0G-yYmP67'
    ]
    return randolphCompanies.includes(companyId)
  }

  const groupedShiftsByEmployees = processShiftsForExport({
    employees,
    shiftsByDay,
    startAsDate,
    isTwoWeekPayPeriod,
    positionSettings,
    overtimeSection
  })

  const excelHeader = generateExcelHeader(positionSettingsFlat, overtimeSection)

  const excelContent = isRandolphCompany()
    ? generateExcelContentForRandolphRestaurants(
        groupedShiftsByEmployees,
        CO_NUMBER,
        endAsDate,
        positionSettingsFlat,
        isTwoWeekPayPeriod
      )
    : generateExcelContentForClassicRestaurants(
        groupedShiftsByEmployees,
        CO_NUMBER,
        endAsDate,
        positionSettingsFlat,
        isTwoWeekPayPeriod,
        overtimeSection,
        maxHoursPerWeek
      )
  return [excelHeader, ...excelContent]
}

const generateExcelHeader = (
  positionSettingsFlat: Set<string>,
  overTimeSection: string
): ExportDataRowType[] => {
  const headers = [
    { value: I18n.t('attendance.pay_export.company'), type: 'string' },
    { value: I18n.t('attendance.pay_export.employee_no'), type: 'string' },
    {
      value: `${I18n.t('attendance.last_name')}, ${I18n.t('attendance.first_name')}`,
      type: 'string'
    },
    { value: I18n.t('attendance.pay_export.record_no'), type: 'string' },
    { value: I18n.t('attendance.pay_export.period'), type: 'Date' }
  ]

  const extraTips = [
    {
      value: `G21(${I18n.t('attendance.pay_export.quantity')})`,
      type: 'string'
    },
    { value: `G21(${I18n.t('attendance.pay_export.rate')})`, type: 'string' },
    {
      value: `G517(${I18n.t('attendance.pay_export.amount')})`,
      type: 'string'
    },
    {
      value: `G211(${I18n.t('attendance.pay_export.week')})`,
      type: 'string'
    },
    {
      value: `G211(${I18n.t('attendance.pay_export.amount')})`,
      type: 'string'
    },
    { value: `G809(${I18n.t('attendance.pay_export.week')})`, type: 'string' },
    {
      value: `G809(${I18n.t('attendance.pay_export.amount')})`,
      type: 'string'
    },
    { value: `G212(${I18n.t('attendance.pay_export.week')})`, type: 'string' },
    {
      value: `G212(${I18n.t('attendance.pay_export.amount')})`,
      type: 'string'
    }
  ]

  const uniquePositionArray = [...positionSettingsFlat]
  uniquePositionArray.forEach(section => {
    // Week column
    headers.push({
      value: `G${section}(${I18n.t('attendance.pay_export.week')})`,
      type: 'string'
    })

    // Quantity column
    if (!positionsCodesWithoutRateAndQuantity.includes(section)) {
      headers.push({
        value: `G${section}(${I18n.t('attendance.pay_export.quantity')})`,
        type: 'string'
      })
    }

    // Rate or Amount column
    headers.push({
      value: `G${section}(${
        positionsCodesWithoutRateAndQuantity.includes(section)
          ? I18n.t('attendance.pay_export.amount')
          : I18n.t('attendance.pay_export.rate')
      })`,
      type: 'string'
    })
  })

  // add overtime section
  headers.push({
    value: `G${overTimeSection}(${I18n.t('attendance.pay_export.week')})`,
    type: 'string'
  })

  // Only add quantity column if overtime section is not 232
  if (overTimeSection !== '232') {
    headers.push({
      value: `G${overTimeSection}(${I18n.t('attendance.pay_export.quantity')})`,
      type: 'string'
    })
  }

  headers.push({
    value: `G${overTimeSection}(${
      overTimeSection === '232'
        ? I18n.t('attendance.pay_export.amount')
        : I18n.t('attendance.pay_export.rate')
    })`,
    type: 'string'
  })

  headers.push(...extraTips)
  return headers
}

export const generateExcelContentForClassicRestaurants = (
  employeesShifts: ProcessedShiftsData,
  CO_NUMBER: string,
  endAsDate: string,
  positionSettingsFlat: Set<string>,
  isTwoWeekPayPeriod: boolean,
  overtimeSection: string,
  maxHoursPerWeek: number
) => {
  const rows: ExportDataRowType[][] = []
  const weeksToIterate = getWeeksToIterate(isTwoWeekPayPeriod)

  // Get the positions array in original order
  const positionsArray = [...positionSettingsFlat]

  for (const employeeUid in employeesShifts) {
    const employeeShift = employeesShifts[employeeUid]
    const infos = employeeShift.infos

    // Group shifts by week, position and rate
    for (const weekKey of weeksToIterate) {
      const currentWeek = weekKey as 'firstWeek' | 'secondWeek'
      const weekIndex = currentWeek === 'firstWeek' ? '1' : '2'
      const shiftsOfTheWeek = employeeShift.pay[currentWeek]

      // Use common grouping logic
      const groupedShifts = groupShiftsByPositionAndRate(shiftsOfTheWeek)

      // Second pass: Create a row for each position-rate group
      for (const groupKey in groupedShifts) {
        const group = groupedShifts[groupKey]

        // Use the totals from the grouped data

        // Create a new row for this group
        let row = [
          { type: 'string', value: CO_NUMBER },
          { type: 'string', value: `${infos?.payrollId ?? ''}` },
          { type: 'string', value: `${infos?.surname}, ${infos?.name}` },
          { type: 'string', value: '0' },
          { type: 'string', value: endAsDate }
        ]

        // Initialize empty columns for all positions in original order
        positionsArray.forEach(gCode => {
          // If the position has no need for rate or quantity, we add 2 columns (Week, Amount)
          // else we add 3 columns (Week, Quantity, Rate)
          const positionColumnCount =
            positionsCodesWithoutRateAndQuantity.includes(gCode) ? 2 : 3

          for (let i = 0; i < positionColumnCount; i++) {
            row.push({ value: '', type: 'string' })
          }
        })

        // Add empty columns for overtime section
        const overtimeColumnCount =
          positionsCodesWithoutRateAndQuantity.includes(overtimeSection) ? 2 : 3

        for (let i = 0; i < overtimeColumnCount; i++) {
          row.push({ value: '', type: 'string' })
        }

        // Now fill in the data for this particular shift
        const startIndex = 5 // First 5 columns are for employee info
        let indexToReplace = startIndex

        // Find the position of this payment section in the original array
        const currentPaymentCode = group.paymentSectionCode

        // We need to calculate the correct column index based on positions that come before
        // this one in the original array
        for (let i = 0; i < positionsArray.length; i++) {
          const posCode = positionsArray[i]

          // Once we've found our position, stop adding column offsets
          if (posCode === currentPaymentCode) {
            break
          }

          // Add the correct number of columns for this position type
          const columnCount = positionsCodesWithoutRateAndQuantity.includes(
            posCode
          )
            ? 2
            : 3
          indexToReplace += columnCount
        }

        const shiftDuration = group.totalRegularHours / 60

        const salaryRate = getSalaryRate(
          group.type as SalaryRateUnit,
          +(group.rate ?? 0),
          maxHoursPerWeek
        )
        const additionalSalary = group.totalAdditionalSalary || 0

        // Check if this position code needs only amount (no quantity)
        const isAmountOnlyPositionCode =
          positionsCodesWithoutRateAndQuantity.includes(currentPaymentCode)

        // Calculate the rate/amount column index based on whether quantity is needed
        const rateOrAmountColumnIndex = isAmountOnlyPositionCode
          ? indexToReplace + 1 // Week + Amount for codes like 514
          : indexToReplace + 2 // Week + Quantity + Rate for normal codes

        // Normal salary
        if (group.rate !== 0) {
          if (
            shiftDuration - group.overtimeDuration > 0 ||
            group.type === 'yearly'
          ) {
            // Week index - always add this
            row[indexToReplace] = {
              value: weekIndex,
              type: 'string'
            }

            // Quantity - only add if not in noNeedRateOrQuantity
            if (!isAmountOnlyPositionCode) {
              const quantityColumnIndex = indexToReplace + 1
              row[quantityColumnIndex] = {
                value:
                  group.rate === 0 && group.additionalSalary
                    ? '1' // If there is an additional salary only with no rate
                    : group.type === 'yearly'
                      ? `${maxHoursPerWeek}`
                      : (shiftDuration - group.overtimeDuration).toFixed(2),
                type: 'string'
              }
            }

            // Rate or Amount - always add this
            row[rateOrAmountColumnIndex] = {
              value: isAmountOnlyPositionCode
                ? (salaryRate * +shiftDuration + additionalSalary)
                    .toFixed(2)
                    .toString()
                : salaryRate === 0 // When salaryRate is 0, employee receives only additionalSalary payment instead of regular wage
                  ? additionalSalary.toFixed(2).toString()
                  : salaryRate.toFixed(2).toString(),
              type: 'string'
            }
          }

          // Overtime
          // Skip overtime for yearly salary employees
          if (group.overtimeDuration > 0 && group.rate !== 0 && group.type !== 'yearly') {
            // Calculate the overtime index by adding all position columns
            let overtimeIndex = 5 // Start after the first 5 columns

            // Count columns for all position codes
            for (const posCode of positionsArray) {
              overtimeIndex += positionsCodesWithoutRateAndQuantity.includes(
                posCode
              )
                ? 2
                : 3
            }

            const isOvertimeNoNeedRateOrQuantityCode =
              positionsCodesWithoutRateAndQuantity.includes(overtimeSection)

            const shouldAddOvertime =
              !positionsCodesWithoutRateAndQuantity.includes(
                group.paymentSectionCode
              )
            if (shouldAddOvertime) {
              // Week index
              row[overtimeIndex] = {
                value: weekIndex,
                type: 'string'
              }

              // Quantity - only add if not in positionsCodesWithoutRateAndQuantity
              if (!isOvertimeNoNeedRateOrQuantityCode) {
                row[overtimeIndex + 1] = {
                  value: group.overtimeDuration.toString(),
                  type: 'string'
                }
              }

              // Rate or Amount
              const overtimeRateOrAmountIndex =
                isOvertimeNoNeedRateOrQuantityCode
                  ? overtimeIndex + 1 // Week + Amount
                  : overtimeIndex + 2 // Week + Quantity + Rate

              row[overtimeRateOrAmountIndex] = {
                value: isOvertimeNoNeedRateOrQuantityCode
                  ? (salaryRate * group.overtimeDuration).toFixed(2).toString()
                  : salaryRate.toFixed(2).toString(),
                type: 'string'
              }
            }
          }
        } else {
          // Additional salary
          // Week index
          row[indexToReplace] = {
            value: weekIndex,
            type: 'string'
          }

          // Quantity
          if (!isAmountOnlyPositionCode) {
            const quantityColumnIndex = indexToReplace + 1
            row[quantityColumnIndex] = {
              value: '1',
              type: 'string'
            }
          }

          // Rate or Amount
          row[rateOrAmountColumnIndex] = {
            value: group.additionalSalary?.toString() ?? '0',
            type: 'string'
          }
        }

        // Add the row for this group
        rows.push(row)
      }
    }
  }
  return rows
}

const generateExcelContentForRandolphRestaurants = (
  employeeShifts: ProcessedShiftsData,
  CO_NUMBER: string,
  endAsDate: string,
  positionSettingsFlat: Set<string>,
  isTwoWeekPayPeriod: boolean
) => {
  const rows: ExportDataRowType[][] = []
  const weeksToIterate = isTwoWeekPayPeriod
    ? ['firstWeek', 'secondWeek']
    : ['firstWeek']

  for (const employeeUid in employeeShifts) {
    const startIndex = 5
    const infos = employeeShifts[employeeUid].infos

    // Process each week
    for (const weekKey of weeksToIterate) {
      const week = weekKey as 'firstWeek' | 'secondWeek'
      const weekIndex = week === 'firstWeek' ? 1 : 2

      // Create a map to group shifts by position and rate
      const groupedShifts: Record<
        string,
        {
          paymentSectionCode: string
          regularHours: number
          overtimeDuration: number
          rate: number
        }
      > = {}

      // Total overtime for this week (across all rates)
      let weekOverTimeHours = 0
      let weekOvertimeRate = 0

      // First pass: group by position and rate
      for (const payKey of Object.keys(employeeShifts[employeeUid].pay[week])) {
        const weekPay = employeeShifts[employeeUid].pay[week][payKey]

        if (weekPay) {
          // Create a composite key using position code and rate
          const groupKey = `${weekPay.paymentSectionCode}_${weekPay.rate}`

          if (!groupedShifts[groupKey]) {
            groupedShifts[groupKey] = {
              paymentSectionCode: weekPay.paymentSectionCode,
              regularHours: 0,
              overtimeDuration: 0,
              rate: weekPay.rate
            }
          }

          // Accumulate hours and overtime
          groupedShifts[groupKey].regularHours += weekPay.regularHours
          // Don't accumulate overtime for yearly salary employees
          const overtimeToAdd = weekPay.type === 'yearly' ? 0 : weekPay.overtimeDuration
          groupedShifts[groupKey].overtimeDuration += overtimeToAdd

          // Count total overtime for all shifts in this week (skip yearly salary)
          if (weekPay.type !== 'yearly') {
            weekOverTimeHours += weekPay.overtimeDuration
            if (weekPay.overtimeDuration > 0) {
              weekOvertimeRate = weekPay.rate // Use the rate from shifts with overtime
            }
          }
        }
      }

      // Second pass: create rows for each group
      for (const groupKey in groupedShifts) {
        const groupData = groupedShifts[groupKey]

        let row = [
          { type: 'string', value: CO_NUMBER },
          { type: 'string', value: `${infos?.payrollId ?? ''} ` },
          { type: 'string', value: `${infos?.surname}, ${infos?.name}` },
          { type: 'string', value: '0' },
          { type: 'string', value: endAsDate }
        ]

        // Generate columns for each payment section
        const uniquePositionArray = [...positionSettingsFlat]
        uniquePositionArray.forEach(() => {
          row.push({ value: '', type: 'string' })
          row.push({ value: '', type: 'string' })
          row.push({ value: '', type: 'string' })
        })

        const sectionIndex =
          getPositionColumnIndex(
            positionSettingsFlat,
            groupData.paymentSectionCode
          ) * 3
        const indexToReplace = sectionIndex + startIndex

        row[indexToReplace].value = weekIndex.toString()
        row[indexToReplace + 1].value = (groupData.regularHours / 60)
          .toFixed(2)
          .toString()
        row[indexToReplace + 2].value = groupData.rate.toFixed(2).toString()

        rows.push(row)
      }

      // Add overtime row if needed
      if (weekOverTimeHours > 0) {
        let overtimeRow = [
          { type: 'string', value: CO_NUMBER },
          { type: 'string', value: `${infos?.payrollId ?? ''} ` },
          { type: 'string', value: `${infos?.surname}, ${infos?.name}` },
          { type: 'string', value: '0' },
          { type: 'string', value: endAsDate }
        ]

        // Add empty position columns
        const uniquePositionArray = [...positionSettingsFlat]
        uniquePositionArray.forEach(() => {
          overtimeRow.push({ value: '', type: 'string' })
          overtimeRow.push({ value: '', type: 'string' })
          overtimeRow.push({ value: '', type: 'string' })
        })

        // Add overtime information
        overtimeRow.push({ value: weekIndex.toString(), type: 'string' })
        overtimeRow.push({
          value: (weekOverTimeHours / 60).toFixed(2).toString(),
          type: 'string'
        })
        overtimeRow.push({
          value: weekOvertimeRate.toFixed(2).toString(),
          type: 'string'
        })

        rows.push(overtimeRow)
      }
    }
  }
  return rows
}

const getPositionColumnIndex = (
  paymentSections: Set<string>,
  payKey: string
) => {
  return [...paymentSections].indexOf(payKey)
}
