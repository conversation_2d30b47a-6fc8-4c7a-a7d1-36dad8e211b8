import { I18n } from 'react-redux-i18n'

import { minutesToHours } from 'utils/time'

import { ExportCell } from './types/exportCell'
import {
  IAttendanceEnhancedShift,
  IAttendanceEnhancedShifts
} from 'types/attendance'
import { IPositions } from 'types/company'
import { IEmployee as Employee } from 'types/employee'

type Props = {
  startAsDate: string
  endAsDate: string
  employees: Employee[]
  name: string
  shifts: IAttendanceEnhancedShifts
  jobs: IPositions
  hasEmployeurDNumbers: boolean
  hasAcombaIntegration: boolean
}

export const generateByShiftExport = ({
  startAsDate,
  endAsDate,
  employees,
  jobs,
  name,
  shifts,
  hasEmployeurDNumbers,
  hasAcombaIntegration
}: Props) => {
  const acombaHeader: ExportCell[] = hasAcombaIntegration
    ? [
        {
          value: I18n.t('export_attendance.acombaId'),
          type: 'string'
        }
      ]
    : []

  const data: ExportCell[][] = [
    [
      {
        value: I18n.t('export_attendance.date'),
        type: 'string'
      },
      {
        value: `${startAsDate} ${I18n.t('export_attendance.to')} ${endAsDate}`,
        type: 'string'
      }
    ],
    [
      {
        value: I18n.t('export_attendance.name'),
        type: 'string'
      },
      {
        value: name,
        type: 'string'
      }
    ],
    [],
    [],
    [],

    [
      {
        value: I18n.t('export_attendance.first_name'),
        type: 'string'
      },
      {
        value: I18n.t('export_attendance.last_name'),
        type: 'string'
      },
      {
        value: I18n.t('export_attendance.empl_number'),
        type: 'string'
      },
      ...acombaHeader,
      {
        value: 'Type',
        type: 'string'
      },
      {
        value: '',
        type: 'string'
      },
      {
        value: I18n.t('export_attendance.date'),
        type: 'string'
      },
      {
        value: I18n.t('export_attendance.start'),
        type: 'string'
      },
      {
        value: I18n.t('export_attendance.end'),
        type: 'string'
      },
      {
        value: I18n.t('export_attendance.unpaid_break'),
        type: 'string'
      },
      {
        value: '',
        type: 'string'
      },
      {
        value: I18n.t('export_attendance.position'),
        type: 'string'
      },
      {
        value: '',
        type: 'string'
      },
      {
        value: '',
        type: 'string'
      },
      {
        value: I18n.t('export_attendance.total'),
        type: 'string'
      }
    ]
  ]

  const dates = Object.keys(shifts)

  dates.sort().forEach(date => {
    const dayEmployees = shifts[date]
    const dayShifts: (IAttendanceEnhancedShift & {
      employeeId: string
      shiftId: string
    })[] = []

    Object.entries(dayEmployees).forEach(([uid, shifts]) => {
      Object.entries(shifts).forEach(([shiftId, shift]) => {
        if (shift.start !== undefined) {
          dayShifts.push({ ...shift, shiftId, employeeId: uid })
        }
      })
    })
    dayShifts.sort((a, b) => (a.start! < b.start! ? -1 : 1))

    dayShifts
      .filter(shift => shift.start !== undefined)
      .forEach(shift => {
        const employee = employees.find(c => c.uid === shift.employeeId)!
        const positionName = jobs[shift.positionId]?.name
        let unpaidBreak
        if (shift.breaks) {
          unpaidBreak = Object.values(shift.breaks).reduce((acc, breakItem) => {
            return acc + (breakItem.lengthRounded || 0) / 60
          }, 0)
        }

        const acombaCell: ExportCell[] = hasAcombaIntegration
          ? [
              {
                value: employee.acombaId || '',
                type: 'string'
              }
            ]
          : []
        data.push([
          {
            value: employee.name,
            type: 'string'
          },
          {
            value: employee.surname,
            type: 'string'
          },
          {
            value:
              (hasEmployeurDNumbers ? employee.payrollId : employee.customId) ||
              '',
            type: 'string'
          },
          ...acombaCell,
          {
            value: I18n.t('export_attendance.shift'),
            type: 'string'
          },
          {
            value: '',
            type: 'string'
          },
          {
            value: date,
            type: 'string'
          },
          {
            value: minutesToHours(shift.start),
            type: 'string'
          },
          {
            value: minutesToHours(shift.end),
            type: 'string'
          },
          {
            value: unpaidBreak?.toFixed(2) || '',
            type: 'string'
          },
          {
            value: '',
            type: 'string'
          },
          {
            value: positionName,
            type: 'string'
          },
          {
            value: '',
            type: 'string'
          },
          {
            value: '',
            type: 'string'
          },
          {
            value: shift.shiftLengthHours,
            type: 'number'
          }
        ])
      })
  })

  return data
}
