import React from 'react'
import { I18n } from 'react-redux-i18n'

import dayjs from 'dayjs'
import isEmpty from 'lodash/isEmpty'
import styled from 'styled-components'
import { theme } from 'styles/theme'

import { roundTime } from '../payrollUtils'

import { SALARY_TYPE_YEARLY } from 'utils/constants'
import { minutesToHours } from 'utils/time'

import type {
  IAllConflictingAttendanceShifts,
  IAttendanceEnhancedShifts,
  OvertimeCalculationMode
} from 'types/attendance'
import type { IEmployee } from 'types/employee'

import checkGreenIcon from 'img/icons/checkGreenFillIcon.svg'
import clockFilledBlue from 'img/icons/clockFilledBlue.svg'
import clockFilledOrange from 'img/icons/clockFilledOrange.svg'
import closeFilledIcon from 'img/icons/closeFilledIcon.svg'
import exclamationOrange from 'img/icons/exclamationOrange.svg'
import exclamationRed from 'img/icons/exclamationRed.svg'
import triangleIcon from 'img/icons/triangleDarkIcon.svg'

type TimeSheetRowProps = {
  isWeekConfirmed: boolean
  weekHours: number
  weekDates: string[]
  attendanceData: IAttendanceEnhancedShifts
  employee: IEmployee
  roundingTime: number
  allConflictingShifts: IAllConflictingAttendanceShifts
  selectedEmployeeId: string
  selectedDate: string
  selectedShiftKey: string
  setselectedEmployeeId: (id: string) => void
  setselectedDate: (date: string) => void
  setSelectedShiftKey: (shiftKey: string) => void
  tableCellsRefs: {
    current: {
      [key: string]: HTMLDivElement | null
    }
  }
  shiftPopoverElement: {
    current: HTMLDivElement | null
  }
  setPopoverType: (type: string) => void
  overtimeCalculationMode: OvertimeCalculationMode

  selectedPositionId: string
  maxHoursPerWeek: number
}

const TimeSheetRow = ({
  isWeekConfirmed,
  weekHours,
  weekDates,
  attendanceData,
  employee,
  roundingTime,
  allConflictingShifts,
  selectedEmployeeId,
  selectedDate,
  selectedShiftKey,
  setselectedEmployeeId,
  setselectedDate,
  setSelectedShiftKey,
  tableCellsRefs,
  shiftPopoverElement,
  setPopoverType,
  overtimeCalculationMode,

  selectedPositionId,
  maxHoursPerWeek
}: TimeSheetRowProps) => (
  <>
    <TableBodyCellStyled>
      <EmpoyeeStatsStyled
        weekConfirmed={isWeekConfirmed}
        $isOvertime={
          weekHours > maxHoursPerWeek &&
          overtimeCalculationMode !== 'not-calculated'
        }
      >
        <EmpoyeeStatsRowHoursStyled>
          <p>{I18n.t('attendance.hoursWorked')}</p>
          <span>{weekHours}h</span>
        </EmpoyeeStatsRowHoursStyled>
      </EmpoyeeStatsStyled>
    </TableBodyCellStyled>

    {weekDates.map(date => {
      const dayMoment = dayjs(date, 'YYYY-MM-DD')
      const isToday = dayMoment.isSame(dayjs(), 'day')
      const dayShifts =
        (attendanceData[date] && attendanceData[date][employee.uid]) || {}

      if (isEmpty(dayShifts)) {
        return (
          <TableBodyCellStyled
            // broken ref
            ref={el => (tableCellsRefs.current[`${employee.uid}-${date}`] = el)}
            key={date}
          >
            <TableBodyWeekDayStyled
              isToday={isToday}
              disabled={false}
            >
              {isToday ? I18n.t('attendance.today') : dayMoment.format('D')}
            </TableBodyWeekDayStyled>
            <TableBodyCellEmptyStyled
              onClick={() => {
                shiftPopoverElement.current =
                  tableCellsRefs.current[`${employee.uid}-${date}`]
                setselectedEmployeeId(employee.uid)
                setselectedDate(date)
                setPopoverType('timesheet')
              }}
            >
              +
            </TableBodyCellEmptyStyled>
          </TableBodyCellStyled>
        )
      }

      return (
        <TableBodyCellStyled key={date}>
          <TableBodyWeekDayStyled isToday={isToday}>
            {isToday ? I18n.t('attendance.today') : dayMoment.format('D')}
          </TableBodyWeekDayStyled>

          {Object.entries(dayShifts)
            .sort(([_shiftKeyA, shiftA], [_shiftKeyB, shiftB]) => {
              if (shiftA.start && shiftB.start) {
                return shiftA.start - shiftB.start
              }
              return 0
            })
            .map(([shiftKey, shift]) => {
              const isAutoShift =
                shift.type === SALARY_TYPE_YEARLY && shift.start === undefined
              const shiftStartRounded = roundTime(shift.start, roundingTime)
              const notClockedOut =
                shift.end === undefined || shift.end === null
              const shiftEndRounded = notClockedOut
                ? null
                : roundTime(shift.end, roundingTime)

              const {
                isClockInDifferent = false,
                isClockOutDiffrent = false,
                neverClockedOut = false
              } = allConflictingShifts?.[date]?.[employee.uid]?.[shiftKey] || {}

              const isWorking =
                isToday &&
                !neverClockedOut &&
                !shift.isConfirmed &&
                (typeof shift.end === 'undefined' || shift.end === null)

              const isConflictingShift =
                isClockInDifferent ||
                isClockOutDiffrent ||
                !shift.positionId ||
                (neverClockedOut && !isWorking)

              const { isStartOverlapping, isEndOverlapping } = shift
              const isOverlapping = isStartOverlapping || isEndOverlapping

              const isConfirmed = Boolean(
                !isWorking &&
                  (shift.isConfirmed || !isConflictingShift) &&
                  !isOverlapping
              )

              const totalStyles: TableCellShiftTotalStyledProps = {}
              if (!isConfirmed) {
                if (isWorking) {
                  totalStyles.isWorking = true
                } else if (neverClockedOut) {
                  totalStyles.notClocked = true
                } else if (isConflictingShift) {
                  totalStyles.isDifferentTime = true
                } else if (isOverlapping) {
                  totalStyles.isOverlapping = true
                }
              }

              const isShiftSelected =
                selectedEmployeeId === employee.uid &&
                selectedDate === date &&
                selectedShiftKey === shiftKey

              return (
                <TableCellShiftStyled
                  key={shiftKey}
                  // ref={shiftPopoverElement}
                  shiftConfirmed={Boolean(isShiftSelected) && isConfirmed}
                  isConflicting={
                    Boolean(isShiftSelected) &&
                    !isConfirmed &&
                    (isConflictingShift || isOverlapping)
                  }
                  ref={el =>
                    (tableCellsRefs.current[
                      `${employee.uid}-${date}-${shiftKey}`
                    ] = el)
                  }
                  onClick={() => {
                    shiftPopoverElement.current =
                      tableCellsRefs.current[
                        `${employee.uid}-${date}-${shiftKey}`
                      ]
                    setselectedEmployeeId(employee.uid)
                    setselectedDate(date)
                    setSelectedShiftKey(shiftKey)

                    setPopoverType('timesheet')
                  }}
                  $withOpacity={
                    isAutoShift ||
                    (selectedPositionId !== shift.positionId &&
                      selectedPositionId !== '')
                  }
                >
                  {isOverlapping ? (
                    <CloseIconStyled />
                  ) : isConfirmed ? null : isWorking ? (
                    // Time clocked-in is different by +/= 15 minutes
                    <ClockIconStyled orange={isClockInDifferent} />
                  ) : isClockInDifferent ? (
                    // never clocked-out
                    <ExclamationIconStyled orange={!neverClockedOut} />
                  ) : neverClockedOut ? (
                    <CloseIconStyled />
                  ) : null}

                  <TableCellShiftTimeStyled>
                    <TableCellShiftInStyled
                      isDifferentTime={!isConfirmed && isClockInDifferent}
                      isOverlapping={isStartOverlapping}
                      //  when salary is '-'??
                    >
                      <span>{I18n.t('attendance.in')}</span>
                      <p>{minutesToHours(shiftStartRounded, false)}</p>
                    </TableCellShiftInStyled>
                    <TimeTriangleIconStyled
                      src={triangleIcon}
                      alt=''
                    />
                    <TableCellShiftOutStyled
                      isDifferentTime={!isConfirmed && isClockOutDiffrent}
                      isWorking={isWorking} // Employee is currently working
                      notClocked={
                        (!isConfirmed && neverClockedOut) || isEndOverlapping
                      } // Employee never clocked-out
                      isOverlapping={isEndOverlapping}
                    >
                      <>
                        <span>{I18n.t('attendance.out')}</span>
                        <p>
                          {!neverClockedOut && shiftEndRounded !== null
                            ? minutesToHours(shiftEndRounded)
                            : isAutoShift
                              ? ''
                              : '-'}
                        </p>
                      </>
                    </TableCellShiftOutStyled>
                  </TableCellShiftTimeStyled>
                  <TableCellShiftTotalStyled
                    {...totalStyles}
                    isOverlapping={isOverlapping}
                  >
                    <span>{I18n.t('attendance.total')}</span>
                    <p>{!neverClockedOut ? shift.shiftLengthHours : '0'}h</p>
                  </TableCellShiftTotalStyled>
                </TableCellShiftStyled>
              )
            })}
        </TableBodyCellStyled>
      )
    })}
  </>
)

export default TimeSheetRow

const TableBodyCellStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;

  padding: ${theme.rem(21)} ${theme.rem(4)} ${theme.rem(8)};

  position: relative;

  border-right: 2px dashed #dbe3eb;

  color: ${theme.colors.midGrey600};
  font-family: ${theme.fonts.bold};
  font-size: ${theme.remFont(17)};

  &:first-child {
    border-right: none;
    padding: ${theme.rem(15)} 0;
  }
  &:nth-child(9) {
    border-right: none;
  }
  &:nth-child(2) {
    padding: ${theme.rem(15)} ${theme.rem(6)} ${theme.rem(15)} 0;
  }

  @media (max-width: 1400px) {
    padding: ${theme.rem(21)} ${theme.rem(4)} ${theme.rem(9)};
  }
`
const TableBodyCellEmptyStyled = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;

  width: 100%;
  height: 100%;
  max-height: ${theme.rem(82)};
  background-color: unset;
  border: unset;

  border-radius: 12px;

  pointer-events: ${({ disabled }) => (disabled ? 'none' : null)};
  opacity: ${({ disabled }) => (disabled ? '0.4' : null)};

  &:hover {
    box-shadow: ${({ disabled }) =>
      disabled ? theme.colors.blue : '0px 0px 10px -3px rgba(0, 0, 0, 0.4)'};
  }
`

type TableBodyWeekDayStyledProps = {
  isToday?: boolean
  disabled?: boolean
}

const TableBodyWeekDayStyled = styled.span<TableBodyWeekDayStyledProps>`
  position: absolute;
  top: 0;
  left: ${theme.rem(6)};

  font-size: ${theme.remFont(12)};
  color: ${({ isToday }) => (isToday ? theme.colors.blue : 'inherit')};
  font-style: italic;

  opacity: ${({ disabled }) => (disabled ? '0.4' : null)};
`

const EmpoyeeStatsRowHoursStyled = styled.div`
  display: flex;
  align-items: center;

  gap: ${theme.rem(10)};
  width: 100%;
  p {
    display: flex;
    justify-content: flex-end;

    flex: 0.7;
    max-width: 70%;

    white-space: unset;
    text-align: center;
    font-size: ${theme.remFont(10.5)};
    font-style: italic;
    color: ${theme.colors.midGrey600};
  }
  span {
    display: flex;
    justify-content: flex-start;

    flex: 0.3;
  }
`

type EmpoyeeStatsStyledProps = {
  weekConfirmed?: boolean
  $isOvertime?: boolean
}

const EmpoyeeStatsStyled = styled.div<EmpoyeeStatsStyledProps>`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  padding: 0 ${theme.rem(6)};
  height: ${theme.rem(60)};
  width: 100%;

  position: relative;

  background-color: ${props =>
    props.weekConfirmed && props.$isOvertime
      ? theme.colors.lightRed
      : props.weekConfirmed
        ? '#cdf5e8'
        : props.$isOvertime
          ? theme.colors.lightRed
          : '#dbe3eb'};
  border-radius: ${theme.rem(18)};

  color: ${theme.colors.darkGrey};
  line-height: normal;
  font-size: ${theme.remFont(13)};
  cursor: default;
  &:after {
    content: ${props =>
      props.weekConfirmed || (props.weekConfirmed && props.$isOvertime)
        ? "''"
        : 'unset'};
    width: ${theme.rem(22)};
    height: ${theme.rem(22)};

    position: absolute;
    right: ${theme.rem(-4)};
    top: ${theme.rem(-8)};

    background: url(${checkGreenIcon});
  }
  ${EmpoyeeStatsRowHoursStyled} {
    span {
      color: ${({ $isOvertime }) => ($isOvertime ? theme.colors.red : null)};
    }
  }
`

const TimeTriangleIconStyled = styled.img`
  display: flex;
  align-self: flex-end;

  width: ${theme.rem(11)};
  height: ${theme.rem(11)};
  margin-bottom: ${theme.rem(8)};
  margin-left: ${theme.rem(-2)};

  @media (max-width: 1400px) {
    width: ${theme.rem(11)};
    height: ${theme.rem(11)};
  }
`

type TableCellShiftStyledProps = {
  shiftConfirmed?: boolean
  isConflicting?: boolean
  $withOpacity?: boolean
}

const TableCellShiftStyled = styled.div<TableCellShiftStyledProps>`
  display: flex;
  flex-direction: column;

  width: 100%;
  position: relative;

  border-radius: ${theme.rem(18)};
  background-color: #fff;
  cursor: pointer;
  box-shadow: ${props =>
    props.shiftConfirmed
      ? `0px 0px 3px 3px ${theme.colors.green}!important`
      : props.isConflicting
        ? '0px 0px 3px 3px #ec758a!important'
        : ''};

  &:hover {
    box-shadow: 0px 0px 10px -3px rgba(0, 0, 0, 0.4);
  }
  &:nth-child(3),
  &:nth-child(4),
  &:nth-child(5) {
    margin-top: ${theme.rem(10)};
  }

  &:after {
    content: ${({ $withOpacity }) => ($withOpacity ? "''" : null)};
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: rgba(236, 241, 245, 0.4);
    border-radius: ${theme.rem(18)};
    pointer-events: none;
  }
`

type TableCellShiftTotalStyledProps = {
  isWorking?: boolean
  notClocked?: boolean
  isDifferentTime?: boolean
  isOverlapping?: boolean
}

const TableCellShiftTotalStyled = styled.div<TableCellShiftTotalStyledProps>`
  display: flex;
  align-items: center;
  justify-content: center;

  width: 100%;
  padding: ${theme.rem(4)} ${theme.rem(6)};

  border-radius: 0 0 ${theme.rem(18)} ${theme.rem(18)};
  background-color: ${props =>
    props.isWorking
      ? '#aee9ff'
      : props.isDifferentTime
        ? '#fddbaa'
        : props.isOverlapping
          ? '#fee6eb'
          : props.notClocked
            ? '#fac7d5'
            : '#cdf5e8'};

  span {
    margin-right: ${theme.rem(12)};

    font-size: ${theme.remFont(12)};
    font-style: italic;
    color: ${theme.colors.midGrey600};
  }
  p {
    color: ${({ isWorking }) =>
      isWorking ? theme.colors.blue : theme.colors.darkGrey};
    font-size: ${theme.remFont(13)};
  }

  @media (max-width: 1400px) {
    span {
      font-size: ${theme.remFont(11)};
    }
    p {
      font-size: ${theme.remFont(12)};
    }
  }
`

type TableCellShiftInStyledProps = {
  isDifferentTime?: boolean
  notClocked?: boolean
  isOverlapping?: boolean
}

const TableCellShiftInStyled = styled.div<TableCellShiftInStyledProps>`
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;

  flex: 1;
  padding: ${theme.rem(3)};

  border-radius: ${theme.rem(14)};
  background-color: ${({ isDifferentTime, isOverlapping }) =>
    isOverlapping ? '#fee6eb' : isDifferentTime ? '#fdeacd' : '#fff'};

  line-height: normal;

  span {
    font-size: ${theme.remFont(12)};
    font-style: italic;
    color: ${theme.colors.midGrey600};
    white-space: nowrap;
  }
  p {
    color: ${props =>
      props.isDifferentTime
        ? '#f9b63a'
        : props.isOverlapping
          ? '#ec758a'
          : props.notClocked
            ? '#fee6eb'
            : theme.colors.darkGrey};
    font-size: ${theme.remFont(13)};
  }

  @media (max-width: 1400px) {
    padding: ${theme.rem(3)} ${theme.rem(4)};

    span {
      font-size: ${theme.remFont(11)};
    }
    p {
      font-size: ${theme.remFont(12)};
    }
  }
`

const TableCellShiftTimeStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;

  width: 100%;
  padding: ${theme.rem(5)};

  @media (max-width: 1400px) {
    padding: ${theme.rem(4)};
  }
`

type TableCellShiftOutStyledProps = {
  isWorking?: boolean
  notClocked?: boolean
  isDifferentTime?: boolean
  isOverlapping?: boolean
}

const TableCellShiftOutStyled = styled(
  TableCellShiftInStyled
)<TableCellShiftOutStyledProps>`
  background-color: ${props =>
    props.isWorking
      ? '#d5f5ff'
      : props.notClocked || props.isOverlapping
        ? '#fee6eb'
        : props.isDifferentTime
          ? '#fdeacd'
          : '#fff'};
  p {
    color: ${props =>
      props.isWorking
        ? theme.colors.blue
        : props.notClocked || props.isOverlapping
          ? '#ec758a'
          : props.isDifferentTime
            ? '#f9b63a'
            : theme.colors.darkGrey};
  }
`

type ExclamationIconStyledProps = {
  small?: boolean
  top?: string
  zIndex?: boolean
  orange?: boolean
}

const ExclamationIconStyled = styled.div<ExclamationIconStyledProps>`
  display: flex;
  justify-content: center;
  align-items: center;

  width: ${({ small }) => (small ? theme.rem(20) : theme.rem(23))};
  height: ${({ small }) => (small ? theme.rem(20) : theme.rem(23))};

  position: absolute;
  right: ${theme.rem(-6)};
  top: ${({ top }) => (top ? top : theme.rem(-8))};
  z-index: ${({ zIndex }) => (zIndex ? '1' : '')};

  background-image: url(${({ orange }) =>
    orange ? exclamationOrange : exclamationRed});
  background-color: ${({ orange }) => (orange ? '#f9b63a' : '#ec758a')};
  background-size: ${({ small }) => (small ? theme.rem(1.5) : theme.rem(2.5))};
  background-position: center;
  background-repeat: no-repeat;
  border-radius: 50%;
  z-index: 1;
`

type ClockIconStyledProps = {
  orange?: boolean
}

const ClockIconStyled = styled.div<ClockIconStyledProps>`
  display: flex;
  justify-content: center;
  align-items: center;

  width: ${theme.rem(23)};
  height: ${theme.rem(23)};

  position: absolute;
  right: ${theme.rem(-6)};
  top: ${theme.rem(-8)};

  background-image: url(${({ orange }) =>
    orange ? clockFilledOrange : clockFilledBlue});
  background-color: ${({ orange }) => (orange ? '#f9b63a' : theme.colors.blue)};
  background-size: ${theme.rem(15)};
  background-position: center;
  background-repeat: no-repeat;
  border-radius: 50%;
  z-index: 1;
`
const CloseIconStyled = styled(ClockIconStyled)`
  background-image: url(${closeFilledIcon});
  background-color: unset;
  background-size: 100%;
`
