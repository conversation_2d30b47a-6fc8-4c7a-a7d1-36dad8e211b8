import styled from 'styled-components'
import { theme } from 'styles/theme'

import { ReactComponent as ClockIcon } from 'img/icons/hoursIcon.svg'
import { ReactComponent as SparkleIcon } from 'img/icons/sparkleIcon.svg'

export const TabListStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  justify-self: center;

  padding: 0.3rem;
  gap: 0.3rem;

  border-radius: 0.8rem;
  background-color: rgba(229, 235, 239, 0.8);
`

export const TabButtonStyled = styled.button<{
  $isActive: boolean
  $noOpacity?: boolean
}>`
  display: flex;
  align-items: center;
  justify-content: center;

  gap: 0.5rem;
  padding: 0.3rem 1rem;
  min-width: 7.5rem;

  border: none;
  background: ${({ $isActive }) =>
    $isActive ? 'linear-gradient(180deg, #3BBCFF, #2D87FF 100%)' : 'none'};
  border-radius: 0.6rem;
  box-shadow: ${({ $isActive }) =>
    $isActive
      ? '2px 2px 4px -1 rgba(18, 18, 23,0.06),2px 2px 4px -1 rgba(18, 18, 23,0.08)'
      : null};

  color: ${({ $isActive }) =>
    $isActive ? 'white' : theme.colorsNew.darkGrey500};
  font-size: 0.95rem;
  font-family: ${theme.fonts.normal};
  transition: all 0.2s ease-in-out;
  opacity: ${({ $isActive, $noOpacity }) =>
    $isActive || $noOpacity ? 1 : 0.3};

  &:hover,
  &:focus {
    opacity: 1;
  }

  svg {
    width: 1.2rem;
    height: 1.2rem;
    flex-shrink: 0;
    fill: currentColor;
  }
`

export const WeekBlockWrapStyled = styled.div`
  width: 100%;
  padding: 1rem;
`

export const WeekBlockStyled = styled.div`
  display: flex;
  align-items: center;
  flex-direction: column;

  gap: 0.8rem;
  width: 100%;
  padding: 1rem 1.5rem;

  border-radius: 1rem;
  background-color: #fff;
`

export const WeekTopBlockStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;

  width: 100%;
`

export const WeekTopBlockRowStyled = styled.div`
  display: flex;
  align-items: center;

  gap: 0.3rem;
`

export const PeriodBlockStyled = styled.div`
  display: flex;
  align-items: center;

  gap: 0.5rem;
  padding: 0 1rem;
`

export const CalendarButtonStyled = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;

  width: 1.4rem;
  height: 1.4rem;
  padding: 0;

  color: ${theme.colorsNew.darkGrey500};

  border: 0;
  background-color: unset;
  opacity: 0.7;
  transition: all 0.2s ease-in-out;
  :hover,
  :focus {
    opacity: 1;
  }
`

export const CalendarIconStyled = styled.img`
  width: 1.2rem;
  height: 1.2rem;
`

export const PeriodSliderStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;

  gap: 0.2rem;
`

export const PeriodArrowStyled = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;

  width: 1.4rem;
  height: 1.4rem;
  padding: 0;

  border: none;
  border-radius: 50%;
  background-color: transparent;
  cursor: pointer;
  opacity: 0.7;

  transition: opacity 0.2s ease;

  :hover,
  :focus {
    opacity: 1;
  }

  svg {
    width: 1rem;
    height: 1rem;
  }
`

export const PeriodTextStyled = styled.button<{ $isLowerCase?: boolean }>`
  padding: 0;
  min-width: 7rem;

  border: none;
  background-color: transparent;

  color: ${theme.colorsNew.darkGrey500};
  font-size: 0.9rem;
  font-family: ${theme.fonts.bold};
  text-transform: ${({ $isLowerCase }) => ($isLowerCase ? 'lowercase' : null)};
`

export const PeriodStatusStyled = styled.button<{ $isCurrentPeriod: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;

  padding: 0.2rem 0.8rem;

  border: none;
  border-radius: 0.6rem;
  background-color: #e6f7ff;

  color: #00a2e9;
  font-size: 0.75rem;
  font-family: ${theme.fonts.normal};
  line-height: normal;
  transition: all 0.2s ease-in-out;
  :hover,
  :focus {
    color: #fff;
    background-color: #00a2e9;
  }
`

export const WeekPeriodTabsStyled = styled(TabListStyled)`
  padding: 0;

  ${TabButtonStyled} {
    min-width: 5.5rem;
    font-size: 0.875rem;
  }
`

export const ButtonStyled = styled.button<{ $isOrange?: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;

  gap: 0.4rem;
  height: 2rem;
  padding: 0.2rem 0.8rem;

  border: ${({ $isOrange }) => ($isOrange ? '0' : '1px solid #d0d5dd')};
  border-radius: 0.6rem;
  background: ${({ $isOrange }) =>
    $isOrange ? 'linear-gradient( #FFA100, #FF4D00)' : '#fff'};

  color: ${({ $isOrange }) =>
    $isOrange ? '#fff' : theme.colorsNew.darkGrey500};
  font-size: 0.8rem;
  font-family: ${theme.fonts.normal};

  span {
    display: flex;
    align-items: center;
    justify-content: center;

    width: 1rem;
    height: 1rem;

    border-radius: 50%;
    background-color: ${({ $isOrange }) => ($isOrange ? '#fff' : '#848da3')};

    color: ${({ $isOrange }) => ($isOrange ? '#E18700' : '#fff')};
    font-size: 0.675rem;
    font-family: ${theme.fonts.bold};
    line-height: normal;
  }

  :hover,
  :focus {
    background: ${({ $isOrange }) => ($isOrange ? '#FFA000' : '#d0d5dd')};
  }
`

export const ClockIconStyled = styled(ClockIcon)`
  width: 1rem;
  height: 1rem;
  fill: currentColor;
`

export const WeekDaysStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;

  gap: 0.3rem;
  width: 100%;
`

export const DayStyled = styled.div<{ $isToday: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;

  flex: 1;
  padding: 0.3rem;

  border: 1px solid
    ${({ $isToday }) => ($isToday ? theme.colorsNew.blue : 'transparent')};
  border-radius: 0.6rem;
  background-color: #efefef;

  color: ${theme.colorsNew.darkGrey500};
  font-size: 0.9rem;
  font-family: ${theme.fonts.normal};
`

export const GreyOverlayStyled = styled.div`
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
`

export const ClaimedStatusStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;

  gap: 0.5rem;
  height: 2rem;
  padding: 0.2rem 0.2rem 0.2rem 0.6rem;

  border-radius: 0.6rem;
  background: linear-gradient(
    90deg,
    #01d9d5 0%,
    #00befa 18.27%,
    #9967ff 42.79%,
    #fd5c61 61.54%,
    #ff902d 84.13%,
    #ffa000 97.12%
  );
`

export const ClaimedStatusBlockStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;

  gap: 0.4rem;
  height: 100%;
  padding: 0.1rem 0.6rem;

  border-radius: 0.4rem;
  background-color: #fff;
  overflow: hidden;
`

export const ClaimedTextStyled = styled.p`
  color: ${theme.colorsNew.darkGrey500};
  font-size: 0.8rem;
  font-family: ${theme.fonts.light};
  line-height: normal;
`

export const SparkleIconStyled = styled(SparkleIcon)`
  width: 1.2rem;
  height: 1.2rem;
  fill: currentColor;

  .sparkleStar1 {
    transform-origin: center;
    animation: sparkle 0.8s ease-in-out forwards;
  }
  .sparkleStar2 {
    transform-origin: right;
    animation: sparkle2 1s ease-in-out 0.4s forwards;
  }
  .sparkleStar3 {
    transform-origin: top;
    animation: sparkle3 1.3s ease-in-out forwards;
  }

  @keyframes sparkle {
    0% {
      transform: scale(0.5);
    }
    100% {
      transform: scale(1);
    }
  }
  @keyframes sparkle2 {
    0% {
      transform: scale(0.8);
    }
    25% {
      transform: scale(1.2);
    }
    40% {
      transform: scale(1.4);
    }
    65% {
      transform: scale(1.2);
    }
    100% {
      transform: scale(1);
    }
  }
  @keyframes sparkle3 {
    0% {
      transform: scale(1);
    }
    25% {
      transform: scale(1.4);
    }
    40% {
      transform: scale(1.8);
    }
    50% {
      transform: scale(1.4);
    }
    60% {
      transform: scale(1.1);
    }
    100% {
      transform: scale(1);
    }
  }
`

export const ErrorContainer = styled.div`
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  background: #fff;
  border: 1px solid #ff6b6b;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.15);
  max-width: 400px;
  animation: slideInRight 0.3s ease-out;

  .error-content {
    display: flex;
    align-items: flex-start;
    padding: 16px;
    gap: 12px;
  }

  .error-icon {
    font-size: 24px;
    flex-shrink: 0;
  }

  .error-details {
    flex: 1;
  }

  .error-title {
    font-weight: 600;
    color: #d63031;
    margin-bottom: 8px;
  }

  .error-message {
    color: #636e72;
    font-size: 14px;
    margin-bottom: 4px;
  }

  .retry-info {
    color: #0984e3;
    font-size: 12px;
    font-style: italic;
  }

  .degraded-info {
    color: #f39c12;
    font-size: 12px;
    font-weight: 500;
    margin-top: 4px;
  }

  .retry-button {
    background: #d63031;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s;

    &:hover:not(:disabled) {
      background: #b71c1c;
    }

    &:disabled {
      background: #ddd;
      cursor: not-allowed;
    }
  }

  @keyframes slideInRight {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }
`

export const LoadingContainer = styled.div`
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 999;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);

  .loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 32px;
    gap: 16px;
  }

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #0984e3;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  .loading-text {
    color: #636e72;
    font-size: 16px;
    text-align: center;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
`
