import styled from 'styled-components'
import { theme } from 'styles/theme'

import { OutlineButton } from 'components/ui/OutlineButton'

import { ReactComponent as DeleteIcon } from 'img/icons/trashNewIcon.svg'

export const DeleteModalTextStyled = styled.p`
  color: ${theme.colorsNew.darkGrey500};
  font-size: 1.1rem;
  text-align: center;
  font-family: ${theme.fonts.normal};
`

export const DeleteButtonStyled = styled(OutlineButton)`
  gap: 0.4rem;
  flex: 1;
  width: unset;

  border-width: 1px;
  border-radius: 1.2rem;

  font-size: 0.95rem;
  font-family: ${theme.fonts.normal};
  line-height: normal;
`

export const ButtonBlockStyled = styled.div`
  display: flex;

  padding: 1.5rem;

  gap: 1rem;
  width: 100%;

  position: absolute;
  bottom: 0;
`

export const DeleteModalStyled = styled.div<{ $isConflictModal?: boolean }>`
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;

  gap: 0.8rem;
  width: 100%;
  height: 100%;

  padding: 1.5rem;
  position: absolute;
  top: 0;
  left: 0;

  border: 1px solid
    ${({ $isConflictModal }) => ($isConflictModal ? 'transparent' : '#ff3b30')};
  border-radius: ${({ $isConflictModal }) =>
    $isConflictModal ? '0.5rem' : '0.8rem'};
  background-color: #fff;
  z-index: 1100;

  ${DeleteModalTextStyled} {
    font-size: ${({ $isConflictModal }) =>
      $isConflictModal ? '0.95rem' : null};
  }
  ${DeleteButtonStyled} {
    height: ${({ $isConflictModal }) => ($isConflictModal ? '1.8rem' : null)};
    font-size: ${({ $isConflictModal }) =>
      $isConflictModal ? '0.85rem' : null};
  }
  ${ButtonBlockStyled} {
    padding: ${({ $isConflictModal }) => ($isConflictModal ? '1rem' : null)};
  }
`

export const CancelButtonStyled = styled(DeleteButtonStyled)`
  border-color: #afbaca;
  background-color: #fff;
  color: #848da3;
`

export const DeleteIconStyled = styled(DeleteIcon)`
  width: 0.9rem;
  height: 0.9rem;
  fill: currentColor;
`
