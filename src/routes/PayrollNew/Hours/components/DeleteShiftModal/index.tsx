import React from 'react'
import { I18n } from 'react-redux-i18n'

import {
  DeleteModalTextStyled,
  DeleteButtonStyled,
  ButtonBlockStyled,
  DeleteModalStyled,
  CancelButtonStyled,
  DeleteIconStyled
} from '../../../styles/DeleteShiftModal.styles'

const DeleteShiftModal = ({
  onClose,
  onDelete,
  isConflictModal
}: {
  onClose: () => void
  onDelete: () => void
  isConflictModal?: boolean
}) => {
  return (
    <DeleteModalStyled $isConflictModal={isConflictModal}>
      <DeleteModalTextStyled>
        Are you sure you want to delete this shift?
      </DeleteModalTextStyled>
      <ButtonBlockStyled>
        <DeleteButtonStyled
          onClick={onDelete}
          color='red'
        >
          <DeleteIconStyled />
          {I18n.t('common.delete')}
        </DeleteButtonStyled>
        <CancelButtonStyled onClick={onClose}>
          {I18n.t('common.cancel')}
        </CancelButtonStyled>
      </ButtonBlockStyled>
    </DeleteModalStyled>
  )
}

export default DeleteShiftModal

