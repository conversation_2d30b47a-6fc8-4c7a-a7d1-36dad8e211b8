import styled from 'styled-components'
import { theme } from 'styles/theme'

export const DrawerStyled = styled.div<{ $isVisible: boolean }>`
  display: flex;
  align-items: center;
  flex-direction: column;

  gap: 2rem;
  width: 15rem;
  height: 100vh;
  padding: 2rem;
  min-width: 300px;

  position: fixed;
  top: 0;
  right: 0;
  pointer-events: ${({ $isVisible }) => ($isVisible ? 'auto' : 'none')};
  background-color: #fff;

  opacity: ${({ $isVisible }) => ($isVisible ? 1 : 0)};
  visibility: ${({ $isVisible }) => ($isVisible ? 'visible' : 'hidden')};
  transform: translateX(${({ $isVisible }) => ($isVisible ? '0' : '100%')});
  transition: all 0.3s ease-in-out;
  overflow-y: auto;

  box-shadow: -30px 44px 44px 0px rgba(0, 0, 0, 0.2);
  z-index: 1000;
`

export const DrawerBlockStyled = styled.div`
  display: flex;
  align-items: center;
  flex-direction: column;

  gap: 1rem;
  width: 100%;
`

export const DrawerBlockHeaderStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;

  width: 100%;
`

export const DrawerBlockHeaderTitleStyled = styled.p`
  color: ${theme.colorsNew.darkGrey500};
  font-size: 0.9rem;
  font-family: ${theme.fonts.normal};
`

export const DrawerBlockListStyled = styled.div`
  display: flex;
  align-items: center;
  flex-direction: column;

  gap: 0.5rem;
  width: 100%;
`

export const ListItemStyled = styled.button<{ $isSelected: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;

  width: 100%;
  padding: 0.5rem 0.8rem;

  border: 1.5px solid
    ${({ $isSelected }) => ($isSelected ? '#00b1ff' : '#e4e7ec')};
  border-radius: 0.8rem;
  background-color: ${({ $isSelected }) => ($isSelected ? '#fff' : '#f2f2f4')};

  color: ${theme.colorsNew.darkGrey500};
  font-size: 0.875rem;
  font-family: ${theme.fonts.normal};
  :hover {
    border-color: #00b1ff;
  }
`

export const SwitchStyled = styled.button<{ $isActive: boolean }>`
  display: flex;
  align-items: center;
  justify-content: ${({ $isActive }) =>
    $isActive ? 'flex-end' : 'flex-start'};

  width: 2.4rem;
  height: 1.4rem;
  padding: 0.2rem;

  border: 0;
  background-color: ${({ $isActive }) => ($isActive ? '#00A2E9' : '#e9eff6')};
  border-radius: 1.2rem;
  transition: all 0.2s ease-in-out;
  :hover,
  :focus {
    box-shadow: 0px 0px 4px 0px rgba(18, 18, 23, 0.5);
  }
`

export const SwitchButtonStyled = styled.div`
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  background-color: transparent;
  border: 0.3rem solid #fff;
  box-shadow:
    0px 1px 2px 0px rgba(18, 18, 23, 0.06),
    0px 1px 3px 0px rgba(18, 18, 23, 0.1);
`

export const SingleRoleMessageStyled = styled.div`
  padding: 1rem;
  margin-bottom: 1rem;
  border-radius: 0.8rem;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;

  color: ${theme.colorsNew.darkGrey500};
  font-size: 0.875rem;
  font-family: ${theme.fonts.normal};
  text-align: center;
  line-height: 1.4;
`
