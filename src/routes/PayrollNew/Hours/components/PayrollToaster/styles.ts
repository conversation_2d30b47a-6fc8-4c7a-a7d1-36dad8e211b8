import styled from 'styled-components'
import { theme } from 'styles/theme'

import { ReactComponent as CloseIcon } from 'img/icons/closeIcon.svg'

export const ToasterStyled = styled.div<{ $bgColor: string }>`
  display: flex;
  align-items: center;
  flex-direction: column;

  gap: 0.8rem;
  padding: 0.8rem;
  border-radius: 0.6rem;

  background: ${({ $bgColor }) => $bgColor};
`

export const CloseIconStyled = styled(CloseIcon)`
  width: 0.8rem;
  height: 0.8rem;
  stroke: #fff;
`

export const HeaderStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
`

export const IconWrapStyled = styled.div`
  svg {
    width: 1.2rem;
    height: 1.2rem;
  }
`

export const BlockStyled = styled.div`
  display: flex;
  align-items: flex-end;
  justify-content: space-between;

  gap: 1rem;
  width: 100%;
`

export const TextStyled = styled.p`
  color: ${theme.colors.white};
  font-size: 0.9rem;
  font-family: ${theme.fonts.normal};
  line-height: normal;
`

export const ButtonStyled = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;

  padding: 0.2rem 1rem;
  min-height: 2rem;
  flex-shrink: 0;

  border: 1px solid #fff;
  border-radius: 1.2rem;
  background: transparent;

  color: ${theme.colors.white};
  font-size: 0.8rem;
  font-family: ${theme.fonts.bold};
`
