import Popover from 'react-bootstrap/Popover'

import styled from 'styled-components'
import { theme } from 'styles/theme'

import { CompanyAvatar } from 'components/ui/AvatarInitials'

export const PopoverStyled = styled(Popover)`
  width: 40rem;
  max-width: 40rem;
  padding: 0;

  border: 0;
  border-radius: 0.8rem;

  .arrow {
    display: none;
  }
`

export const ContainerStyled = styled.div`
  display: flex;
  flex-direction: column;
`

export const HeaderWrapStyled = styled.div`
  padding: 1.5rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
`

export const HeaderStyled = styled.div`
  display: grid;
  align-items: center;

  gap: 0.5rem;
  grid-template-columns: 1fr 0.8fr 1fr 1.4rem 1fr 0.6fr;
`

export const HeaderTitleStyled = styled.p`
  color: ${theme.colorsNew.darkGrey500};
  font-size: 1rem;
  font-family: ${theme.fonts.normal};
  line-height: normal;

  span {
    margin-left: 0.5rem;
    padding: 0.2rem 0.5rem;
    border-radius: 0.8rem;
    background-color: rgba(28, 34, 43, 0.05);

    font-size: 0.8rem;
    font-family: ${theme.fonts.normal};
  }
`

export const HeaderButtonStyled = styled.button`
  display: flex;
  align-items: center;
  justify-content: flex-start;

  gap: 0.5rem;
  padding: 0;

  border: 0;
  opacity: 0.35;
  background-color: transparent;

  color: ${theme.colorsNew.darkGrey500};
  font-size: 0.9rem;
  font-family: ${theme.fonts.normal};

  img {
    width: 0.8rem;
    height: 0.8rem;
  }

  :hover,
  :focus {
    opacity: 1;
  }

  :not(:first-of-type),
  :not(:nth-of-type(1)) {
    justify-content: center;
  }
`

export const CloseButtonStyled = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;

  padding: 0;
  position: absolute;
  right: 1.5rem;
  top: 1.5rem;

  border: none;
  opacity: 0.5;
  background: none;
  img {
    width: 0.9rem;
    height: 0.9rem;
  }
  :hover,
  :focus {
    opacity: 1;
  }
`

export const ListStyled = styled.div`
  display: flex;
  flex-direction: column;

  padding: 0 1.5rem 1.5rem;
  max-height: 27rem;

  overflow-y: auto;
`

export const ListItemStyled = styled.div`
  display: grid;
  align-items: center;

  gap: 0.5rem;
  padding: 0.8rem 0;
  grid-template-columns: 1fr 0.9fr 1fr 1.4rem 1fr 0.6fr;

  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
`

export const TimeAvatarBlockStyled = styled.div`
  display: flex;
  align-items: center;

  gap: 0.8rem;
`

export const TimeBlockStyled = styled.button`
  display: flex;
  align-items: center;

  gap: 0.3rem;
  padding: 0;
  min-width: 3.5rem;

  border: 0;
  background-color: transparent;

  color: ${theme.colorsNew.darkGrey500};
  font-size: 0.85rem;
  font-family: ${theme.fonts.light};
  line-height: normal;

  img {
    width: 1.1rem;
    height: 1.1rem;
  }
`

export const AvatarWrapStyled = styled.button`
  width: 2rem;
  height: 2rem;
  padding: 0;
  border: 0;
  background-color: transparent;
`

export const CompanyAvatarStyled = styled(CompanyAvatar)`
  width: 2rem;
  height: 2rem;
  font-size: 0.85rem;
`

export const CategoryStyled = styled.div`
  display: flex;
  align-items: center;
  justify-self: flex-start;

  gap: 0.3rem;
  padding: 0.2rem 0.6rem 0.2rem 0.4rem;

  border: 1px solid rgba(10, 12, 17, 0.1);
  border-radius: 0.8rem;
  background-color: rgba(28, 34, 43, 0.02);

  color: ${theme.colorsNew.darkGrey500};
  font-size: 0.775rem;
  font-family: ${theme.fonts.normal};
  line-height: normal;

  img {
    width: 0.85rem;
    height: 0.85rem;
  }
`

export const ChangesTextStyled = styled.p`
  color: ${theme.colorsNew.darkGrey500};
  font-size: 0.875rem;
  text-align: center;
  font-family: ${theme.fonts.normal};
  line-height: normal;
`

export const ArrowRightStyled = styled.img`
  width: 0.8rem;
  height: 0.8rem;
  margin: auto;
`

export const StatusStyled = styled.div<{ $status?: string }>`
  display: flex;
  align-items: center;
  justify-content: center;

  padding: 0.3rem;

  border-radius: 0.8rem;
  background-color: ${props => {
    switch (props.$status) {
      case 'added':
        return 'rgba(34, 197, 94, 0.1)'
      case 'deleted':
        return 'rgba(239, 68, 68, 0.1)'
      case 'modified':
        return 'rgba(59, 130, 246, 0.1)'
      default:
        return 'rgba(28, 34, 43, 0.05)'
    }
  }};

  color: ${props => {
    switch (props.$status) {
      case 'added':
        return '#16a34a'
      case 'deleted':
        return '#dc2626'
      case 'modified':
        return '#2563eb'
      default:
        return theme.colorsNew.darkGrey500
    }
  }};
  font-size: 0.8rem;
  font-family: ${theme.fonts.normal};
  line-height: normal;
`

export const LoadingStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: ${theme.colorsNew.darkGrey500};
  font-size: 0.9rem;
  font-family: ${theme.fonts.normal};
`

export const TooltipStyled = styled(Popover)`
  min-width: 6rem;
  width: unset;
  padding: 0.5rem 1rem;
  max-width: 12rem;
  margin-bottom: 0.5rem;

  border: 0;
  border-radius: 0.6rem;
  background-color: #242a38;

  color: #fff;
  font-size: 0.8rem;
  font-family: ${theme.fonts.regular};
  line-height: normal;

  .arrow {
    :before,
    :after {
      border-top-color: #242a38;
    }
  }
  span {
    font-family: ${theme.fonts.bold};
  }
`

export const EmptyListStyled = styled.p`
  margin-top: 1rem;

  color: ${theme.colorsNew.darkGrey500};
  font-size: 0.95rem;
  text-align: center;
  font-family: ${theme.fonts.light};
`
