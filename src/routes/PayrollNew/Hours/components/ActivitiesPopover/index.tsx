import React, { forwardRef, useEffect, useState } from 'react'
import OverlayTrigger from 'react-bootstrap/OverlayTrigger'
import { I18n } from 'react-redux-i18n'

import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'

import { database } from '../../../../../index'

import {
  PopoverStyled,
  ContainerStyled,
  HeaderWrapStyled,
  HeaderStyled,
  HeaderTitleStyled,
  HeaderButtonStyled,
  CloseButtonStyled,
  ListStyled,
  ListItemStyled,
  TimeAvatarBlockStyled,
  TimeBlockStyled,
  AvatarWrapStyled,
  CompanyAvatarStyled,
  CategoryStyled,
  ChangesTextStyled,
  ArrowRightStyled,
  StatusStyled,
  LoadingStyled,
  TooltipStyled,
  EmptyListStyled
} from '../../../styles/ActivitiesPopover.styles'

import { Company } from 'types/company'
import { IEmployee } from 'types/employee'

import arrowRightIcon from 'img/icons/arrowRightThicIcon.svg'
import closeIcon from 'img/icons/closeIcon.svg'
import categoriesIcon from 'img/icons/folderOpenIcon.svg'
import clockIcon from 'img/icons/hoursIcon.svg'
import plusIcon from 'img/icons/plusIcon.svg'
import trashIcon from 'img/icons/trashNewIcon.svg'
import sortIcon from 'img/icons/upDownIcon.svg'
import roleIcon from 'img/icons/userNewIcon.svg'

dayjs.extend(relativeTime)

// Helper function to format time values - moved outside component for performance
const formatTimeValue = (value: string | number | undefined): string => {
  if (value === undefined || value === null) return 'N/A'
  if (typeof value === 'number') {
    const hours = Math.floor(value / 60)
    const minutes = value % 60
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`
  }
  return String(value)
}

// Helper function to get relative time - moved outside component for performance
const getRelativeTime = (timestamp: number): string => {
  return dayjs(timestamp).fromNow()
}

// Helper function to get category icon - moved outside component for performance
const getCategoryIcon = (action: string): string => {
  switch (action) {
    case 'start-end':
    case 'start':
    case 'end':
    case 'break':
    case 'unplanned':
      return clockIcon
    case 'role':
      return roleIcon
    case 'add-shift':
    case 'add-break':
      return plusIcon
    case 'delete-shift':
    case 'delete-break':
      return trashIcon
    default:
      return clockIcon
  }
}

// Helper function to filter and sort activities - moved outside component for performance  
const filterAndSortActivities = (
  activities: ActivityLogEntry[],
  sortBy: 'timestamp' | 'action',
  sortOrder: 'asc' | 'desc',
  filterCategory: string
): ActivityLogEntry[] => {
  let filtered = activities

  if (filterCategory !== 'all') {
    filtered = activities.filter(
      activity => activity.action === filterCategory
    )
  }

  return filtered.sort((a, b) => {
    if (sortBy === 'timestamp') {
      return sortOrder === 'desc'
        ? b.timestamp - a.timestamp
        : a.timestamp - b.timestamp
    } else {
      const aValue = a.action
      const bValue = b.action
      return sortOrder === 'desc'
        ? bValue.localeCompare(aValue)
        : aValue.localeCompare(bValue)
    }
  })
}

// Activity log types
type ActivityLogEntry = {
  id: string
  timestamp: number
  authorId: string
  authorName: string
  authorAvatar?: string
  action:
    | 'start-end'
    | 'role'
    | 'start'
    | 'end'
    | 'delete-shift'
    | 'add-shift'
    | 'break'
    | 'unplanned'
    | 'add-break'
    | 'delete-break'
  oldValue?: string | number
  newValue?: string | number
  status: 'modified' | 'added' | 'deleted'
}

type ActivitiesPopoverProps = {
  onClose: () => void
  employee?: IEmployee
  date?: string
  currentCompany?: Company
}

const ActivitiesPopover = forwardRef<HTMLDivElement, ActivitiesPopoverProps>(
  ({ onClose, employee, date, currentCompany, ...rest }, ref) => {
    const [activities, setActivities] = useState<ActivityLogEntry[]>([])
    const [loading, setLoading] = useState(true)
    const [sortBy, setSortBy] = useState<'timestamp' | 'action'>('timestamp')
    const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
    const [filterCategory, setFilterCategory] = useState<string>('all')

    // Load activity log from Firebase
    useEffect(() => {
      if (!employee?.uid || !date || !currentCompany?.key) {
        setLoading(false)
        return
      }

      const activityRef = database
        .ref(
          `AttendanceActivityLog/${currentCompany.key}/${date}/${employee.uid}`
        )
        .orderByChild('timestamp')

      const unsubscribe = activityRef.on('value', snapshot => {
        const data = snapshot.val() || {}
        const activityList: ActivityLogEntry[] = Object.entries(data).map(
          ([id, activity]) => ({
            id,
            ...(activity as Omit<ActivityLogEntry, 'id'>)
          })
        )

        setActivities(activityList.reverse()) // Most recent first
        setLoading(false)
      })

      return () => {
        activityRef.off('value', unsubscribe)
      }
    }, [employee?.uid, date, currentCompany?.key])

    // Sort and filter activities
    const filteredAndSortedActivities = React.useMemo(() => {
      return filterAndSortActivities(activities, sortBy, sortOrder, filterCategory)
    }, [activities, sortBy, sortOrder, filterCategory])

    const categories = [
      {
        value: 'all',
        label: I18n.t('common.all', { defaultValue: 'All' }),
        icon: categoriesIcon
      },
      {
        value: 'start-end',
        label: I18n.t('payroll.start_end'),
        icon: clockIcon
      },
      {
        value: 'role',
        label: I18n.t('payroll.role'),
        icon: roleIcon
      },
      {
        value: 'start',
        label: I18n.t('payroll.start'),
        icon: clockIcon
      },
      {
        value: 'end',
        label: I18n.t('payroll.end'),
        icon: clockIcon
      },
      {
        value: 'delete-shift',
        label: I18n.t('payroll.delete_shift'),
        icon: trashIcon
      },
      {
        value: 'add-shift',
        label: I18n.t('payroll.add_shift'),
        icon: plusIcon
      },
      {
        value: 'break',
        label: I18n.t('payroll.break'),
        icon: clockIcon
      },
      {
        value: 'unplanned',
        label: I18n.t('payroll.unplanned'),
        icon: clockIcon
      },
      {
        value: 'add-break',
        label: I18n.t('payroll.add_break'),
        icon: plusIcon
      },
      {
        value: 'delete-break',
        label: I18n.t('payroll.delete_break'),
        icon: trashIcon
      }
    ]

    const statuses = [
      {
        value: 'modified',
        label: I18n.t('payroll.modified')
      },
      {
        value: 'added',
        label: I18n.t('payroll.added')
      },
      {
        value: 'deleted',
        label: I18n.t('payroll.deleted')
      }
    ]
    return (
      <PopoverStyled
        {...rest}
        id='payroll_activities-popover'
        ref={ref}
      >
        <ContainerStyled>
          <HeaderWrapStyled>
            <HeaderStyled>
              <HeaderTitleStyled>
                {I18n.t('payroll.activity_log')}
                <span>{filteredAndSortedActivities.length}</span>
              </HeaderTitleStyled>
              <HeaderButtonStyled
                onClick={() =>
                  setFilterCategory(
                    filterCategory === 'all' ? 'start-end' : 'all'
                  )
                }
                style={{ opacity: filterCategory !== 'all' ? 1 : 0.35 }}
              >
                <img
                  src={categoriesIcon}
                  alt=''
                />
                {filterCategory === 'all'
                  ? I18n.t('payroll.categories')
                  : categories.find(c => c.value === filterCategory)?.label}
              </HeaderButtonStyled>
              <HeaderButtonStyled
                onClick={() => {
                  if (sortBy === 'action') {
                    setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
                  } else {
                    setSortBy('action')
                    setSortOrder('asc')
                  }
                }}
                style={{ opacity: sortBy === 'action' ? 1 : 0.35 }}
              >
                <img
                  src={sortIcon}
                  alt=''
                />
                {I18n.t('payroll.action', { defaultValue: 'Action' })}
              </HeaderButtonStyled>
              <div />
              <HeaderButtonStyled
                onClick={() => {
                  if (sortBy === 'timestamp') {
                    setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
                  } else {
                    setSortBy('timestamp')
                    setSortOrder('desc')
                  }
                }}
                style={{ opacity: sortBy === 'timestamp' ? 1 : 0.35 }}
              >
                <img
                  src={sortIcon}
                  alt=''
                />
                {I18n.t('payroll.time', { defaultValue: 'Time' })}
              </HeaderButtonStyled>
              <div />
              <CloseButtonStyled onClick={onClose}>
                <img
                  src={closeIcon}
                  alt='Close'
                />
              </CloseButtonStyled>
            </HeaderStyled>
          </HeaderWrapStyled>

          <ListStyled>
            {loading ? (
              <LoadingStyled>
                {I18n.t('common.loading', { defaultValue: 'Loading...' })}
              </LoadingStyled>
            ) : filteredAndSortedActivities.length === 0 ? (
              <EmptyListStyled>
                {I18n.t('payroll.no_activity_to_date', {
                  defaultValue: 'No activity recorded for this shift'
                })}
              </EmptyListStyled>
            ) : (
              filteredAndSortedActivities.map(activity => (
                <ListItemStyled key={activity.id}>
                  <TimeAvatarBlockStyled>
                    <OverlayTrigger
                      trigger={['hover', 'focus']}
                      placement='top'
                      overlay={
                        <TooltipStyled
                          id={`payroll_activities-popover-time-${activity.id}`}
                        >
                          {dayjs(activity.timestamp).format(
                            'MMMM D, YYYY [at] h:mm A'
                          )}
                        </TooltipStyled>
                      }
                    >
                      <TimeBlockStyled>
                        <img
                          src={clockIcon}
                          alt=''
                        />
                        {getRelativeTime(activity.timestamp)}
                      </TimeBlockStyled>
                    </OverlayTrigger>

                    <OverlayTrigger
                      trigger={['hover', 'focus']}
                      placement='top'
                      overlay={
                        <TooltipStyled
                          id={`payroll_activities-popover-avatar-${activity.id}`}
                        >
                          {activity.authorName}
                        </TooltipStyled>
                      }
                    >
                      <AvatarWrapStyled>
                        <CompanyAvatarStyled
                          avatar={activity.authorAvatar}
                          initials={activity.authorName
                            .split(' ')
                            .map(n => n[0])
                            .join('')
                            .toUpperCase()}
                        />
                      </AvatarWrapStyled>
                    </OverlayTrigger>
                  </TimeAvatarBlockStyled>

                  <CategoryStyled>
                    <img
                      src={getCategoryIcon(activity.action)}
                      alt=''
                    />
                    {categories.find(c => c.value === activity.action)?.label ||
                      activity.action}
                  </CategoryStyled>

                  <ChangesTextStyled>
                    {formatTimeValue(activity.oldValue)}
                  </ChangesTextStyled>

                  <ArrowRightStyled
                    src={arrowRightIcon}
                    alt=''
                  />

                  <ChangesTextStyled>
                    {formatTimeValue(activity.newValue)}
                  </ChangesTextStyled>

                  <StatusStyled $status={activity.status}>
                    {statuses.find(s => s.value === activity.status)?.label ||
                      activity.status}
                  </StatusStyled>
                </ListItemStyled>
              ))
            )}
          </ListStyled>
        </ContainerStyled>
      </PopoverStyled>
    )
  }
)

export default ActivitiesPopover
