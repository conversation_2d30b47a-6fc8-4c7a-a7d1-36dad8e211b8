import React from 'react'
import useOnclickOutside from 'react-cool-onclickoutside'
import { I18n } from 'react-redux-i18n'

import {
  DepartmentRoles,
  RoleFilterState,
  RoleFilterItem,
  DepartmentType,
  saveRoleFilterState
} from 'utils/payroll/roleFilterUtils'

import {
  DrawerStyled,
  DrawerBlockStyled,
  DrawerBlockHeaderStyled,
  DrawerBlockHeaderTitleStyled,
  DrawerBlockListStyled,
  ListItemStyled,
  SwitchStyled,
  SwitchButtonStyled,
  SingleRoleMessageStyled
} from './FilterDrawer/styles'

// Helper function to handle department switch logic - moved outside component for performance
const handleDepartmentSwitch = (
  isDepartmentSelected: boolean,
  department: DepartmentType,
  roles: RoleFilterItem[],
  filterState: RoleFilterState
): RoleFilterState => {
  const newState = { ...filterState }

  if (isDepartmentSelected) {
    // Deselect all roles in this department
    newState.selectedRoles[department] = []
    roles.forEach(role => {
      newState.selectedSubcategories[role.id] = []
    })

    // Remove department from selectedDepartments if no roles are selected
    newState.selectedDepartments = newState.selectedDepartments.filter(d => d !== department)
  } else {
    // Select all roles in this department
    newState.selectedRoles[department] = roles.map(role => role.id)
    roles.forEach(role => {
      newState.selectedSubcategories[role.id] = role.subcategories.map(sub => sub.id)
    })

    // Add department to selectedDepartments if not already there
    if (!newState.selectedDepartments.includes(department)) {
      newState.selectedDepartments = [...newState.selectedDepartments, department]
    }
  }

  return newState
}

// Helper function to handle role click logic - moved outside component for performance
const handleRoleToggle = (
  roleId: string,
  department: DepartmentType,
  roles: RoleFilterItem[],
  filterState: RoleFilterState
): RoleFilterState => {
  const newState = { ...filterState }
  const role = roles.find(r => r.id === roleId)
  if (!role) return newState

  const currentSelectedRoles = newState.selectedRoles[department] || []
  const isRoleSelected = currentSelectedRoles.includes(roleId)

  if (isRoleSelected) {
    // Deselect role
    const remainingRoles = currentSelectedRoles.filter(id => id !== roleId)
    newState.selectedSubcategories[roleId] = []
    newState.selectedRoles[department] = remainingRoles

    // Update department selection based on remaining roles
    if (remainingRoles.length === 0) {
      // If no roles left in department, remove department from selectedDepartments
      newState.selectedDepartments = newState.selectedDepartments.filter(d => d !== department)
    }
  } else {
    // Select role
    newState.selectedRoles[department] = [...currentSelectedRoles, roleId]
    newState.selectedSubcategories[roleId] = role.subcategories.map(sub => sub.id)

    // Add department to selectedDepartments if not already there
    if (!newState.selectedDepartments.includes(department)) {
      newState.selectedDepartments = [...newState.selectedDepartments, department]
    }
  }

  return newState
}

// Helper function to get department title - moved outside component for performance
const getDepartmentTitle = (department: DepartmentType): string => {
  switch (department) {
    case 'FOH':
      return I18n.t('schedule.frontOfHouse')
    case 'BOH':
      return I18n.t('schedule.backOfHouse')
    case 'MNG':
      return I18n.t('schedule.management')
    default:
      return department
  }
}

interface FilterDrawerProps {
  show: boolean
  drawerRef: ReturnType<typeof useOnclickOutside>
  departmentRoles: DepartmentRoles
  filterState: RoleFilterState
  onFilterChange: (newState: RoleFilterState) => void
  isSingleRole: boolean
  singleRoleInfo?: {
    department: DepartmentType
    role: RoleFilterItem
  } | null
  onSwitchToRoles?: () => void
  currentDisplayBy: string
}

interface DrawerBlockProps {
  title: string
  department: DepartmentType
  roles: RoleFilterItem[]
  filterState: RoleFilterState
  onFilterChange: (newState: RoleFilterState) => void
  onSwitchToRoles?: () => void
  currentDisplayBy: string
}

const DrawerBlock: React.FC<DrawerBlockProps> = ({
  title,
  department,
  roles,
  filterState,
  onFilterChange,
  onSwitchToRoles,
  currentDisplayBy
}) => {
  const selectedRoleIds = filterState.selectedRoles[department] || []
  // Department is considered "selected" if ALL roles in the department are selected
  const isDepartmentSelected = selectedRoleIds.length === roles.length

  const handleSwitchClick = () => {
    const newState = handleDepartmentSwitch(isDepartmentSelected, department, roles, filterState)

    onFilterChange(newState)
    saveRoleFilterState(newState)

    // Auto-switch to "Roles" view when filter changes, but only if currently in "Employees" view
    if (onSwitchToRoles && currentDisplayBy === 'employees') {
      onSwitchToRoles()
    }
  }

  const handleRoleClick = (roleId: string) => {
    const newState = handleRoleToggle(roleId, department, roles, filterState)

    onFilterChange(newState)
    saveRoleFilterState(newState)

    // Auto-switch to "Roles" view when filter changes, but only if currently in "Employees" view
    if (onSwitchToRoles && currentDisplayBy === 'employees') {
      onSwitchToRoles()
    }
  }

  return (
    <DrawerBlockStyled>
      <DrawerBlockHeaderStyled>
        <DrawerBlockHeaderTitleStyled>{title}</DrawerBlockHeaderTitleStyled>
        <SwitchStyled
          $isActive={isDepartmentSelected}
          onClick={handleSwitchClick}
        >
          <SwitchButtonStyled />
        </SwitchStyled>
      </DrawerBlockHeaderStyled>
      <DrawerBlockListStyled>
        {roles.map((role) => (
          <ListItemStyled
            $isSelected={selectedRoleIds.includes(role.id)}
            key={role.id}
            onClick={() => handleRoleClick(role.id)}
          >
            {role.name}
          </ListItemStyled>
        ))}
      </DrawerBlockListStyled>
    </DrawerBlockStyled>
  )
}

const FilterDrawer = ({
  show,
  drawerRef,
  departmentRoles,
  filterState,
  onFilterChange,
  isSingleRole,
  singleRoleInfo,
  onSwitchToRoles,
  currentDisplayBy
}: FilterDrawerProps) => {
  // For single role companies, show only that department and role
  if (isSingleRole && singleRoleInfo) {
    return (
      <DrawerStyled
        $isVisible={show}
        ref={drawerRef}
      >
        <SingleRoleMessageStyled>
          {I18n.t('payroll.filter')} - {getDepartmentTitle(singleRoleInfo.department)}: {singleRoleInfo.role.name}
        </SingleRoleMessageStyled>
        <DrawerBlock
          title={getDepartmentTitle(singleRoleInfo.department)}
          department={singleRoleInfo.department}
          roles={[singleRoleInfo.role]}
          filterState={filterState}
          onFilterChange={onFilterChange}
          onSwitchToRoles={onSwitchToRoles}
          currentDisplayBy={currentDisplayBy}
        />
      </DrawerStyled>
    )
  }

  // Standard filter for companies with multiple roles
  return (
    <DrawerStyled
      $isVisible={show}
      ref={drawerRef}
    >
      {departmentRoles.FOH.length > 0 && (
        <DrawerBlock
          title={getDepartmentTitle('FOH')}
          department='FOH'
          roles={departmentRoles.FOH}
          filterState={filterState}
          onFilterChange={onFilterChange}
          onSwitchToRoles={onSwitchToRoles}
          currentDisplayBy={currentDisplayBy}
        />
      )}

      {departmentRoles.BOH.length > 0 && (
        <DrawerBlock
          title={getDepartmentTitle('BOH')}
          department='BOH'
          roles={departmentRoles.BOH}
          filterState={filterState}
          onFilterChange={onFilterChange}
          onSwitchToRoles={onSwitchToRoles}
          currentDisplayBy={currentDisplayBy}
        />
      )}

      {departmentRoles.MNG.length > 0 && (
        <DrawerBlock
          title={getDepartmentTitle('MNG')}
          department='MNG'
          roles={departmentRoles.MNG}
          filterState={filterState}
          onFilterChange={onFilterChange}
          onSwitchToRoles={onSwitchToRoles}
          currentDisplayBy={currentDisplayBy}
        />
      )}
    </DrawerStyled>
  )
}

export default FilterDrawer
