import React, { useRef, useState, useEffect, useCallback } from 'react'
import { I18n } from 'react-redux-i18n'

import ShiftCard from './ShiftCard'
import RollingNumber from '../RollingNumber'

import {
  ModalStyled,
  HeaderStyled,
  TitleStyled,
  SubtitleStyled,
  CloseButtonStyled,
  CloseIconStyled,
  BodyStyled,
  ListStyled,
  ListItemStyled,
  BlockStyled,
  PeriodWrapStyled,
  PeriodTabsStyled,
  PeriodItemStyled,
  ScrollContainerStyled,
  TypeSectionStyled,
  CardEmptyStyled,
  SavedAmountStyled
} from '../../../styles/ConflictShiftModal.styles'

import checkIcon from 'img/icons/checkBlueIcon.svg'
import flagIcon from 'img/icons/flagIcon.svg'

// Types for conflict data
interface ConflictShift {
  id: string
  employeeId: string
  employeeName: string
  date: string
  type: ConflictType
  shiftData: any
  resolved?: boolean
}

type ConflictType =
  | 'clocked-in-early'
  | 'missing-end'
  | 'clocked-out-late'
  | 'unplanned-shift'
  | 'shift-too-short'
  | 'shift-under-3-hours'

type ViewOption = 'entire-period' | 'week-1' | 'week-2' | 'today'

interface ConflictShiftModalProps {
  show: boolean
  onHide: () => void
  conflicts?: ConflictShift[]
  onResolveConflict?: (conflictId: string, resolution: any) => void
  attendanceSettings?: any
}

// Helper function to create type ref callback - moved outside component for performance
const createTypeRefSetter = (
  typeId: string,
  typeRefs: React.MutableRefObject<{ [key: string]: HTMLDivElement | null }>
) => (el: HTMLDivElement | null) => {
  typeRefs.current[typeId] = el
}

const ConflictShiftModal: React.FC<ConflictShiftModalProps> = ({
  show,
  onHide,
  conflicts = [],
  onResolveConflict,
  attendanceSettings
}) => {
  const [activeType, setActiveType] = useState<ConflictType>('clocked-in-early')
  const [activePeriod, setActivePeriod] = useState<ViewOption>('entire-period')
  const [resolvedConflicts, setResolvedConflicts] = useState<Set<string>>(new Set())
  const [totalSaved, setTotalSaved] = useState(0)
  const [showSavedAmount, setShowSavedAmount] = useState(false)

  const scrollContainerRef = useRef<HTMLDivElement>(null)
  const typeRefs = useRef<{ [key: string]: HTMLDivElement | null }>({})

  // Filter conflicts based on active period
  const filteredConflicts = useCallback(() => {
    const today = new Date().toISOString().split('T')[0]

    return conflicts.filter(conflict => {
      if (resolvedConflicts.has(conflict.id)) return false

      switch (activePeriod) {
        case 'today':
          return conflict.date === today
        case 'week-1':
          // TODO: Implement week 1 filtering based on payroll period
          return true
        case 'week-2':
          // TODO: Implement week 2 filtering based on payroll period
          return true
        case 'entire-period':
        default:
          return true
      }
    })
  }, [conflicts, activePeriod, resolvedConflicts])

  // Group conflicts by type and calculate counts
  const conflictsByType = useCallback(() => {
    const filtered = filteredConflicts()
    const grouped: { [key in ConflictType]: ConflictShift[] } = {
      'clocked-in-early': [],
      'missing-end': [],
      'clocked-out-late': [],
      'unplanned-shift': [],
      'shift-too-short': [],
      'shift-under-3-hours': []
    }

    filtered.forEach(conflict => {
      if (grouped[conflict.type]) {
        grouped[conflict.type].push(conflict)
      }
    })

    return grouped
  }, [filteredConflicts])

  // Priority order for conflict types (resolved types move to bottom)
  const getTypeList = useCallback(() => {
    const grouped = conflictsByType()
    const typeOrder: ConflictType[] = [
      'clocked-in-early',
      'missing-end',
      'clocked-out-late',
      'unplanned-shift',
      'shift-too-short',
      'shift-under-3-hours'
    ]

    const typesWithConflicts = typeOrder.filter(type => grouped[type].length > 0)
    const typesWithoutConflicts = typeOrder.filter(type => grouped[type].length === 0)

    return [...typesWithConflicts, ...typesWithoutConflicts].map(type => ({
      id: type,
      title: getTypeTitle(type),
      count: grouped[type].length
    }))
  }, [conflictsByType])

  const getTypeTitle = (type: ConflictType): string => {
    switch (type) {
      case 'clocked-in-early': return I18n.t('payroll.clocked_in_early')
      case 'missing-end': return I18n.t('payroll.missing_end')
      case 'clocked-out-late': return I18n.t('payroll.clocked_out_late')
      case 'unplanned-shift': return I18n.t('payroll.unplanned_shift')
      case 'shift-too-short': return I18n.t('payroll.shift_too_short')
      case 'shift-under-3-hours': return I18n.t('payroll.shift_under_3_hours')
      default: return ''
    }
  }

  // Calculate period counts
  const getPeriodList = useCallback(() => {
    const today = new Date().toISOString().split('T')[0]

    return [
      {
        id: 'entire-period' as ViewOption,
        title: I18n.t('payroll.entire_period'),
        count: conflicts.filter(c => !resolvedConflicts.has(c.id)).length
      },
      {
        id: 'week-1' as ViewOption,
        title: I18n.t('payroll.week') + ' 1',
        count: 0 // TODO: Calculate based on actual week 1 conflicts
      },
      {
        id: 'week-2' as ViewOption,
        title: I18n.t('payroll.week') + ' 2',
        count: 0 // TODO: Calculate based on actual week 2 conflicts
      },
      {
        id: 'today' as ViewOption,
        title: I18n.t('payroll.today'),
        count: conflicts.filter(c => c.date === today && !resolvedConflicts.has(c.id)).length
      }
    ]
  }, [conflicts, resolvedConflicts])

  const typeList = getTypeList()
  const periodList = getPeriodList()
  const currentConflicts = conflictsByType()[activeType] || []

  // Handle conflict resolution
  const handleResolveConflict = useCallback((
    conflictId: string,
    resolution: { type: 'approve' | 'delete' | 'claim' | 'modify', savedAmount?: number }
  ) => {
    setResolvedConflicts(prev => new Set([...prev, conflictId]))

    if (resolution.type === 'claim' && resolution.savedAmount) {
      setTotalSaved(prev => prev + (resolution.savedAmount || 0))
    }

    if (onResolveConflict) {
      onResolveConflict(conflictId, resolution)
    }

    // TODO: Show toast notification with undo functionality
  }, [onResolveConflict, setResolvedConflicts, setTotalSaved])

  // Handle undo conflict resolution
  const handleUndoResolve = useCallback((conflictId: string, savedAmount?: number) => {
    setResolvedConflicts(prev => {
      const newSet = new Set(prev)
      newSet.delete(conflictId)
      return newSet
    })

    if (savedAmount) {
      setTotalSaved(prev => Math.max(0, prev - savedAmount))
    }
  }, [setResolvedConflicts, setTotalSaved])

  // Auto-scroll to active type section
  const handleTypeClick = useCallback((typeId: ConflictType) => {
    setActiveType(typeId)
    const targetRef = typeRefs.current[typeId]
    if (targetRef && scrollContainerRef.current) {
      targetRef.scrollIntoView({ behavior: 'smooth', block: 'start' })
    }
  }, [setActiveType])

  // Handle scroll to update active type
  const handleScroll = useCallback(() => {
    if (!scrollContainerRef.current) return

    const container = scrollContainerRef.current
    const containerTop = container.scrollTop
    const containerHeight = container.clientHeight

    // Find which section is most visible
    let mostVisibleType: ConflictType | null = null
    let maxVisibleArea = 0

    typeList.forEach(type => {
      const element = typeRefs.current[type.id as ConflictType]
      if (!element) return

      const elementTop = element.offsetTop - container.offsetTop
      const elementBottom = elementTop + element.offsetHeight

      const visibleTop = Math.max(containerTop, elementTop)
      const visibleBottom = Math.min(containerTop + containerHeight, elementBottom)
      const visibleArea = Math.max(0, visibleBottom - visibleTop)

      if (visibleArea > maxVisibleArea) {
        maxVisibleArea = visibleArea
        mostVisibleType = type.id as ConflictType
      }
    })

    if (mostVisibleType && mostVisibleType !== activeType) {
      setActiveType(mostVisibleType)
    }
  }, [typeList, activeType, setActiveType])

  // Set up scroll listener
  useEffect(() => {
    const container = scrollContainerRef.current
    if (!container) return

    container.addEventListener('scroll', handleScroll)
    return () => container.removeEventListener('scroll', handleScroll)
  }, [handleScroll])

  // Auto-select first type with conflicts when modal opens
  useEffect(() => {
    if (show && typeList.length > 0) {
      const firstTypeWithConflicts = typeList.find(type => type.count > 0)
      if (firstTypeWithConflicts) {
        setActiveType(firstTypeWithConflicts.id as ConflictType)
      }
    }
  }, [show, typeList, setActiveType])

  // Handle modal close with saved amount animation
  const handleClose = useCallback(() => {
    if (totalSaved > 0) {
      setShowSavedAmount(true)
      // TODO: Trigger reclaim animation
      setTimeout(() => {
        setShowSavedAmount(false)
        onHide()
      }, 2000)
    } else {
      onHide()
    }
  }, [totalSaved, onHide, setShowSavedAmount])

  const setTypeRef = (typeId: string) => createTypeRefSetter(typeId, typeRefs)

  // Check if all conflicts are resolved
  const allResolved = currentConflicts.length === 0 &&
    Object.values(conflictsByType()).every(conflicts => conflicts.length === 0)

  return (
    <ModalStyled
      show={show}
      onHide={handleClose}
    >
      <HeaderStyled>
        <TitleStyled>
          <img
            src={flagIcon}
            alt=''
          />
          {I18n.t('payroll.conflicting_shifts')}
        </TitleStyled>
        <SubtitleStyled>
          {I18n.t(
            'payroll.please_check_the_time_cards_below_to_ensure_proper_payroll_management'
          )}
        </SubtitleStyled>
        <CloseButtonStyled onClick={handleClose}>
          <CloseIconStyled />
        </CloseButtonStyled>
      </HeaderStyled>
      <BodyStyled>
        <ListStyled>
          {typeList.map(item => (
            <ListItemStyled
              $isActive={item.id === activeType}
              onClick={() => handleTypeClick(item.id as ConflictType)}
              key={item.id}
            >
              {item.title}
              {item.count > 0 && <span>{item.count}</span>}
            </ListItemStyled>
          ))}
        </ListStyled>
        <BlockStyled>
          <PeriodWrapStyled>
            <PeriodTabsStyled>
              {periodList.map(item => (
                <PeriodItemStyled
                  $isActive={item.id === activePeriod}
                  onClick={() => setActivePeriod(item.id as ViewOption)}
                  key={item.id}
                >
                  {item.title} {item.count > 0 && <span>{item.count}</span>}
                </PeriodItemStyled>
              ))}
            </PeriodTabsStyled>
          </PeriodWrapStyled>
          <ScrollContainerStyled ref={scrollContainerRef}>
            {allResolved ? (
              <CardEmptyStyled>
                <img
                  src={checkIcon}
                  alt=''
                />
                {I18n.t('payroll.you_are_all_caught_up')}
              </CardEmptyStyled>
            ) : (
              typeList.map(type => {
                const typeConflicts = conflictsByType()[type.id as ConflictType] || []
                return (
                  <TypeSectionStyled
                    key={type.id}
                    ref={setTypeRef(type.id)}
                  >
                    <ShiftCard
                      activeType={type.id}
                      conflicts={typeConflicts}
                      onResolveConflict={handleResolveConflict}
                      onUndoResolve={handleUndoResolve}
                      attendanceSettings={attendanceSettings}
                    />
                  </TypeSectionStyled>
                )
              })
            )}
          </ScrollContainerStyled>
        </BlockStyled>
      </BodyStyled>

      {/* Show saved amount animation when closing */}
      {showSavedAmount && totalSaved > 0 && (
        <SavedAmountStyled>
          <RollingNumber value={totalSaved} />
          {I18n.t('payroll.saved')}
        </SavedAmountStyled>
      )}
    </ModalStyled>
  )
}

export default ConflictShiftModal
