import React from 'react'
import { I18n } from 'react-redux-i18n'

import {
  HeaderStyled,
  SettingsButtonStyled,
  SettingsIconWrapStyled,
  SettingsIconStyled,
  TabListStyled,
  TabButtonStyled,
  SubmitButtonStyled
} from './PayrollHeader/styles'

// Icons
import { ReactComponent as HoursIcon } from 'img/icons/hoursIcon.svg'
import { ReactComponent as TipsIcon } from 'img/icons/tipsIcon.svg'
import { ReactComponent as CutsIcon } from 'img/icons/cutsIcon.svg'
import { ReactComponent as SubmitIcon } from 'img/icons/submitIcon.svg'

interface PayrollHeaderProps {
  onSettingsClick: () => void
  onExportClick: () => void
}

export const PayrollHeader: React.FC<PayrollHeaderProps> = ({
  onSettingsClick,
  onExportClick
}) => {
  return (
    <HeaderStyled>
      <SettingsButton onSettingsClick={onSettingsClick} />
      <PayrollTabs />
      <SubmitButton onExportClick={onExportClick} />
    </HeaderStyled>
  )
}

const SettingsButton: React.FC<{ onSettingsClick: () => void }> = ({ onSettingsClick }) => (
  <SettingsButtonStyled onClick={onSettingsClick}>
    <SettingsIconWrapStyled>
      <SettingsIconStyled />
    </SettingsIconWrapStyled>
    {I18n.t('payroll.settings')}
  </SettingsButtonStyled>
)

const PayrollTabs: React.FC = () => (
  <TabListStyled>
    <TabButtonStyled $isActive>
      <HoursIcon />
      {I18n.t('payroll.hours')}
    </TabButtonStyled>
    <TabButtonStyled disabled>
      <TipsIcon />
      {I18n.t('payroll.tips')}
    </TabButtonStyled>
    <TabButtonStyled disabled>
      <CutsIcon />
      {I18n.t('payroll.cuts')}
    </TabButtonStyled>
  </TabListStyled>
)

const SubmitButton: React.FC<{ onExportClick: () => void }> = ({ onExportClick }) => (
  <SubmitButtonStyled onClick={onExportClick}>
    <SubmitIcon />
    {I18n.t('common.submit')}
  </SubmitButtonStyled>
)
