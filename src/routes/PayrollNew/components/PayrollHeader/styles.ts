import styled from 'styled-components'
import { theme } from 'styles/theme'

import { ReactComponent as SettingsIcon } from 'img/icons/settingsIconOutline.svg'

export const HeaderStyled = styled.div`
  display: grid;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 0 1rem;
  grid-template-columns: 1fr 1fr 1fr;
`

export const SettingsButtonStyled = styled.button`
  display: flex;
  align-items: center;
  justify-self: flex-start;
  gap: 0.5rem;
  padding: 0 0.5rem 0 0;
  border: 0;
  background-color: unset;
  color: #a0a8ba;
  font-family: ${theme.fonts.normal};
  font-size: 1rem;

  :hover,
  :focus {
    color: ${theme.colorsNew.darkGrey500};
  }
`

export const SettingsIconWrapStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border: 1px solid currentColor;
  border-radius: 50%;
`

export const SettingsIconStyled = styled(SettingsIcon)`
  width: 1.2rem;
  height: 1.2rem;
  stroke: currentColor;
`

export const TabListStyled = styled.div`
  display: flex;
  align-items: center;
  justify-self: center;
  justify-content: center;
  gap: 0.3rem;
  padding: 0.3rem;
  border-radius: 0.8rem;
  background-color: rgba(229, 235, 239, 0.8);
`

export const TabButtonStyled = styled.button<{ $isActive?: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  flex: 1;
  padding: 0.3rem 1rem;
  min-width: 7.5rem;
  border: none;
  background: ${({ $isActive }) =>
    $isActive ? 'linear-gradient(180deg, #3BBCFF, #2D87FF 100%)' : 'none'};
  border-radius: 0.6rem;
  box-shadow: ${({ $isActive }) =>
    $isActive
      ? '2px 2px 4px -1 rgba(18, 18, 23,0.06),2px 2px 4px -1 rgba(18, 18, 23,0.08)'
      : null};
  color: ${({ $isActive }) =>
    $isActive ? 'white' : theme.colorsNew.darkGrey500};
  font-size: 0.95rem;
  font-family: ${theme.fonts.normal};
  transition: all 0.2s ease-in-out;
  opacity: ${({ $isActive, disabled }) =>
    $isActive ? 1 : disabled ? 0.3 : 0.3};
  cursor: ${props => (props.disabled ? 'not-allowed' : 'pointer')};

  &:hover,
  &:focus {
    opacity: 1;
    background: linear-gradient(180deg, #3bbcff, #2d87ff 100%);
    color: white;
  }

  &:disabled {
    opacity: 0.3;
    background: transparent;
    color: ${theme.colorsNew.darkGrey500};
  }

  svg {
    width: 1.2rem;
    height: 1.2rem;
    fill: currentColor;
  }
`

export const SubmitButtonStyled = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  justify-self: flex-end;
  gap: 0.5rem;
  padding: 0.4rem 0.8rem 0.4rem 0.6rem;
  border: 1px solid currentColor;
  border-radius: 0.8rem;
  background-color: transparent;
  color: #4bccad;
  font-size: 0.9rem;
  font-family: ${theme.fonts.normal};
  transition: all 0.2s ease-in-out;
  cursor: pointer;

  :hover,
  :focus {
    color: #fff;
    background-color: #4bccad;
  }

  svg {
    width: 1.2rem;
    height: 1.2rem;
    stroke: currentColor;
  }
`
