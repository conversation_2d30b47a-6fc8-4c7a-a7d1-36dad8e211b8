import React from 'react'
import PayrollSettingsModal from '../modals/PayrollSettingsModal'
import ExportPayPeriodModal from '../modals/ExportPayPeriodModal'
import { AttendanceSettings } from 'types/attendance'
import { POWERPAY_EARNING_MODE } from 'utils/constants'

interface PayrollModalsProps {
  showSettingsModal: boolean
  showExportModal: boolean
  onCloseSettings: () => void
  onCloseExport: () => void
  onExport: (option: string, format?: 'simplified' | 'detailed' | 'xls' | 'txt') => Promise<void>
  attendanceSettings: AttendanceSettings
  setAttendanceSettings: React.Dispatch<React.SetStateAction<AttendanceSettings>>
  integrationType: string
  jobs?: Record<string, { name: string }>
  maxHoursPerWeek?: number
  powerpayEarningCodesTypePerPosition?: Record<string, POWERPAY_EARNING_MODE>
  updatePowerpayEarningMode?: (positionId: string, earningType: POWERPAY_EARNING_MODE) => Promise<void>
}

export const PayrollModals: React.FC<PayrollModalsProps> = ({
  showSettingsModal,
  showExportModal,
  onCloseSettings,
  onCloseExport,
  onExport,
  attendanceSettings,
  setAttendanceSettings,
  integrationType,
  jobs,
  maxHoursPerWeek,
  powerpayEarningCodesTypePerPosition,
  updatePowerpayEarningMode
}) => {
  return (
    <>
      {showSettingsModal && (
        <PayrollSettingsModal
          showModal={showSettingsModal}
          onClose={onCloseSettings}
          jobs={jobs || {}}
          attendanceSettings={attendanceSettings}
          setAttendanceSettings={setAttendanceSettings}
          maxHoursPerWeek={maxHoursPerWeek || 40}
          integrationType={integrationType}
          powerpayEarningCodesTypePerPosition={powerpayEarningCodesTypePerPosition || {}}
          updatePowerpayEarningMode={updatePowerpayEarningMode || (async () => {})}
        />
      )}

      {showExportModal && (
        <ExportPayPeriodModal
          showModal={showExportModal}
          onClose={onCloseExport}
          onClick={onExport}
          integrationType={integrationType}
        />
      )}
    </>
  )
}
