
# Inline Function Optimization in Payroll Components

## Overview

To improve performance and maintainability, we refactored several Payroll-related components by moving inline functions outside of the render scope. This prevents unnecessary re-creation of functions on every render and makes the codebase easier to test and maintain.

## Optimized Components

The following 8 components were optimized:

1. **Hours/index.tsx**
2. **HoursTable/index.tsx**
3. **CalendarPopover/index.tsx**
4. **PayrollContent.tsx**
5. **FilterDrawer.tsx**
6. **ConflictShiftModal/index.tsx**
7. **ShiftPopover/index.tsx**
8. **ActivitiesPopover/index.tsx**

---

## Example: Before and After

### Before Optimization

Inline functions were defined inside components, causing them to be re-created on every render:

```tsx
const handleSwitchClick = () => {
  // ...logic for department selection...
}

const handleRoleClick = (roleId: string) => {
  // ...logic for role selection...
}
```

### After Optimization

Now, these functions are defined outside the component as pure helpers and invoked from within the component. This approach:
- Reduces render overhead
- Improves code clarity
- Makes logic reusable and testable

```tsx
// Outside component
const handleDepartmentSwitch = (isDepartmentSelected, department, roles, filterState) => {
  // ...logic...
}

const handleRoleToggle = (roleId, department, roles, filterState) => {
  // ...logic...
}

// Inside component
const handleSwitchClick = () => {
  const newState = handleDepartmentSwitch(isDepartmentSelected, department, roles, filterState)
  // ...existing code...
}

const handleRoleClick = (roleId) => {
  const newState = handleRoleToggle(roleId, department, roles, filterState)
  // ...existing code...
}
```

---

## Component-Specific Notes

### 1. Hours/index.tsx
- Moved helper functions for day index and week rendering outside the component.

### 2. HoursTable/index.tsx
- Extracted break duration and popover positioning logic to external helpers.

### 3. CalendarPopover/index.tsx
- Moved period calculation logic outside the component.

### 4. PayrollContent.tsx
- Extracted employee filtering and grouping logic to external helpers.

### 5. FilterDrawer.tsx
- Moved department and role selection logic to external helpers.
- Moved department title formatting outside the component.

### 6. ConflictShiftModal/index.tsx
- Moved type click and ref setter logic outside the component.

### 7. ShiftPopover/index.tsx
- Added utility for date parsing and ensured all heavy calculations use external helpers or React.useMemo.

### 8. ActivitiesPopover/index.tsx
- Moved time formatting, relative time, icon selection, and activity filtering/sorting logic outside the component.

---

## Benefits
- **Performance:** Functions are not re-created on every render.
- **Readability:** Business logic is separated from UI code.
- **Testability:** Helper functions can be unit tested independently.

## Summary

This pattern is now applied in all major Payroll components and is recommended for other components with similar inline logic.
