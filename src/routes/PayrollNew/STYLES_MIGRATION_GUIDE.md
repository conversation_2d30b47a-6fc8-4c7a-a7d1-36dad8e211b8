# Styled Components Migration Guide

This guide explains how to move styled components from component files to dedicated styles files within the Payroll module.

## 🎯 Why This Pattern?

✅ **Separation of Concerns**: Logic and styling are separated  
✅ **Cleaner Components**: Easier to read and understand component logic  
✅ **Reusability**: Styles can be imported and used in other components  
✅ **Maintainability**: Easier to update styles without touching component logic  
✅ **Testing**: Component logic can be tested without style dependencies  
✅ **Performance**: Styles are defined once and reused  

## 📁 Directory Structure


### Centralized Styles Folder (Recommended)

For better organization, place all style files in a single `styles` folder at the module level, and name each file after the component it styles:

```
Payroll/
├── components/
│   ├── HoursTable/
│   │   └── index.tsx
│   ├── PayrollHeader/
│   │   └── index.tsx
│   └── ...
├── styles/
│   ├── HoursTable.styles.ts
│   ├── PayrollHeader.styles.ts
│   └── ...
└── ...
```

**Import styles in your component like:**
```typescript
import { HoursTableStyled } from '../styles/HoursTable.styles'
```

This approach keeps all styles in one place, makes them easy to find, and enforces a clear naming convention.

## 🔧 Step-by-Step Migration

### 1. Create the styles file

Create `ComponentName/styles.ts` with all styled components:

```typescript
import styled from 'styled-components'
import { theme } from 'styles/theme'
import { SomeIcon } from 'img/icons/someIcon.svg'

export const ContainerStyled = styled.div`
  display: flex;
  // ... styles
`

export const ButtonStyled = styled.button<{ $isActive?: boolean }>`
  background: ${({ $isActive }) => $isActive ? '#blue' : '#gray'};
  // ... styles
`

// Export all styled components
```

### 2. Update component imports

Replace styled-components imports with style imports:

```typescript
// Before
import styled from 'styled-components'
import { theme } from 'styles/theme'

// After (centralized styles folder)
import {
  ContainerStyled,
  ButtonStyled,
  // ... other styled components
} from '../styles/ComponentName.styles'
```

### 3. Remove styled components from component

Delete all styled component definitions from the end of the component file.

### 4. Keep component logic only

The component file should only contain:
- React imports
- Business logic hooks
- Event handlers
- JSX rendering
- Helper functions

## 📝 Examples

### ✅ Good - After Migration

**Component file (`ConflictShiftModal/index.tsx`):**
```typescript
import React, { useState } from 'react'
import { I18n } from 'react-redux-i18n'
import {
  ModalStyled,
  HeaderStyled,
  TitleStyled
} from './styles'

const ConflictShiftModal = ({ show, onHide }) => {
  const [activeType, setActiveType] = useState('clocked-in-early')
  
  // ... component logic only
  
  return (
    <ModalStyled show={show} onHide={onHide}>
      <HeaderStyled>
        <TitleStyled>{I18n.t('payroll.conflicting_shifts')}</TitleStyled>
      </HeaderStyled>
    </ModalStyled>
  )
}
```

**Styles file (`ConflictShiftModal/styles.ts`):**
```typescript
import styled from 'styled-components'
import Modal from 'react-bootstrap/Modal'
import { theme } from 'styles/theme'

export const ModalStyled = styled(Modal)`
  .modal-content {
    width: 70vw;
    min-width: 30rem;
  }
`

export const HeaderStyled = styled.div`
  display: flex;
  padding: 0.8rem 1.5rem;
  border-bottom: 1px solid rgba(10, 12, 17, 0.1);
`

export const TitleStyled = styled.div`
  color: #0a0c11;
  font-size: 1rem;
  font-family: ${theme.fonts.normal};
`
```

## 🚀 Priority Components for Migration

Based on complexity and size, migrate in this order:

### High Priority (Large components with many styles)
1. `HoursTable.tsx` - 600+ lines with ~30 styled components
2. `FilterDrawer.tsx` - ✅ **Done**
3. `PayrollSettingsModal.tsx`
4. `ExportPayPeriodModal.tsx`

### Medium Priority
5. `ShiftPopover.tsx`
6. `CalendarPopover.tsx`
7. `PayrollToaster.tsx` - ✅ **Done**

### Low Priority (Smaller components)
8. `RollingNumber.tsx`
9. `DeleteShiftModal.tsx`
10. `ActivitiesPopover.tsx`

## 🧪 Testing After Migration

After migrating each component:

1. **Visual Test**: Ensure the component renders correctly
2. **Functionality Test**: Verify all interactions work
3. **Style Test**: Check responsive behavior
4. **Import Test**: Ensure no import errors

## 📋 Migration Checklist

For each component:

- [ ] Create `styles.ts` file
- [ ] Move all styled components to styles file
- [ ] Export all styled components
- [ ] Update component imports
- [ ] Remove styled-components imports from component
- [ ] Remove styled component definitions from component
- [ ] Test component functionality
- [ ] Verify no style regressions
- [ ] Check for any missing imports
- [ ] Update component documentation

## 🛡️ Best Practices

1. **Naming**: Keep original styled component names for consistency
2. **Exports**: Always export styled components (don't use default exports)
3. **Types**: Include TypeScript props for styled components
4. **Organization**: Group related styled components together
5. **Comments**: Add comments for complex styling logic

## 🔄 Maintenance

- **Adding new styles**: Add to the styles file, not the component
- **Updating styles**: Modify only the styles file
- **Reusing styles**: Import from styles file in other components
- **Refactoring**: Consider moving common styles to shared style files

## ⚠️ Common Pitfalls


1. **Forgetting to export**: Always export styled components
2. **Import conflicts**: Remove old styled component definitions
3. **Missing dependencies**: Include all icon/component imports in styles
4. **Type errors**: Maintain TypeScript props for styled components
5. **Theme imports**: Ensure theme is imported in styles file
6. **Incorrect asset paths**: When moving styled components to a new file, update any relative import paths for SVGs, images, or other assets. Prefer using absolute paths from the `src` directory if your project supports it, or adjust the relative path as needed. Double-check the actual file name and location to avoid "Module not found" errors.

This pattern makes the codebase much more maintainable and follows React best practices!
