import React, { useState } from 'react'
import { useSelector } from 'react-redux'

// Types
import { RootState } from 'store/reducers'

// Styled Components
import {
  ContainerStyled,
  LoadingMessage,
  MainStyled
} from './styles/Payroll.styles'

// Utils
import { usePayrollIntegrations } from 'utils/hooks/usePayrollIntegrations'

// Hooks
import { usePayrollSettings } from './hooks/usePayrollSettings'
import { usePayrollIntegration } from './hooks/usePayrollIntegration'
import { usePayrollExport } from './hooks/usePayrollExport'

// Components
import PayrollContent from './PayrollContent'
import { PayrollHeader } from './components/PayrollHeader'
import { PayrollModals } from './components/PayrollModals'
import { PeriodProvider } from 'contexts/PeriodContext'

const Payroll: React.FC = () => {
  const currentCompany = useSelector((state: RootState) =>
    state.companies.find(company => company.key === state.currentCompanyId) ||
    { key: '', payrollStartingDay: 'Monday' } as any
  )

  // Modal states
  const [showSettingsModal, setShowSettingsModal] = useState(false)
  const [showExportModal, setShowExportModal] = useState(false)

  // Custom hooks for business logic
  const { attendanceSettings, setAttendanceSettings, isLoading: settingsLoading } = usePayrollSettings(
    currentCompany.key,
    currentCompany.payrollStartingDay
  )

  const { coNumber, integrationType, isLoading: integrationLoading } = usePayrollIntegration(currentCompany.key)

  const { handleExport } = usePayrollExport({
    coNumber,
    integrationType,
    onExportComplete: () => setShowExportModal(false)
  })

  // Employee integrations and payroll data
  usePayrollIntegrations(currentCompany.key)

  // Loading state
  if (settingsLoading || integrationLoading) {
    return (
      <ContainerStyled>
        <LoadingMessage>Loading payroll data...</LoadingMessage>
      </ContainerStyled>
    )
  }

  return (
    <PeriodProvider initialAttendanceSettings={attendanceSettings}>
      <ContainerStyled>
        <PayrollHeader
          onSettingsClick={() => setShowSettingsModal(true)}
          onExportClick={() => setShowExportModal(true)}
        />

        <MainStyled>
          <PayrollContent
            attendanceSettings={attendanceSettings}
            setAttendanceSettings={setAttendanceSettings}
          />
        </MainStyled>

        <PayrollModals
          showSettingsModal={showSettingsModal}
          showExportModal={showExportModal}
          onCloseSettings={() => setShowSettingsModal(false)}
          onCloseExport={() => setShowExportModal(false)}
          onExport={handleExport}
          attendanceSettings={attendanceSettings}
          setAttendanceSettings={setAttendanceSettings}
          integrationType={integrationType}
        />
      </ContainerStyled>
    </PeriodProvider>
  )
}

export default Payroll
