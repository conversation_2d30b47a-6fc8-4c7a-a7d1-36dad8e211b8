import React, { useState, useEffect, useMemo } from 'react'
import { useSelector } from 'react-redux'
import { I18n } from 'react-redux-i18n'
import { RootState } from 'store/reducers'
import { usePeriod } from 'contexts/PeriodContext'
import { database } from '../../index'

// Types
import { AttendanceShifts, AttendanceSettings } from 'types/attendance'
import { IEmployee } from 'types/employee'
import { IPosition } from 'types/company'

// Utils
import {
  getEmployeesToDisplay,
  sortEmployees,
  mergeEmployeesWithIntegrations
} from 'utils/payroll/employeeUtils'
import { useEmployeeIntegrationIds } from 'utils/hooks/useEmployeeIntegrationIds'
import { containsString } from 'utils/removeDiacriticsString'

// Components
import Hours from './Hours'

// Icons
import { ReactComponent as EmployeesIcon } from 'img/icons/filterByEmployeesIcon.svg'
import { ReactComponent as RolesIcon } from 'img/icons/filterByRolesIcon.svg'

// Helper function to filter employees - moved outside component for performance
const filterEmployees = (
  employeesToDisplay: IEmployee[],
  searchTerm: string,
  selectedPositionId: string
) => {
  return employeesToDisplay.filter(employee => {
    // Search filter
    const matchesSearch = !searchTerm || 
      containsString(employee.name || '', searchTerm) ||
      containsString(employee.email || '', searchTerm)

    // Position filter
    const matchesPosition = !selectedPositionId ||
      employee.positions?.some(
        ({ categoryId }) => categoryId === selectedPositionId
      )

    return matchesSearch && matchesPosition
  })
}

// Helper function to group employees by role - moved outside component for performance
const groupEmployeesByRole = (
  filteredEmployees: IEmployee[],
  currentCompanyJobs: any
): { [roleId: string]: { role: IPosition; employees: IEmployee[] } } => {
  const grouped: {
    [roleId: string]: { role: IPosition; employees: IEmployee[] }
  } = {}

  // First, initialize all non-archived roles with empty employee arrays
  Object.entries(currentCompanyJobs || {}).forEach(([jobId, job]) => {
    const role = job as IPosition
    if (!role.archived && role.name && role.name.trim() !== '') {
      grouped[jobId] = {
        role: role,
        employees: []
      }
    }
  })

  // Then, populate with actual employees
  filteredEmployees.forEach(employee => {
    employee.positions?.forEach(position => {
      const role = currentCompanyJobs?.[position.categoryId]
      if (role && grouped[position.categoryId]) {
        if (!grouped[position.categoryId].employees.find(e => e.uid === employee.uid)) {
          grouped[position.categoryId].employees.push(employee)
        }
      }
    })
  })

  return grouped
}

interface PayrollContentProps {
  attendanceSettings: AttendanceSettings
  setAttendanceSettings: (settings: AttendanceSettings) => void
}

const PayrollContent: React.FC<PayrollContentProps> = ({
  attendanceSettings,
  setAttendanceSettings
}) => {
  const currentCompany = useSelector((state: RootState) =>
    state.companies.find(company => company.key === state.currentCompanyId) ||
    { key: '', jobs: {} } as any
  )
  const allEmployees = useSelector((state: RootState) => state.employees.employees)
  
  // Period context
  const { period } = usePeriod()
  const { startOfPeriodStr, endOfPeriodStr } = period

  // Local state
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedPositionId, setSelectedPositionId] = useState('')
  const [employeeOrder] = useState('name')
  const [displayBy, setDisplayBy] = useState('employee')
  const [attendanceData, setAttendanceData] = useState<AttendanceShifts>({})
  const [isDataLoaded, setIsDataLoaded] = useState(false)

  // Employee integrations
  const integrationsIds = useEmployeeIntegrationIds(currentCompany.key)

  // Load attendance data when period changes
  useEffect(() => {
    let attendanceRef: ReturnType<typeof database.ref> | null = null

    const loadData = async () => {
      if (currentCompany.key) {
        const query = database
          .ref('Attendance/' + currentCompany.key)
          .orderByKey()
          .startAt(startOfPeriodStr)
          .endAt(endOfPeriodStr)

        attendanceRef = database.ref('Attendance/' + currentCompany.key)

        query.on('value', (s) => {
          const data = s.val() || {}
          setAttendanceData(data)
          setIsDataLoaded(true)
        })
      }
    }

    loadData()

    return () => {
      if (attendanceRef) {
        attendanceRef.off()
      }
    }
  }, [currentCompany.key, startOfPeriodStr, endOfPeriodStr])

  // Process employees with integrations
  const employeesWithIntegrations = useMemo(() => {
    return mergeEmployeesWithIntegrations(allEmployees, integrationsIds)
  }, [allEmployees, integrationsIds])

  // Get employees to display
  const employeesToDisplay = useMemo(() => {
    const filteredEmployees = getEmployeesToDisplay(employeesWithIntegrations, attendanceData, currentCompany.jobs)
    return sortEmployees(filteredEmployees, employeeOrder, false)
  }, [employeesWithIntegrations, employeeOrder, attendanceData, currentCompany.jobs])

  // Filter employees based on search and position
  const filteredEmployees = useMemo(() => {
    return filterEmployees(employeesToDisplay, searchTerm, selectedPositionId)
  }, [employeesToDisplay, searchTerm, selectedPositionId])

  // Group employees by role
  const employeesByRole = useMemo(() => {
    return groupEmployeesByRole(filteredEmployees, currentCompany.jobs)
  }, [filteredEmployees, currentCompany.jobs])

  // Display options
  const displayByArray = [
    { id: 'role', label: I18n.t('navbar.roles'), icon: <RolesIcon /> },
    { id: 'employee', label: I18n.t('navbar.employees'), icon: <EmployeesIcon /> }
  ]

  // Search handler
  const onSearchEmployee = (value: string) => {
    setSearchTerm(value)
  }

  return (
    <Hours
      employeesArray={filteredEmployees}
      employeesByRole={employeesByRole}
      searchTerm={searchTerm}
      setSearchTerm={setSearchTerm}
      selectedPositionId={selectedPositionId}
      setSelectedPositionId={setSelectedPositionId}
      currentCompany={currentCompany}
      displayBy={displayBy}
      setDisplayBy={setDisplayBy}
      displayByArray={displayByArray}
      onSearchEmployee={onSearchEmployee}
      attendanceSettings={attendanceSettings}
      setAttendanceSettings={setAttendanceSettings}
      hasPayrollIntegration={false} // TODO: Calculate this properly
    />
  )
}

export default PayrollContent
