import React, { useCallback, useState } from 'react'
import { I18n } from 'react-redux-i18n'

import ExportOptions from '../../components/ExportOptions'

import { EMPLOYEURD, POWERPAY } from 'utils/constants'

import checkIcon from 'img/icons/checkPurpleFat.svg'
import closeIcon from 'img/icons/closePurple.svg'

import {
  ModalStyled,
  ModalHeaderStyled,
  ModalBodyStyled,
  SubtitleStyled,
  CheckboxStyled,
  RowBlockStyled,
  RowLabelStyled,
  RowStyled,
  RowTextStyled,
  ModalFooterStyled,
  ExportIcontStyled,
  ButtonStyled,
  PowerPayInfoStyled
} from '../../styles/ExportPayPeriodModal.styles'

import { InputWithPrefix } from '../../../../components/ui/InputWithPrefix'

export type ExportFormat = 'simplified' | 'detailed' | 'xls' | 'txt'

const getExportOptions = (integrationType: string) => {
  const options: {
    value: string
    title: string
    description: string
    exportOptions?: ExportFormat[]
  }[] = [
    {
      value: 'excel',
      title: I18n.t('attendance.excelSpreadsheet'),
      description: I18n.t('attendance.willDownloadExcel'),
      exportOptions: ['simplified', 'detailed']
    },
    {
      value: 'payroll',
      title:
        integrationType === EMPLOYEURD
          ? I18n.t('attendance.employeurd_export_format')
          : I18n.t('attendance.nethris_export_format'),
      description: I18n.t('attendance.will_be_download_as_a_text_document'),
      exportOptions: ['txt', 'xls']
    },
    {
      value: 'by-shift',
      title: I18n.t('attendance.by_shift_export_format'),
      description: I18n.t('attendance.willDownloadExcel')
    },
    {
      value: 'pay-evolution',
      title: I18n.t('attendance.payEvolutionExportFormat'),
      description: I18n.t('attendance.willDownloadExcel')
    }
  ]

  if (integrationType === POWERPAY) {
    options.push({
      value: 'powerpay',
      title: I18n.t('attendance.powerpayExportFormat'),
      description: I18n.t('attendance.willDownloadXML')
    })
  }

  return options
}

type Props = {
  showModal: boolean
  onClose: () => void
  onClick: (
    option: string,
    selectedExportOption: ExportFormat,
    extraData?: {
      powerpay?: {
        branchId?: string
        payrollNumber?: string
      }
    }
  ) => void
  integrationType: string
}

function ExportPayPeriodModal({
  showModal,
  onClose,
  onClick,
  integrationType
}: Props) {
  const [option, setOption] = useState('excel')
  const [exportFormat, setExportFormat] = useState<ExportFormat>('simplified')

  // Powerpay
  const [powerpayBranchId, setPowerpayBranchId] = useState('')
  const [powerpayPayrollNumber, setPowerpayPayrollNumber] = useState('')

  const onExport = () => {
    const optionalPayload =
      integrationType === POWERPAY
        ? {
            powerpay: {
              branchId: powerpayBranchId,
              payrollNumber: powerpayPayrollNumber
            }
          }
        : undefined

    onClick(option, exportFormat, optionalPayload)
  }

  const exportOptions = getExportOptions(integrationType)

  const updateExportOption = (option: string) => {
    setOption(option)
    switch (option) {
      case 'excel':
        setExportFormat('simplified')
        break
      case 'payroll':
        setExportFormat('txt')
        break
    }
  }

  return (
    <ModalStyled
      show={showModal}
      onHide={onClose}
    >
      <ModalHeaderStyled>
        <ExportIcontStyled />
        {I18n.t('attendance.exportPayPeriod')}
        <button onClick={onClose}>
          <img
            src={closeIcon}
            alt=''
          />
        </button>
      </ModalHeaderStyled>

      <ModalBodyStyled>
        <SubtitleStyled>
          {I18n.t('attendance.howToExportPayPeriod')}
        </SubtitleStyled>
        {exportOptions.map(item => {
          const { value, title, description } = item
          const isActive = value === option
          return (
            <>
              <RowStyled
                key={value}
                onClick={() => updateExportOption(value)}
              >
                <CheckboxStyled checked={isActive}>
                  <img
                    src={checkIcon}
                    alt=''
                  />
                </CheckboxStyled>
                <RowBlockStyled>
                  <RowLabelStyled selected={isActive}>{title}</RowLabelStyled>
                  <RowTextStyled>{description}</RowTextStyled>
                </RowBlockStyled>
                {item.value === option && item.exportOptions && (
                  <ExportOptions
                    key={title}
                    currentOption={exportFormat}
                    setExportOption={(format: ExportFormat) => setExportFormat(format)}
                    exportOptions={item.exportOptions}
                  />
                )}
              </RowStyled>
              {isActive &&
                integrationType === POWERPAY &&
                value === 'powerpay' && (
                  <PowerPayInfoStyled>
                    <InputWithPrefix
                      label={I18n.t('attendance.powerpay.branch_id')}
                      prefix='PP'
                      placeholder={I18n.t('attendance.powerpay.branch_id')}
                      value={powerpayBranchId}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => setPowerpayBranchId(e.target.value)}
                      type='text'
                      maxLength={2}
                    />
                    <InputWithPrefix
                      label={I18n.t('attendance.powerpay.payroll_id')}
                      prefix='#'
                      placeholder={I18n.t('attendance.powerpay.payroll_id')}
                      value={powerpayPayrollNumber}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => setPowerpayPayrollNumber(e.target.value)}
                      type='text'
                      maxLength={4}
                    />
                  </PowerPayInfoStyled>
                )}
            </>
          )
        })}
      </ModalBodyStyled>

      <ModalFooterStyled>
        <ButtonStyled
          color='purple'
          onClick={onExport}
          disabled={
            option === 'powerpay' &&
            integrationType === POWERPAY &&
            (!powerpayBranchId || !powerpayPayrollNumber)
          }
        >
          <ExportIcontStyled />
          {I18n.t('attendance.export')}
        </ButtonStyled>
      </ModalFooterStyled>
    </ModalStyled>
  )
}

export default ExportPayPeriodModal
