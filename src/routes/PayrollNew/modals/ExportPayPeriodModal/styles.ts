import Modal from 'react-bootstrap/Modal'

import styled from 'styled-components'
import { theme } from 'styles/theme'

import { OutlineButton } from 'components/ui/OutlineButton'

import { ReactComponent as ExportIcon } from 'img/icons/exportIcon.svg'

export const ModalStyled = styled(Modal)`
  .modal-dialog {
    display: flex;
    align-items: center;
    justify-content: center;

    max-width: 680px;
  }
`

export const ModalHeaderStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;

  width: 100%;
  padding: ${theme.rem(25)};

  position: relative;

  background-color: #e5d8fa;
  border-radius: 0.8rem 0.8rem 0 0;

  color: #a984d2;
  font-family: ${theme.fonts.bold};
  font-size: ${theme.remFont(17)};

  svg {
    width: ${theme.rem(24)};
    height: ${theme.rem(24)};
    margin-right: ${theme.rem(10)};
  }

  button {
    display: flex;
    align-items: center;
    justify-content: center;

    height: ${theme.rem(30)};
    width: ${theme.rem(30)};
    padding: 0;

    position: absolute;
    right: ${theme.rem(15)};

    background-color: #f3e9ff;
    border: none;
    border-radius: 50%;

    img {
      height: ${theme.rem(13)};
      width: ${theme.rem(13)};
    }
  }
`

export const ModalBodyStyled = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;

  padding: ${theme.rem(60)} ${theme.rem(15)} ${theme.rem(60)} 14%;
  width: 100%;
  gap: 0.4rem;
`

export const SubtitleStyled = styled.p`
  margin-bottom: ${theme.rem(20)};

  font-size: ${theme.remFont(15)};
  font-family: ${theme.fonts.bold};
  color: ${theme.colors.midGrey600};
`

export const CheckboxStyled = styled.div<{ checked: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;

  height: ${theme.rem(34)};
  width: ${theme.rem(34)};
  margin-right: ${theme.rem(15)};
  margin-bottom: 1rem;

  background-color: ${({ checked }) => (checked ? '#f3e9ff' : '#ecf1f5')};
  border-radius: 6px;

  img {
    display: ${({ checked }) => (checked ? 'flex' : 'none')};

    height: ${theme.rem(21)};
    width: ${theme.rem(21)};
  }
`

export const RowBlockStyled = styled.div`
  display: flex;
  align-items: flex-start;
  flex-direction: column;

  margin-right: ${theme.rem(25)};
`

export const RowLabelStyled = styled.p<{ selected: boolean }>`
  font-size: ${theme.remFont(15)};
  font-family: ${theme.fonts.bold};
  color: ${({ selected }) => (selected ? '#a984d2' : theme.colors.midGrey600)};
  line-height: normal;

  span {
    margin-left: ${theme.rem(10)};

    color: #f9b63a;
    font-family: ${theme.fonts.boldItalic};
    font-size: ${theme.remFont(13)};
  }
`

export const RowStyled = styled.button`
  display: flex;
  align-items: center;

  position: relative;
  padding: 0;
  background-color: unset;
  border: none;

  &:hover {
    ${RowLabelStyled} {
      color: #a984d2;
    }
    ${CheckboxStyled} {
      background-color: #f3e9ff;
    }
  }
`

export const RowTextStyled = styled.p`
  font-size: ${theme.remFont(13)};
  font-family: ${theme.fonts.bold};
  color: ${theme.colors.midGrey500};
`

export const ModalFooterStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;

  margin: 0 ${theme.rem(20)};
  padding: ${theme.rem(24)};
  border-top: 1px solid #f3e9ff;
`

export const ExportIcontStyled = styled(ExportIcon)`
  height: ${theme.rem(24)};
  width: ${theme.rem(24)};
`

export const ButtonStyled = styled(OutlineButton)`
  border-color: transparent;
  padding-inline: 1rem;
  gap: 0.7rem;

  &:hover {
    color: #fff;
    background-color: #a984d2;

    svg path {
      fill: #fff;
    }
  }
`
