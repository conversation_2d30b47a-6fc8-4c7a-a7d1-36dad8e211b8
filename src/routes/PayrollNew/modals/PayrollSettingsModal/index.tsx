import React from 'react'
import OverlayTrigger from 'react-bootstrap/OverlayTrigger'
import { I18n } from 'react-redux-i18n'

import dayjs from 'dayjs'

import CustomSelect from 'components/ui/Select'

import { roundTime } from '../../../Payroll/payrollUtils'

import {
  EMPLOYEURD,
  NETHRIS,
  POWERPAY_EARNING_MODE
} from 'utils/constants'
import { minutesToHours } from 'utils/time'

import {
  PopoverHintStyled,
  HintPopoverTextStyled,
  PopoverMarkStyled,
  HintPopoverExampleBlockStyled,
  HintPopoverTimeBlockStyled,
  TimeTriangleIconStyled,
  ModalStyled,
  ModalHeaderStyled,
  CloseButtonStyled,
  ModalBodyStyled,
  CardStyled,
  CardTitleStyled,
  SettingsContainerRowStyled,
  SettingsBlockStyled,
  SettingsLabelStyled,
  SettingsInputStyled,
  ExportInputStyled,
  ExportInputLabelStyled,
  NumberInputStyled,
  RolesHeaderStyled,
  RolesTableStyled,
  RolesRowStyled,
  RolesRowNameStyled,
  PairedInputWrapStyled,
  PairedInputLabelStyled,
  PairedInputLabeExtraStyled,
  ArrowTopStyled,
  RoundingTopButtonStyled,
  ArrowDownStyled,
  RoundingDownButtonStyled,
  RoundingSwitchStyled,
  CustomSelectStyled
} from '../../styles/PayrollSettingsModal.styles'

import {
  AttendanceSettings,
  CutDistribution,
  CutFormat,
  EmployeeOrder,
  OvertimeCalculationMode,
  PayrollFrequency,
  SalaryType,
  TipsDistribution
} from 'types/attendance'
import { Option } from 'types/selectOption'

import closeIcon from 'img/icons/closeWhite.svg'
import triangleIcon from 'img/icons/triangleDarkIcon.svg'

// FIX TODO
// for each input with % value - add type='number' + '%' to value, or leave type='text' but add certain check for numbers only!
// remove any disabled and $isDisabled props from inputs if needed

type OptionType<T> = {
  label: string
  value: T
  disabled?: boolean
}

const RoundTimeHintPopover = React.forwardRef<
  HTMLDivElement,
  { value: number }
>(({ value, ...rest }, ref) => (
  <PopoverHintStyled
    {...rest}
    ref={ref}
    id='payrol-setting-hint-popover'
  >
    <HintPopoverTextStyled>
      {I18n.t('attendance.roundningTimeIs')}
    </HintPopoverTextStyled>
    <HintPopoverExampleBlockStyled>
      <HintPopoverTextStyled isItalic>
        {I18n.t('attendance.example')}:
      </HintPopoverTextStyled>
      <HintPopoverTextStyled>
        {I18n.t('attendance.roundingTimeAt')} <span>{value} min</span>
      </HintPopoverTextStyled>
      <HintPopoverTimeBlockStyled>
        <div>
          <HintPopoverTextStyled isItalic>
            {I18n.t('attendance.clockAt')}
            <span>8:03</span>
          </HintPopoverTextStyled>
        </div>
        <TimeTriangleIconStyled
          src={triangleIcon}
          alt=''
        />
        <div>
          <HintPopoverTextStyled isItalic>
            {I18n.t('attendance.rounded')}
            <span>{minutesToHours(roundTime(8 * 60 + 3, value))}</span>
          </HintPopoverTextStyled>
        </div>
      </HintPopoverTimeBlockStyled>
    </HintPopoverExampleBlockStyled>
  </PopoverHintStyled>
))

type PayrollSettingsModalProps = {
  showModal: boolean
  onClose: () => void
  jobs: Record<string, { name: string }>
  attendanceSettings: AttendanceSettings
  setAttendanceSettings: React.Dispatch<
    React.SetStateAction<AttendanceSettings>
  >
  maxHoursPerWeek: number
  integrationType: string
  powerpayEarningCodesTypePerPosition: Record<string, POWERPAY_EARNING_MODE>
  updatePowerpayEarningMode: (
    positionId: string,
    earningType: POWERPAY_EARNING_MODE
  ) => Promise<void>
}

function PayrollSettingsModal({
  showModal,
  onClose,
  jobs,
  attendanceSettings,
  setAttendanceSettings,
  maxHoursPerWeek,
  integrationType,
  powerpayEarningCodesTypePerPosition,
  updatePowerpayEarningMode
}: PayrollSettingsModalProps) {
  const {
    roundingTime = 5,
    positionSettings = {},
    startingWeek,
    tipsDeclaration,
    tipsDistribution,
    cutDistribution,
    tipsExport,
    cutExport,
    declarationExport,
    cutPercentage,
    cutRoundingValue,
    cutRoundingType,
    overtimeExport,
    payrollFrequency,
    overtimeCalculationMode = 'weekly',
    employeeOrder = 'surname'
  } = attendanceSettings

  const isNethrisOrEmployeurD =
    integrationType === NETHRIS || integrationType === EMPLOYEURD

  const earningModeOptions: OptionType<POWERPAY_EARNING_MODE>[] = [
    {
      value: POWERPAY_EARNING_MODE.HOURLY,
      label: I18n.t('attendance.powerpay.hourly')
    },
    {
      value: POWERPAY_EARNING_MODE.AMOUNT,
      label: I18n.t('attendance.powerpay.amount')
    }
  ]

  const weeks = []
  for (let i = 0; i < 3; i++) {
    const startOfWeek = dayjs().startOf('week').add(i, 'weeks')

    weeks.push({
      value: startOfWeek.format('YYYY-MM-DD'),
      label: `${startOfWeek.format('MMM')} ${startOfWeek.format(
        'D'
      )}-${startOfWeek.clone().endOf('week').format('D')}`
    })
  }

  const payrollFrequencyOptions: OptionType<PayrollFrequency>[] = [
    { value: 'biweekly', label: I18n.t('attendance.biweekly') },
    { value: 'weekly', label: I18n.t('attendance.weekly') }
  ]

  const overtimeOptions: OptionType<OvertimeCalculationMode>[] = [
    {
      value: 'not-calculated',
      label: I18n.t('attendance.overtime_not_calculated')
    },
    {
      value: 'weekly',
      label: `${I18n.t('attendance.overtime_weekly')} (+${maxHoursPerWeek}h)`
    },
    {
      value: 'biweekly',
      label: `${I18n.t('attendance.overtime_biweekly')} (+${maxHoursPerWeek * 2}h)`
    }
  ]

  const tipsDistributionOptions: OptionType<TipsDistribution>[] = [
    // {
    //   value: 'time-period',
    //   label: I18n.t('attendance.byTimePeriod')
    // },
    {
      value: 'daily',
      label: I18n.t('attendance.byTimePeriod')
    }
  ]

  const cutDistributionOptions: OptionType<CutDistribution>[] = [
    {
      value: 'daily',
      label: I18n.t('attendance.daily')
    },
    {
      value: 'weekly',
      label: I18n.t('attendance.weekly')
    },
    {
      value: 'biweekly',
      label: I18n.t('attendance.biweekly'),
      disabled: true
    }
  ]

  const salaryTypeOptions: OptionType<SalaryType>[] = [
    {
      value: 'wage',
      label: I18n.t('attendance.wageOnly')
    },
    {
      value: 'wage_tips',
      label: I18n.t('attendance.wageTips')
    },
    { value: 'wage_cut', label: I18n.t('attendance.wageCut') }
  ]

  const cutFormatOptions: OptionType<CutFormat>[] = [
    {
      value: 'cash',
      label: I18n.t('attendance.inCash')
    },
    {
      value: 'pay',
      label: I18n.t('attendance.deductedFromPay')
    }
  ]

  const sortingOptions: OptionType<EmployeeOrder>[] = [
    {
      value: 'surname',
      label: I18n.t('attendance.last_name')
    },
    {
      value: 'name',
      label: I18n.t('attendance.first_name')
    },
    {
      value: 'employee-number',
      label: I18n.t('attendance.employee_number')
    }
  ]

  // Action type: increase | decrease
  const updateRoundedTime = (action: 'increase' | 'decrease') => {
    if (action === 'increase') {
      setAttendanceSettings(state => ({
        ...state,
        roundingTime: state.roundingTime === 1 ? 5 : state.roundingTime + 5
      }))
    } else {
      setAttendanceSettings(state => ({
        ...state,
        roundingTime: state.roundingTime > 5 ? state.roundingTime - 5 : 1
      }))
    }
  }

  const ExportGPrefix = () => {
    return isNethrisOrEmployeurD ? (
      <ExportInputLabelStyled>G-</ExportInputLabelStyled>
    ) : null
  }
  return (
    <ModalStyled
      show={showModal}
      onHide={onClose}
    >
      <ModalHeaderStyled>
        {I18n.t('attendance.payrollSettings')}
        <CloseButtonStyled onClick={onClose}>
          <img
            src={closeIcon}
            alt=''
          />
        </CloseButtonStyled>
      </ModalHeaderStyled>

      <ModalBodyStyled>
        <CardStyled>
          <CardTitleStyled>{I18n.t('attendance.general')}</CardTitleStyled>

          <SettingsContainerRowStyled>
            <SettingsBlockStyled>
              <SettingsLabelStyled>
                {I18n.t('attendance.payrollFrequency')}
              </SettingsLabelStyled>
              <CustomSelect
                options={payrollFrequencyOptions}
                value={payrollFrequencyOptions.find(
                  option => option.value === payrollFrequency
                )}
                placeholder={I18n.t('schedule.selectWeek')}
                components={{
                  IndicatorSeparator: () => null
                }}
                onChange={option => {
                  const newValue = option as OptionType<PayrollFrequency>
                  setAttendanceSettings(state => ({
                    ...state,
                    payrollFrequency: newValue.value
                  }))
                }}
              />
            </SettingsBlockStyled>

            <SettingsBlockStyled>
              <SettingsLabelStyled>
                {I18n.t('attendance.nextStartingWeek')}
              </SettingsLabelStyled>
              <CustomSelect
                options={weeks}
                value={weeks.find(({ value }) => value === startingWeek)}
                placeholder={I18n.t('schedule.selectWeek')}
                onChange={e => {
                  const newValue = e as Option

                  setAttendanceSettings(state => ({
                    ...state,
                    startingWeek: newValue.value
                  }))
                }}
                components={{
                  IndicatorSeparator: () => null
                }}
              />
            </SettingsBlockStyled>

            <SettingsBlockStyled>
              <SettingsLabelStyled>
                {I18n.t('attendance.roundingTime')}
                <OverlayTrigger
                  trigger={['hover', 'focus']}
                  placement='right'
                  overlay={<RoundTimeHintPopover value={roundingTime} />}
                >
                  <PopoverMarkStyled>?</PopoverMarkStyled>
                </OverlayTrigger>
              </SettingsLabelStyled>
              <NumberInputStyled
                value={`${roundingTime} min`}
                disabledMinus={roundingTime === 1}
                disabledPlus={roundingTime === 15}
                onIncrease={() => updateRoundedTime('increase')}
                onDecrease={() => updateRoundedTime('decrease')}
                isInverse
              />
            </SettingsBlockStyled>
            {/* <SettingsBlockStyled>
              <SettingsLabelStyled>
                {I18n.t('attendance.tipsFormat')}
              </SettingsLabelStyled>
              <CustomSelect
                isDisabled={true}
                options={[
                  { value: 'normal', label: I18n.t('attendance.normal') },
                  { value: 'split', label: I18n.t('attendance.splitTip') }
                ]}
                value={{ value: 'normal', label: I18n.t('attendance.normal') }}
                placeholder={I18n.t('attendance.normal')}
                onChange={() => {}}
                components={{
                  IndicatorSeparator: () => null
                }}
              />
            </SettingsBlockStyled> */}
            <SettingsBlockStyled>
              <SettingsLabelStyled>
                {I18n.t('attendance.employee_order')}
              </SettingsLabelStyled>
              <CustomSelect
                options={sortingOptions}
                value={sortingOptions.find(
                  option => option.value === employeeOrder
                )}
                onChange={option => {
                  const newValue = option as OptionType<EmployeeOrder>
                  setAttendanceSettings(state => ({
                    ...state,
                    employeeOrder: newValue.value
                  }))
                }}
                components={{
                  IndicatorSeparator: () => null
                }}
              />
            </SettingsBlockStyled>
            <SettingsBlockStyled>
              <SettingsLabelStyled>
                {I18n.t('attendance.overtime_paid')}
              </SettingsLabelStyled>
              <CustomSelect
                options={overtimeOptions}
                value={overtimeOptions.find(
                  option => option.value === overtimeCalculationMode
                )}
                components={{
                  IndicatorSeparator: () => null
                }}
                onChange={option => {
                  const newValue = option as OptionType<OvertimeCalculationMode>
                  setAttendanceSettings(state => ({
                    ...state,
                    overtimeCalculationMode: newValue.value
                  }))
                }}
                isOptionDisabled={option => {
                  const optionValue =
                    option as OptionType<OvertimeCalculationMode>

                  return (
                    optionValue.value === 'biweekly' &&
                    payrollFrequency === 'weekly'
                  )
                }}
              />
            </SettingsBlockStyled>
          </SettingsContainerRowStyled>
        </CardStyled>

        <CardStyled>
          <CardTitleStyled>{I18n.t('attendance.tipsAndCut')}</CardTitleStyled>
          <SettingsContainerRowStyled>
            <SettingsBlockStyled>
              <SettingsLabelStyled>
                {I18n.t('attendance.tipsDeclaration')}
              </SettingsLabelStyled>
              <ExportInputStyled>
                <ExportInputLabelStyled $isOnRight>%</ExportInputLabelStyled>
                <SettingsInputStyled
                  value={tipsDeclaration}
                  $withLabel
                  type='number'
                  onChange={e => {
                    const value = e.target.value
                    setAttendanceSettings(state => ({
                      ...state,
                      tipsDeclaration: value
                    }))
                  }}
                />
              </ExportInputStyled>
            </SettingsBlockStyled>
            <SettingsBlockStyled>
              <SettingsLabelStyled>
                {I18n.t('attendance.tipsDistribution')}
              </SettingsLabelStyled>
              <CustomSelect
                isDisabled={true}
                options={tipsDistributionOptions}
                value={tipsDistributionOptions.find(
                  ({ value }) => value === tipsDistribution
                )}
                onChange={e => {
                  const newValue = e as OptionType<TipsDistribution>

                  setAttendanceSettings(state => ({
                    ...state,
                    tipsDistribution: newValue.value
                  }))
                }}
                components={{
                  IndicatorSeparator: () => null
                }}
              />
            </SettingsBlockStyled>

            <SettingsBlockStyled>
              <SettingsLabelStyled>
                {I18n.t('attendance.cutRounding')}
              </SettingsLabelStyled>

              <PairedInputWrapStyled>
                <ExportInputStyled>
                  <SettingsInputStyled
                    value={cutPercentage}
                    onChange={e => {
                      const value = e.target.value

                      if (!value || +value <= 100) {
                        setAttendanceSettings(state => ({
                          ...state,
                          cutPercentage: value
                        }))
                      }
                    }}
                    max='100'
                    type='number'
                  />
                  <PairedInputLabelStyled>%</PairedInputLabelStyled>
                </ExportInputStyled>

                <ExportInputStyled>
                  <SettingsInputStyled
                    value={cutRoundingValue}
                    onChange={e => {
                      const value = e.target.value

                      setAttendanceSettings(state => ({
                        ...state,
                        cutRoundingValue: value
                      }))
                    }}
                    type='number'
                    $isEmpty={false}
                  />
                  <PairedInputLabeExtraStyled $isEmpty={false}>
                    $
                  </PairedInputLabeExtraStyled>
                  <RoundingSwitchStyled $isDisabled={!cutRoundingValue}>
                    <RoundingTopButtonStyled
                      onClick={() => {
                        setAttendanceSettings(state => ({
                          ...state,
                          cutRoundingType: 'up'
                        }))
                      }}
                      $isActive={
                        !(cutRoundingValue && cutRoundingType === 'up')
                      }
                    >
                      <ArrowTopStyled />
                    </RoundingTopButtonStyled>
                    <RoundingDownButtonStyled
                      onClick={() => {
                        setAttendanceSettings(state => ({
                          ...state,
                          cutRoundingType: 'down'
                        }))
                      }}
                      $isActive={
                        !(cutRoundingValue && cutRoundingType === 'down')
                      }
                    >
                      <ArrowDownStyled />
                    </RoundingDownButtonStyled>
                  </RoundingSwitchStyled>
                </ExportInputStyled>
              </PairedInputWrapStyled>
            </SettingsBlockStyled>

            <SettingsBlockStyled>
              <SettingsLabelStyled>
                {I18n.t('attendance.cutDistribution')}
              </SettingsLabelStyled>
              <CustomSelect
                options={cutDistributionOptions}
                value={cutDistributionOptions.find(
                  ({ value }) => value === cutDistribution
                )}
                onChange={e => {
                  const newValue = e as OptionType<CutDistribution>

                  setAttendanceSettings(state => ({
                    ...state,
                    cutDistribution: newValue.value
                  }))
                }}
                components={{
                  IndicatorSeparator: () => null
                }}
              />
            </SettingsBlockStyled>
          </SettingsContainerRowStyled>
        </CardStyled>

        <CardStyled>
          <CardTitleStyled>{I18n.t('attendance.export')}</CardTitleStyled>
          <SettingsContainerRowStyled>
            <SettingsBlockStyled>
              <SettingsLabelStyled>
                {I18n.t('attendance.overtime_export')}
              </SettingsLabelStyled>
              <ExportInputStyled>
                <ExportGPrefix />
                <SettingsInputStyled
                  value={overtimeExport}
                  onChange={e => {
                    const value = e.target.value

                    if (!value || value.length <= 3) {
                      setAttendanceSettings(state => ({
                        ...state,
                        overtimeExport: value
                      }))
                    }
                  }}
                  readOnly={false}
                  type='number'
                  $withLabel
                />
              </ExportInputStyled>
            </SettingsBlockStyled>

            <SettingsBlockStyled>
              <SettingsLabelStyled>
                {I18n.t('attendance.tipsExport')}
              </SettingsLabelStyled>
              <ExportInputStyled>
                <ExportGPrefix />
                <SettingsInputStyled
                  value={tipsExport}
                  onChange={e => {
                    const value = e.target.value

                    if (!value || value.length <= 3) {
                      setAttendanceSettings(state => ({
                        ...state,
                        tipsExport: value
                      }))
                    }
                  }}
                  readOnly={false}
                  type='number'
                  $withLabel
                />
              </ExportInputStyled>
            </SettingsBlockStyled>

            <SettingsBlockStyled>
              <SettingsLabelStyled>
                {I18n.t('attendance.cutExport')}
              </SettingsLabelStyled>
              <ExportInputStyled>
                <ExportGPrefix />
                <SettingsInputStyled
                  value={cutExport}
                  onChange={e => {
                    const value = e.target.value

                    if (!value || value.length <= 3) {
                      setAttendanceSettings(state => ({
                        ...state,
                        cutExport: value
                      }))
                    }
                  }}
                  readOnly={false}
                  type='number'
                  $withLabel
                />
              </ExportInputStyled>
            </SettingsBlockStyled>

            <SettingsBlockStyled>
              <SettingsLabelStyled>
                {I18n.t('attendance.declarationExport')}
              </SettingsLabelStyled>
              <ExportInputStyled>
                <ExportGPrefix />
                <SettingsInputStyled
                  // readOnly={false}
                  value={declarationExport}
                  onChange={e => {
                    const value = e.target.value

                    if (!value || value.length <= 3) {
                      setAttendanceSettings(state => ({
                        ...state,
                        declarationExport: value
                      }))
                    }
                  }}
                  type='number'
                  $withLabel
                />
              </ExportInputStyled>
            </SettingsBlockStyled>
          </SettingsContainerRowStyled>
        </CardStyled>

        <CardStyled>
          <CardTitleStyled>{I18n.t('attendance.export')}</CardTitleStyled>
          <RolesHeaderStyled>
            <p>{I18n.t('attendance.roles')}</p>
            <p>{I18n.t('attendance.salaryExport')}</p>
            <p>{I18n.t('attendance.salaryType')}</p>
            <p>{I18n.t('attendance.value')} %</p>
            <p>{I18n.t('attendance.cutFormat')}</p>
            {integrationType === 'powerpay' && (
              <p>{I18n.t('attendance.powerpay_earning_type')}</p>
            )}
          </RolesHeaderStyled>

          <RolesTableStyled>
            {Object.keys(jobs).map(positionId => {
              const { name } = jobs?.[positionId]

              const {
                salaryType = 'wage',
                percentage = '',
                cutFormat = '',
                salaryExport = ''
              } = positionSettings[positionId] || {}

              return (
                <RolesRowStyled key={positionId}>
                  <RolesRowNameStyled>{name}</RolesRowNameStyled>
                  <SettingsBlockStyled>
                    <ExportInputStyled>
                      <ExportGPrefix />
                      <SettingsInputStyled
                        value={salaryExport}
                        onChange={e => {
                          const value = e.target.value

                          if (!value || value.length <= 3) {
                            setAttendanceSettings(state => ({
                              ...state,
                              positionSettings: {
                                ...state.positionSettings,
                                [positionId]: {
                                  ...state.positionSettings[positionId],
                                  salaryExport: value
                                }
                              }
                            }))
                          }
                        }}
                        readOnly={false}
                        type='number'
                        $withLabel={isNethrisOrEmployeurD}
                        $fullWidth={isNethrisOrEmployeurD}
                      />
                    </ExportInputStyled>
                  </SettingsBlockStyled>
                  <CustomSelect
                    options={salaryTypeOptions}
                    value={salaryTypeOptions.find(
                      ({ value }) => value === salaryType
                    )}
                    onChange={option => {
                      const newValue = option as OptionType<SalaryType>

                      const updates: {
                        salaryType: SalaryType
                        percentage: string
                        cutFormat: CutFormat
                      } = {
                        salaryType: newValue.value,
                        cutFormat,
                        percentage
                      }
                      if (newValue.value !== 'wage_cut') {
                        updates.percentage = ''
                      }
                      if (newValue.value !== 'wage_tips') {
                        updates.cutFormat = ''
                      }
                      if (newValue.value === 'wage_tips' && !cutFormat) {
                        updates.cutFormat = 'cash'
                      }
                      setAttendanceSettings(state => ({
                        ...state,
                        positionSettings: {
                          ...state.positionSettings,
                          [positionId]: {
                            ...state.positionSettings[positionId],
                            ...updates
                          }
                        }
                      }))
                    }}
                    components={{
                      IndicatorSeparator: () => null
                    }}
                  />
                  <SettingsInputStyled
                    disabled={salaryType !== 'wage_cut'}
                    value={percentage}
                    placeholder='-'
                    onChange={e => {
                      const value = e.target.value

                      setAttendanceSettings(state => ({
                        ...state,
                        positionSettings: {
                          ...state.positionSettings,
                          [positionId]: {
                            ...state.positionSettings[positionId],
                            percentage: value
                          }
                        }
                      }))
                    }}
                    type='number'
                    max='100'
                  />
                  <CustomSelectStyled
                    isDisabled={salaryType !== 'wage_tips'}
                    options={cutFormatOptions}
                    value={cutFormatOptions.find(
                      ({ value }) => value === cutFormat
                    )}
                    onChange={option => {
                      const newValue = option as OptionType<CutFormat>

                      setAttendanceSettings(state => ({
                        ...state,
                        positionSettings: {
                          ...state.positionSettings,
                          [positionId]: {
                            ...state.positionSettings[positionId],
                            cutFormat: newValue.value
                          }
                        }
                      }))
                    }}
                    placeholder='-'
                    components={{
                      IndicatorSeparator: () => null
                    }}
                  />{' '}
                  {integrationType === 'powerpay' && (
                    <CustomSelectStyled
                      options={earningModeOptions}
                      value={
                        earningModeOptions.find(
                          ({ value }) =>
                            value ===
                            powerpayEarningCodesTypePerPosition[positionId]
                        ) || {
                          value: POWERPAY_EARNING_MODE.HOURLY,
                          label: I18n.t('attendance.powerpay.hourly')
                        }
                      }
                      onChange={option => {
                        const newValue =
                          option as OptionType<POWERPAY_EARNING_MODE>
                        updatePowerpayEarningMode(positionId, newValue.value)
                      }}
                    />
                  )}
                </RolesRowStyled>
              )
            })}
          </RolesTableStyled>
        </CardStyled>
      </ModalBodyStyled>
    </ModalStyled>
  )
}

export default PayrollSettingsModal
