import Modal from 'react-bootstrap/Modal'
import Popover from 'react-bootstrap/Popover'

import styled from 'styled-components'
import { theme } from 'styles/theme'

import NumberInput from 'components/ui/NumberInput'
import CustomSelect from 'components/ui/Select'

export const PopoverHintStyled = styled(Popover)`
  max-width: 17rem;
  width: 17rem;
  padding: 1rem !important;

  border: solid 1px #dbdddd;
  border-radius: 16px;
  margin-left: 1rem;

  z-index: 1051;
`

export const HintPopoverTextStyled = styled.p<{ isItalic?: boolean }>`
  text-align: center;
  color: ${theme.colors.midGrey600};
  font-size: ${theme.remFont(15)};
  font-family: ${({ isItalic }) =>
    isItalic ? theme.fonts.boldItalic : theme.fonts.bold};
  span {
    color: ${theme.colors.darkGrey};
    font-family: ${theme.fonts.heavy};
  }
`

export const PopoverMarkStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;

  width: ${theme.rem(22)};
  height: ${theme.rem(22)};
  margin-left: ${theme.rem(12)};

  background-color: #fff;
  border-radius: 50%;

  font-size: ${theme.remFont(14)};
  color: ${theme.colors.midGrey600};
  font-style: normal;
  cursor: pointer;
  &:hover {
    box-shadow: 0px 0px 10px -3px rgba(0, 0, 0, 0.4);
  }
`

export const HintPopoverExampleBlockStyled = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  margin: ${theme.rem(15)} ${theme.rem(15)} 0;
  padding: ${theme.rem(10)};

  background-color: #ecf1f5;
  border-radius: 1rem;
`

export const HintPopoverTimeBlockStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;

  div {
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;

    flex: 1;
    padding: ${theme.rem(3)} ${theme.rem(6)};
    min-width: ${theme.rem(85)};

    span {
      display: block;
      font-style: normal;
    }
  }
`

export const TimeTriangleIconStyled = styled.img`
  display: flex;
  align-self: flex-end;

  width: ${theme.rem(12)};
  height: ${theme.rem(12)};
  margin-bottom: ${theme.rem(8)};
  @media (max-width: 1400px) {
    width: ${theme.rem(11)};
    height: ${theme.rem(11)};
  }
`

export const ModalStyled = styled(Modal)`
  .modal-content {
    width: 55vw;
    max-width: 1200px;
    min-width: 600px;
  }
`

export const ModalHeaderStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;

  width: 100%;
  padding: 0.8rem 1rem;

  position: relative;

  background-color: #69748f;
  border-radius: 0.8rem 0.8rem 0 0;

  color: #fff;
  font-family: ${theme.fonts.bold};
  font-size: ${theme.remFont(19)};
`

export const CloseButtonStyled = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;

  height: ${theme.rem(30)};
  width: ${theme.rem(30)};
  padding: 0;

  position: absolute;
  right: ${theme.rem(15)};

  background-color: ${theme.colors.midGrey600};
  border: none;
  border-radius: 50%;
  img {
    height: ${theme.rem(13)};
    width: ${theme.rem(13)};
  }
  &:hover,
  &:focus {
    box-shadow: 0px 0px 10px -3px rgba(0, 0, 0, 0.4);
  }
`

export const ModalBodyStyled = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;

  width: 100%;
  padding: 1.2rem;
  gap: 1rem;
  min-height: ${theme.rem(400)};
  max-height: 80vh;
  overflow: auto;
  &::-webkit-scrollbar {
    display: none;
  }
`

export const CardStyled = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  width: 100%;
  padding: 0 1.2rem 1.2rem;

  background-color: #ecf1f5;
  border-radius: 0.8rem;
`

export const CardTitleStyled = styled.div`
  min-width: 7rem;
  padding: 0.1rem 0.4rem;
  margin-bottom: 0.4rem;

  background-color: #d2dfe8;
  border-radius: 0 0 0.8rem 0.8rem;

  font-family: ${theme.fonts.bold};
  font-size: ${theme.remFont(16)};
  color: ${theme.colors.midGrey600};
`

export const SettingsContainerRowStyled = styled.div`
  display: flex;
  justify-content: center;
  align-items: flex-start;

  gap: 0.75rem;
  width: 100%;
`

export const SettingsBlockStyled = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;

  flex: 0.25;
`

export const SettingsLabelStyled = styled.div`
  display: flex;
  align-items: center;

  margin-bottom: 0.1rem;
  margin-left: 0.5rem;

  color: ${theme.colors.midGrey600};
  font-size: ${theme.remFont(14)};
  font-family: ${theme.fonts.boldItalic};
`

export const SettingsInputStyled = styled.input<{
  $withLabel?: boolean
  $fullWidth?: boolean
  $isInvalid?: boolean
  disabled?: boolean
  $isEmpty?: boolean
}>`
  width: 100%;
  height: ${theme.rem(45)};
  padding: 0
    ${({ $withLabel, $fullWidth }) =>
      $fullWidth ? '0.3rem 0 1.6rem' : $withLabel ? '2.2rem' : '0.4rem'};

  border: ${({ $isInvalid }) => `1px solid ${$isInvalid ? 'red' : '#c6d4dd'}`};
  border-radius: ${theme.rem(12)};
  /* background-color: #e9eff3; */
  background-color: #fff;
  opacity: ${({ disabled, $isEmpty }) => (disabled || $isEmpty ? 0.6 : null)};
  pointer-events: ${({ disabled }) => (disabled ? 'none' : null)};
  transition: 0.3s all;

  text-align: center;
  font-family: ${theme.fonts.bold};
  font-size: ${theme.remFont(16)};
  &:hover,
  &:focus {
    box-shadow: 0px 0px 10px -3px rgba(0, 0, 0, 0.4);
  }
  ::placeholder {
    font-family: ${theme.fonts.bold};
  }
`

export const ExportInputStyled = styled.div`
  display: flex;
  align-items: center;
  position: relative;
`

export const ExportInputLabelStyled = styled.div<{ $isOnRight?: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;

  width: 2.2rem;

  position: absolute;
  left: ${({ $isOnRight }) => ($isOnRight ? null : 0)};
  right: ${({ $isOnRight }) => ($isOnRight ? 0 : null)};
  pointer-events: none;

  font-family: ${theme.fonts.bold};
  font-size: ${theme.remFont(15)};
  color: ${theme.colors.midGrey600};
`

export const NumberInputStyled = styled(NumberInput)`
  width: 100%;
  padding: ${theme.rem(6)};
  height: calc(${theme.rem(45)} + 1.5px);

  border: 1px solid #c6d4dd;
  border-radius: ${theme.rem(12)};
  background-color: #fff;
  /* background-color: #e9eff3; */
  box-shadow: unset;
  input {
    width: 100%;
    line-height: unset;
    font-size: ${theme.remFont(15)};
    font-family: ${theme.fonts.bold};
    opacity: 1;
  }

  div {
    border: none;
    span {
      width: ${theme.rem(32)} !important;
      height: ${theme.rem(32)} !important;
      padding: ${theme.rem(3)};
      margin-left: ${theme.rem(3)};

      background-color: #dbe3eb;
      border-radius: 6px;
      border: none;

      font-family: ${theme.fonts.bold};
    }
  }
`

export const RolesHeaderStyled = styled.div`
  display: grid;
  align-items: center;

  width: 100%;
  grid-template-columns: 0.275fr 0.15fr 0.3fr 0.15fr 0.3fr;
  grid-gap: 0.75rem;
  padding: 0 1.5rem 0.1rem 1.5rem;

  p {
    color: ${theme.colors.midGrey600};
    font-family: ${theme.fonts.boldItalic};
    font-size: ${theme.remFont(15)};
    text-align: left;

    &:not(:first-child) {
      padding-left: 1rem;
    }
  }
  > *:first-child {
    text-align: left;
  }
`

export const RolesTableStyled = styled.div`
  display: flex;
  align-items: flex-start;
  flex-direction: column;

  width: 100%;
  gap: 1rem;
`

export const RolesRowStyled = styled(RolesHeaderStyled)`
  padding: 0.4rem 1rem 0.4rem 1.5rem;
  background: #d2dfe8;
  border-radius: ${theme.rem(16)};
`

export const RolesRowNameStyled = styled.div`
  color: ${theme.colors.darkGrey};
  font-family: ${theme.fonts.heavy};
  font-size: ${theme.remFont(17)};
`

export const PairedInputWrapStyled = styled.div`
  display: flex;

  height: 2.25rem;
  border: 1px solid #c6d4dd;
  border-radius: 0.6rem;
  background-color: #fff;
  ${ExportInputStyled} {
    &:first-child ${SettingsInputStyled} {
      border-right: 1px solid #c6d4dd;
      border-radius: 0.6rem 0 0 0.6rem;
    }
  }

  ${SettingsInputStyled} {
    height: 100%;
    padding: 0 2.2rem 0 1rem;

    background-color: transparent;
    border: none;
    border-radius: 0 0.6rem 0.6rem 0;
  }
`

export const PairedInputLabelStyled = styled.div`
  position: absolute;
  right: 0.4rem;
  pointer-events: none;

  font-family: ${theme.fonts.bold};
  font-size: ${theme.remFont(15)};
  color: ${theme.colors.midGrey600};
`

export const PairedInputLabeExtraStyled = styled(PairedInputLabelStyled)<{
  $isEmpty: boolean
}>`
  right: 1.8rem;
  opacity: ${({ $isEmpty }) => ($isEmpty ? 0.6 : null)};
`

export const ArrowTopStyled = styled.div`
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 0 0.24rem 0.4rem 0.24rem;
  border-color: transparent transparent #baced8 transparent;
`

export const RoundingTopButtonStyled = styled.div<{ $isActive: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2px;
  width: 0.8rem;
  height: 0.8rem;

  border: none;
  border-radius: 50%;
  background-color: ${({ $isActive }) =>
    $isActive ? theme.colors.green : 'transparent'};

  ${ArrowTopStyled} {
    border-bottom-color: ${({ $isActive }) => ($isActive ? '#b5f6e4' : null)};
  }
`

export const ArrowDownStyled = styled.div`
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 0.4rem 0.24rem 0 0.24rem;
  border-color: #baced8 transparent transparent transparent;
`

export const RoundingDownButtonStyled = styled.div<{ $isActive: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;

  padding: 2px;
  width: 0.8rem;
  height: 0.8rem;

  border: none;
  border-radius: 50%;
  background-color: ${({ $isActive }) =>
    $isActive ? theme.colors.red : 'transparent'};
  ${ArrowDownStyled} {
    border-top-color: ${({ $isActive }) => ($isActive ? '#ffe0e8' : null)};
  }
`

export const RoundingSwitchStyled = styled.button<{ $isDisabled: boolean }>`
  display: flex;
  flex-direction: column;

  padding: 2px;

  position: absolute;
  right: 0.4rem;

  border: none;
  border-radius: 1rem;
  background-color: #ecf1f5;

  pointer-events: ${({ $isDisabled }) => ($isDisabled ? 'none' : null)};
`

export const CustomSelectStyled = styled(CustomSelect)`
  .Select__placeholder {
    text-align: center;
  }
`
