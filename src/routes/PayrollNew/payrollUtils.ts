import dayjs from 'dayjs'
import { isNaN, reduce } from 'lodash'

import { getShiftLength, isTimeInsideShift } from 'utils/schedule'
import { getOverlapDuration } from 'utils/time'

import { AttendanceBreak, AttendanceBreaks } from 'types/attendance'
import { IShiftEnd, Shift } from 'types/schedule'

export const roundTime = (
  minutes: any,
  precision: number
  //  shiftStart
) => {
  if (typeof minutes !== 'number' || typeof precision !== 'number') {
    return minutes
  }
  return Math.round(minutes / precision) * precision
  // if (precision === 5) {
  //   return Math.ceil(minutes / precision) * precision
  // } else {
  //   if (!shiftStart) {
  //     return Math.ceil(minutes / precision) * precision
  //   }
  //   const module = minutes % precision

  //   return module <= 5
  //     ? minutes - module
  //     : Math.ceil(minutes / precision) * precision
  // }
}

export const getTimeSince = (
  start: number,
  timezone: string,
  onError: (error: any, start: number, timezone: string) => void
) => {
  // we could use some default timezone here
  // but i'm not sure if it's a good idea
  if (!timezone) {
    return 0
  }
  try {
    const now = dayjs().tz(timezone).hour() * 60 + dayjs().tz(timezone).minute()

    let result = now - start

    if (result < 0) {
      result += 1440
    }

    return result
  } catch (error) {
    onError(error, start, timezone)
    return 0
  }
}

export const addZeros = (num: number) => {
  let numString = num.toString()
  while (numString.length < 3) {
    numString = '0' + numString
  }
  return numString
}

type GetCutAmountProps = {
  weeklyData: any
  dailyCuts: any
  startOfWeek: any
  date: any
  positionId: any
  shiftLengthHours: any
  cutGroups: any
  positionSettings: any
  cutRoundingValue: any
  cutRoundingType: any
  period: any
}
export const getCutAmount = (
  cutDistribution: string,
  {
    weeklyData,
    dailyCuts,
    startOfWeek,
    date,
    positionId,
    shiftLengthHours,
    cutGroups,
    positionSettings,
    cutRoundingValue,
    cutRoundingType,
    period
  }: GetCutAmountProps
) => {
  let cutHours = 0
  let sales = 0

  const positionToSumUp = cutGroups.find((group: string[]) =>
    group.includes(positionId)
  ) || [positionId]

  const percentageToUse = positionToSumUp.reduce(
    (acc: number, positionId: string) => {
      const { percentage = 0 } = positionSettings[positionId] || {}
      return acc + +percentage
    },
    0
  )

  if (cutDistribution === 'daily') {
    cutHours = dailyCuts[date]?.[period]?.cutHours?.[positionId] || 0
    sales = dailyCuts[date]?.[period]?.sales || 0
  } else if (cutDistribution === 'weekly') {
    cutHours = weeklyData[startOfWeek]?.cutHours?.[positionId] || 0
    sales = weeklyData[startOfWeek]?.sales || 0
  }

  let cutToReceive = 0

  if (cutHours && sales && percentageToUse && shiftLengthHours) {
    cutToReceive =
      Math.round(
        sales * (percentageToUse / 100) * (shiftLengthHours / cutHours) * 100
      ) / 100
  }

  if (cutRoundingValue) {
    const cutRoundingValueNumber = +cutRoundingValue

    if (!isNaN(cutRoundingValue) && cutRoundingValue !== 0) {
      cutToReceive =
        cutRoundingType === 'up'
          ? Math.ceil(cutToReceive / cutRoundingValueNumber) *
            cutRoundingValueNumber
          : Math.floor(cutToReceive / cutRoundingValueNumber) *
            cutRoundingValueNumber
    }
  }

  return cutToReceive
}

type matchProps = {
  dayShifts: Shift[]
  shiftStartRounded: number
  shift: {
    start: number
    end: IShiftEnd
  }
  defaultDuration: number
}

export const matchClockInWithScheduledShift = ({
  dayShifts,
  shiftStartRounded,
  defaultDuration,
  shift
}: matchProps) => {
  const shiftWithStartInside = dayShifts.find(shift2 => {
    const isStartInside = isTimeInsideShift(
      shiftStartRounded,
      shift2.start,
      shift2.end,
      defaultDuration
    )

    return isStartInside
  })
  if (shiftWithStartInside) {
    return shiftWithStartInside
  } else {
    // if no shift with start inside
    // find a shift from dayShifts that has highest overlap duration with current shift
    // using getOverlapDuration function to get the duration
    const shiftWithHighestOverlap = dayShifts.reduce(
      (
        acc: { shift: Shift | null; overlapDuration: number },
        shift2: Shift
      ) => {
        const overlapDuration = getOverlapDuration(
          shift,
          shift2,
          defaultDuration
        )

        if (overlapDuration > acc.overlapDuration) {
          return {
            shift: shift2,
            overlapDuration
          }
        }

        return acc
      },
      {
        shift: null,
        overlapDuration: 0
      }
    )
    if (shiftWithHighestOverlap.overlapDuration > 0) {
      return shiftWithHighestOverlap.shift
    }
  }

  return null
}

export const getSingleBreakLength = (
  attendanceBreak: AttendanceBreak,
  roundingTime: number
) =>
  attendanceBreak.lengthRounded ??
  roundTime(
    getShiftLength(attendanceBreak.start, attendanceBreak.end, 0),
    roundingTime
  )

export const getBreaksLength = (
  breaks: AttendanceBreaks | undefined,
  roundingTime: number
) => reduce(breaks, (acc, b) => acc + getSingleBreakLength(b, roundingTime), 0)
