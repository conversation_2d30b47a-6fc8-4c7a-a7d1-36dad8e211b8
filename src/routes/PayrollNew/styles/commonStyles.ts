import styled from 'styled-components'
import { theme } from 'styles/theme'

export const Container = styled.div`
  display: flex;
  align-items: center;
  flex-direction: column;
  gap: 1.5rem;
  flex: 1;
  padding: 1rem 1rem 0;
  background-color: #f5faff;
`

export const LoadingMessage = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${theme.colorsNew.darkGrey500};
  font-family: ${theme.fonts.normal};
  font-size: 1rem;
  padding: 2rem;
`

export const MainContent = styled.div`
  display: flex;
  align-items: center;
  flex-direction: column;
  flex: 1;
  width: 100%;
  background-color: #edf2f7;
  border-radius: 1.2rem 1.2rem 0 0;
  min-height: 0;
`

export const Button = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: 1px solid currentColor;
  border-radius: 0.8rem;
  background-color: transparent;
  font-family: ${theme.fonts.normal};
  font-size: 0.9rem;
  transition: all 0.2s ease-in-out;
  cursor: pointer;

  :hover,
  :focus {
    opacity: 0.8;
  }

  :disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`

export const PrimaryButton = styled(Button)`
  color: #4bccad;

  :hover,
  :focus {
    color: #fff;
    background-color: #4bccad;
  }
`

export const SecondaryButton = styled(Button)`
  color: #a0a8ba;

  :hover,
  :focus {
    color: ${theme.colorsNew.darkGrey500};
  }
`
