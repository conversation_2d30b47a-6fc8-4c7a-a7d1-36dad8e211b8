import styled from 'styled-components'
import { theme } from 'styles/theme'

export const ContainerStyled = styled.div`
  font-size: 18px;
  font-family: ${theme.fonts.bold};
  @media (max-width: 1920px) {
    font-size: 16px;
  }
  @media (max-width: 1560px) {
    font-size: 14px;
  }
  @media (max-width: 1440px) {
    font-size: 12px;
  }
  @media (max-width: 1366px) {
    font-size: 10px;
  }
`

export const DigitStyled = styled.span<{ $isSeparator: boolean }>`
  display: inline-flex;
  width: ${({ $isSeparator }) => ($isSeparator ? 'auto' : '1ch')};
  position: relative;
  overflow: hidden;
`

export const ValueStyled = styled.span`
  color: transparent;
  position: relative;
`

export const SeparatorValueStyled = styled.span`
  color: inherit;
  position: relative;
`

interface ScaleProps {
  $transform: string
  $isAnimating: boolean
  $rollDuration: string
}

export const ScaleStyled = styled.span<ScaleProps>`
  display: inline-flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  position: absolute;
  left: 0;
  transform: ${props => props.$transform};
  transition: ${props =>
    props.$isAnimating ? `transform ${props.$rollDuration} ease-out` : 'none'};
  user-select: none;
`

export const DigitSpanStyled = styled.span<{ $isActive: boolean }>`
  color: #455468;
  opacity: ${({ $isActive }) => ($isActive ? 1 : 0.5)};
`
