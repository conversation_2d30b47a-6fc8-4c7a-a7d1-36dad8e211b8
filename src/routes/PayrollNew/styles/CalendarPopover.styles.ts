import Popover from 'react-bootstrap/Popover'
import { DayPicker } from 'react-day-picker'

import styled from 'styled-components'
import { theme } from 'styles/theme'

import CustomSelect from 'components/ui/Select'

import { ReactComponent as CheckIcon } from 'img/icons/checkGreenFat.svg'
import { ReactComponent as CloseIcon } from 'img/icons/closeIcon.svg'

export const PopoverStyled = styled(Popover)`
  min-width: 30rem;
  max-width: 32rem;

  border: 0;
  box-shadow:
    0px 10px 15px -3px rgba(18, 18, 23, 0.08),
    0px 4px 6px -1px rgba(18, 18, 23, 0.08);
  border-radius: 0.8rem;
  margin-left: 5.5rem !important;

  .arrow {
    display: none;
  }
`

export const ContainerStyled = styled.div`
  display: flex;
  flex-direction: column;

  gap: 1rem;
  padding: 1rem;
  position: relative;
`

export const CloseButtonStyled = styled.button`
  display: flex;
  padding: 0.2rem;

  position: absolute;
  right: 0.5rem;
  top: 0.5rem;

  border: none;
  opacity: 0.5;
  background: none;
  :hover,
  :focus {
    opacity: 1;
  }
`

export const CloseIconStyled = styled(CloseIcon)`
  width: 0.8rem;
  height: 0.8rem;

  fill: ${theme.colors.darkGrey};
`

export const MainStyled = styled.div`
  display: flex;
  gap: 1rem;
`

export const CalendarBlockStyled = styled.div`
  display: flex;
  flex-direction: column;

  gap: 0.5rem;
`

export const SelectBlockStyled = styled.div`
  display: flex;

  gap: 0.5rem;
  width: 100%;
`

export const CustomSelectStyled = styled(CustomSelect)``

export const DayPickerStyled = styled(DayPicker)`
  height: calc(2.2rem * 7 + 2px + 0.6rem);
  padding: 0.4rem 0.4rem 0.2rem 0.4rem;

  border: 1px solid #d7dfe9;
  border-radius: 0.8rem;

  --rdp-day_button-height: 2.2rem;
  --rdp-day_button-width: 2.2rem;
  --rdp-day-width: 2.2rem;
  --rdp-day-height: 2.2rem;
  --rdp-outside-opacity: 0.3;

  .rdp-month_caption {
    display: none;
  }
  .rdp-weekday {
    height: 2.2rem;
    color: #455468;
    font-size: 0.825rem;
    font-family: ${theme.fonts.bold};
    opacity: 1;
  }
  .rdp-day_button {
    color: #455468;
    font-size: 0.825rem;
    font-family: ${theme.fonts.normal};
  }

  .rdp-day_selected-week {
    background-color: rgba(50, 173, 230, 0.2);
    opacity: 1;
    &.rdp-selected .rdp-day_button {
      border: 0;
    }
  }

  .rdp-day_today .rdp-day_button {
    background-color: #3bbcff;
    color: white;
    font-weight: bold;
    border-radius: 50%;
  }

  .rdp-today {
    .rdp-day_button {
      color: #fff;
      background-color: #32ade6;
    }
  }
  .rdp-week {
    .rdp-day:first-of-type {
      border-top-left-radius: 50%;
      border-bottom-left-radius: 50%;
    }
    .rdp-day:last-of-type {
      border-top-right-radius: 50%;
      border-bottom-right-radius: 50%;
    }
  }
  .rdp-week:hover {
    background-color: #efeff2;
  }
`

export const ListBlockStyled = styled.div`
  display: flex;
  flex-direction: column;

  gap: 0.5rem;
  flex: 1;
  position: relative;
`

export const ListLabelStyled = styled.div`
  display: flex;
  align-items: center;

  height: 2.25rem;
  padding-left: 0.5rem;

  color: #455468;
  font-size: 0.9rem;
  font-family: ${theme.fonts.bold};
  line-height: normal;
`

export const ListStyled = styled.div`
  display: flex;
  flex-direction: column;

  gap: 0.4rem;
  padding: 1.2rem 0;
  max-height: calc(2.2rem * 7 + 0.6rem);

  overflow-y: auto;
`

export const ListWrapStyled = styled.div`
  position: relative;

  :before {
    content: '';
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 2.4rem;
    background: linear-gradient(
      to top,
      rgba(255, 255, 255, 0),
      rgba(255, 255, 255, 1)
    );

    pointer-events: none;
  }
  :after {
    content: '';
    display: block;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2.4rem;
    background: linear-gradient(
      to bottom,
      rgba(255, 255, 255, 0),
      rgba(255, 255, 255, 1)
    );

    pointer-events: none;
  }
`

export const CircleStyled = styled.span`
  display: flex;
  align-items: center;
  justify-content: center;

  width: 1.2rem;
  height: 1.2rem;

  border: 1px solid #cccccc;
  border-radius: 50%;
  background-color: rgba(204, 204, 204, 0.2);
`

export const CheckIconStyled = styled(CheckIcon)`
  width: 0.75rem;
  height: 0.75rem;
  fill: #32ade6;
`

export const ListItemStyled = styled.button<{
  $isCurrentPeriod: boolean
  $isSelected: boolean
  $isLowerCase?: boolean
}>`
  display: flex;
  align-items: center;
  justify-content: space-between;

  padding: 0.35rem 0.4rem 0.35rem 1rem;

  border: 1.5px solid
    ${({ $isCurrentPeriod, $isSelected }) =>
      $isCurrentPeriod || $isSelected ? '#3BBCFF' : 'transparent'};
  border-radius: 1.2rem;
  background: ${({ $isSelected }) =>
    $isSelected
      ? 'linear-gradient(180deg, #3BBCFF, #2D87FF) !important'
      : 'transparent'};

  color: ${({ $isSelected }) => ($isSelected ? '#fff' : '#455468')};
  font-size: 0.9rem;
  font-family: ${theme.fonts.normal};
  line-height: normal;
  text-transform: ${({ $isLowerCase }) => ($isLowerCase ? 'lowercase' : null)};

  ${CircleStyled} {
    border-color: ${({ $isSelected }) =>
      $isSelected ? '#fff !important' : null};
    background-color: ${({ $isSelected }) =>
      $isSelected ? '#fff !important' : null};
  }

  :hover,
  :focus {
    background-color: #efeff2;

    ${CircleStyled} {
      border-color: #afbaca;
      background-color: rgba(69, 84, 104, 0.1);
    }
  }
`

export const FooterStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
`

export const ResetButtonStyled = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;

  gap: 0.2rem;
  padding: 0.25rem 1rem;

  border: 1px solid #afbaca;
  border-radius: 0.8rem;
  background-color: #fff;

  color: #848da3;
  font-size: 0.85rem;
  font-family: ${theme.fonts.normal};
  line-height: normal;

  :hover,
  :focus {
    color: #fff;
    border-color: #32ade6;
    background-color: #32ade6;
  }
`

export const OpenButtonStyled = styled(ResetButtonStyled)<{
  disabled?: boolean
}>`
  color: ${({ disabled }) => (disabled ? '#afbaca' : '#32ade6')};
  border-color: ${({ disabled }) => (disabled ? '#afbaca' : '#32ade6')};
  cursor: ${({ disabled }) => (disabled ? 'not-allowed' : 'pointer')};
  opacity: ${({ disabled }) => (disabled ? 0.5 : 1)};

  :hover,
  :focus {
    color: ${({ disabled }) => (disabled ? '#afbaca' : '#fff')};
    border-color: ${({ disabled }) => (disabled ? '#afbaca' : '#32ade6')};
    background-color: ${({ disabled }) => (disabled ? '#fff' : '#32ade6')};
  }
`
