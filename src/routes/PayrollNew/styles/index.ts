import styled from 'styled-components'
import { theme } from 'styles/theme'

export const ContainerStyled = styled.div`
  display: flex;
  align-items: center;
  flex-direction: column;
  gap: 1.5rem;
  flex: 1;
  padding: 1rem 1rem 0;
  background-color: #f5faff;
`

export const LoadingMessage = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${theme.colorsNew.darkGrey500};
  font-family: ${theme.fonts.normal};
  font-size: 1rem;
  padding: 2rem;
`

export const MainStyled = styled.div`
  display: flex;
  align-items: center;
  flex-direction: column;
  flex: 1;
  width: 100%;
  background-color: #edf2f7;
  border-radius: 1.2rem 1.2rem 0 0;
  min-height: 0;
`
