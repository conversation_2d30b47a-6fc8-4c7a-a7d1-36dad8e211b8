export interface PayrollTab {
  id: 'hours' | 'tips' | 'cuts'
  label: string
  icon: React.ComponentType
  disabled?: boolean
}

export interface ExportOption {
  type: 'excel' | 'payroll' | 'by-shift' | 'pay-evolution'
  format?: 'simplified' | 'detailed' | 'xls' | 'txt'
}

export interface PayrollState {
  showSettingsModal: boolean
  showExportModal: boolean
  activeTab: PayrollTab['id']
}

export interface PayrollIntegration {
  type: 'NETHRIS' | 'EMPLOYEURD' | ''
  coNumber: string
}

export interface PayrollExportParams {
  option: ExportOption['type']
  format?: ExportOption['format']
  coNumber?: string
  integrationType?: string
}
