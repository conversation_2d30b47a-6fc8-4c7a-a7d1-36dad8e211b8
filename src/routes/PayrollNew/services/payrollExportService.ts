import { I18n } from 'react-redux-i18n'

import toastr from 'toastr'
import zipcelx from 'zipcelx'

export type ExportFormat = 'simplified' | 'detailed' | 'xls' | 'txt'
export type ExportOption = 'excel' | 'payroll' | 'by-shift' | 'pay-evolution'

interface ExportData {
  value: string | number
  type: 'string' | 'number'
}

interface PayrollExportParams {
  option: ExportOption
  format?: ExportFormat
  coNumber?: string
  integrationType?: string
}

export class PayrollExportService {
  private static generateExcelData(type: string): ExportData[][] {
    const baseData: Record<string, ExportData[][]> = {
      excel: [
        [
          { value: 'Employee', type: 'string' },
          { value: 'Hours', type: 'string' },
          { value: 'Overtime', type: 'string' }
        ],
        [
          { value: 'John Doe', type: 'string' },
          { value: 40, type: 'number' },
          { value: 5, type: 'number' }
        ],
        [
          { value: '<PERSON>', type: 'string' },
          { value: 38, type: 'number' },
          { value: 2, type: 'number' }
        ],
        [
          { value: '<PERSON> Johnson', type: 'string' },
          { value: 42, type: 'number' },
          { value: 7, type: 'number' }
        ]
      ],
      'by-shift': [
        [
          { value: 'Date', type: 'string' },
          { value: 'Employee', type: 'string' },
          { value: 'Start Time', type: 'string' },
          { value: 'End Time', type: 'string' },
          { value: 'Hours', type: 'string' }
        ],
        [
          { value: '2024-01-15', type: 'string' },
          { value: 'John Doe', type: 'string' },
          { value: '09:00', type: 'string' },
          { value: '17:00', type: 'string' },
          { value: 8, type: 'number' }
        ],
        [
          { value: '2024-01-15', type: 'string' },
          { value: 'Jane Smith', type: 'string' },
          { value: '10:00', type: 'string' },
          { value: '18:00', type: 'string' },
          { value: 8, type: 'number' }
        ],
        [
          { value: '2024-01-16', type: 'string' },
          { value: 'John Doe', type: 'string' },
          { value: '09:00', type: 'string' },
          { value: '17:00', type: 'string' },
          { value: 8, type: 'number' }
        ]
      ],
      'pay-evolution': [
        [
          { value: 'Employee', type: 'string' },
          { value: 'Previous Period', type: 'string' },
          { value: 'Current Period', type: 'string' },
          { value: 'Difference', type: 'string' }
        ],
        [
          { value: 'John Doe', type: 'string' },
          { value: 320.0, type: 'number' },
          { value: 360.0, type: 'number' },
          { value: 40.0, type: 'number' }
        ],
        [
          { value: 'Jane Smith', type: 'string' },
          { value: 304.0, type: 'number' },
          { value: 320.0, type: 'number' },
          { value: 16.0, type: 'number' }
        ],
        [
          { value: 'Bob Johnson', type: 'string' },
          { value: 336.0, type: 'number' },
          { value: 378.0, type: 'number' },
          { value: 42.0, type: 'number' }
        ]
      ]
    }

    return baseData[type] || baseData.excel
  }

  private static generatePayrollData(
    integrationType: string,
    coNumber: string
  ): ExportData[][] {
    const baseHeader = [
      [
        { value: 'CO_NUMBER', type: 'string' as const },
        { value: coNumber, type: 'string' as const }
      ]
    ]

    if (integrationType === 'NETHRIS') {
      return [
        ...baseHeader,
        [
          { value: 'Employee', type: 'string' },
          { value: 'Hours', type: 'string' },
          { value: 'Rate', type: 'string' }
        ],
        [
          { value: 'John Doe', type: 'string' },
          { value: 40, type: 'number' },
          { value: 15.0, type: 'number' }
        ],
        [
          { value: 'Jane Smith', type: 'string' },
          { value: 38, type: 'number' },
          { value: 16.5, type: 'number' }
        ]
      ]
    } else if (integrationType === 'EMPLOYEURD') {
      return [
        ...baseHeader,
        [
          { value: 'Employee ID', type: 'string' },
          { value: 'Hours', type: 'string' },
          { value: 'Overtime', type: 'string' }
        ],
        [
          { value: '001', type: 'string' },
          { value: 40, type: 'number' },
          { value: 5, type: 'number' }
        ],
        [
          { value: '002', type: 'string' },
          { value: 38, type: 'number' },
          { value: 2, type: 'number' }
        ]
      ]
    }

    return baseHeader
  }

  private static generateFilename(
    option: ExportOption,
    format?: ExportFormat
  ): string {
    const date = new Date().toISOString().split('T')[0]

    if (option === 'excel' && format) {
      return `payroll-export-${format}-${date}`
    }

    if (option === 'payroll') {
      return `payroll-integration-export-${date}`
    }

    return `${option}-export-${date}`
  }

  static async exportExcel(format: ExportFormat): Promise<void> {
    try {
      const data = this.generateExcelData('excel')
      const filename = this.generateFilename('excel', format)

      const config = {
        filename,
        sheet: { data }
      }

      zipcelx(config)
      toastr.success(`Excel export (${format}) downloaded successfully!`)
    } catch (error) {
      console.error('Excel export error:', error)
      throw new Error('Failed to export Excel file')
    }
  }

  static async exportPayroll(
    integrationType: string,
    coNumber: string,
    format?: ExportFormat
  ): Promise<void> {
    try {
      if (!coNumber) {
        toastr.error(I18n.t('attendance.employerD_integration_error'))
        throw new Error('CO_NUMBER is required for payroll export')
      }

      const data = this.generatePayrollData(integrationType, coNumber)
      const filename = `${integrationType.toLowerCase()}-export-${new Date().toISOString().split('T')[0]}`

      const config = {
        filename,
        sheet: { data }
      }

      zipcelx(config)
      toastr.success(
        `Payroll export (${format || 'default'}) for ${integrationType} downloaded successfully!`
      )
    } catch (error) {
      console.error('Payroll export error:', error)
      throw new Error('Failed to export payroll file')
    }
  }

  static async exportByShift(): Promise<void> {
    try {
      const data = this.generateExcelData('by-shift')
      const filename = this.generateFilename('by-shift')

      const config = {
        filename,
        sheet: { data }
      }

      zipcelx(config)
      toastr.success('By-shift export downloaded successfully!')
    } catch (error) {
      console.error('By-shift export error:', error)
      throw new Error('Failed to export by-shift file')
    }
  }

  static async exportPayEvolution(): Promise<void> {
    try {
      const data = this.generateExcelData('pay-evolution')
      const filename = this.generateFilename('pay-evolution')

      const config = {
        filename,
        sheet: { data }
      }

      zipcelx(config)
      toastr.success('Pay-evolution export downloaded successfully!')
    } catch (error) {
      console.error('Pay-evolution export error:', error)
      throw new Error('Failed to export pay-evolution file')
    }
  }

  static async export(params: PayrollExportParams): Promise<void> {
    const { option, format, coNumber, integrationType } = params

    console.log('Export option:', option, 'Format:', format)

    switch (option) {
      case 'excel':
        if (!format) throw new Error('Format is required for Excel export')
        await this.exportExcel(format)
        break

      case 'payroll':
        if (!integrationType || !coNumber) {
          throw new Error(
            'Integration type and CO_NUMBER are required for payroll export'
          )
        }
        await this.exportPayroll(integrationType, coNumber, format)
        break

      case 'by-shift':
        await this.exportByShift()
        break

      case 'pay-evolution':
        await this.exportPayEvolution()
        break

      default:
        throw new Error(`Unsupported export option: ${option}`)
    }
  }
}
