import React, { useEffect, useMemo, useState } from 'react'
import Modal from 'react-bootstrap/Modal'
import { useDispatch, useSelector } from 'react-redux'
import { I18n } from 'react-redux-i18n'

import { useRollbar } from '@rollbar/react'
import { showModal } from 'actions/showModal'
import firebase from 'firebase/app'
import 'firebase/database'
import { useAppContext } from 'index'
import { cloneDeep, findIndex, forEach, isEqual, map } from 'lodash'
import moment from 'moment'
import { RootState } from 'store/reducers'
import styled from 'styled-components'
import { theme } from 'styles/theme'
import toastr from 'toastr'

import { AvatarInitials } from 'components/ui/AvatarInitials'

import { getCurrentEmployee } from 'store/utils/getCurrentEmployee'
import { getLowestPriority } from 'utils/employees'
import { isAllowedToClick, removeArchivedPositions } from 'utils/roles'

import { Applicant } from 'types/applicant'
import { Company } from 'types/company.js'
import { IEmployee } from 'types/employee.js'
import { NewApplicantRequestNotification } from 'types/requests'

type HireApplicantModalProps = {
  isEditEmployee: boolean
  wasHired: boolean
  applicant: Applicant | IEmployee
  currentCompany: Company
  close: () => void
  onSave: ({
    priority,
    positions
  }: {
    priority: IEmployee['priority']
    positions: IEmployee['positions']
  }) => void
  markNotificationAsRead?: () => void
  removeDefinitely: () => void
}

type SelectedPosition = {
  jobId: string
  jobName: string
  subcategoryId: string
  subcategoryName: string
}

const HireApplicantModal = ({
  isEditEmployee,
  wasHired,
  applicant,
  currentCompany,
  close,
  onSave,
  markNotificationAsRead,
  removeDefinitely
}: HireApplicantModalProps) => {
  const { unreadDirectInputRequests, updateRequestsToAnswer } = useAppContext()
  const dispatch = useDispatch()
  const showHire = useSelector((state: RootState) => state.modals.hire)
  const currentEmployee = useSelector(getCurrentEmployee)
  const employees = useSelector((state: RootState) => state.employees.employees)
  const applicantsNotifications = useSelector(
    (state: RootState) => state.notifications.applicants
  )

  const [activePositionId, setActivePositionId] = useState('')
  const [selectedPositions, setSelectedPositions] = useState<
    SelectedPosition[]
  >([])
  const [isProcessing, setIsProcessing] = useState(false)

  const jobs = useMemo(
    () => removeArchivedPositions(currentCompany.jobs),
    [currentCompany.jobs]
  )

  const rollbar = useRollbar()

  useEffect(() => {
    if (applicant && jobs) {
      const positions: SelectedPosition[] = []
      if ((isEditEmployee || wasHired) && 'positions' in applicant) {
        forEach(applicant.positions, position => {
          const job = jobs[position.categoryId]
          if (
            job &&
            !job.archived &&
            job.subcategories[position.subcategoryId] &&
            !job.subcategories[position.subcategoryId].archived
          ) {
            positions.push({
              jobId: position.categoryId,
              jobName: job.name,
              subcategoryId: position.subcategoryId,
              subcategoryName: job.subcategories[position.subcategoryId].name
            })
          }
        })
      }
      setSelectedPositions(positions)
    }
  }, [applicant, isEditEmployee, wasHired, jobs])

  const onClickAccept = () => {
    if (selectedPositions.length < 1) {
      return toastr.error(I18n.t('hireForWork.chooseCategoryAndSubcategory'))
    }

    setIsProcessing(true)

    const positions = selectedPositions.map(position => ({
      categoryId: position.jobId,
      subcategoryId: position.subcategoryId
    }))

    const priority: {
      [key: string]: {
        [key: string]: number
      }
    } = {}
    positions.forEach(position => {
      const positionPriority = getLowestPriority(employees, position.categoryId)
      const subpositionPriority = getLowestPriority(
        employees,
        position.categoryId,
        position.subcategoryId
      )

      if (!priority[position.categoryId]) {
        priority[position.categoryId] = {}
      }

      if (priority[position.categoryId].positionPriority === undefined) {
        priority[position.categoryId].positionPriority = positionPriority + 1
      }

      // check if employee already has priority => then don't overwrite
      if (
        applicant.priority &&
        applicant.priority[position.categoryId] &&
        applicant.priority[position.categoryId][position.subcategoryId] !==
          undefined
      ) {
        priority[position.categoryId][position.subcategoryId] =
          applicant.priority[position.categoryId][position.subcategoryId]
      } else {
        priority[position.categoryId][position.subcategoryId] =
          subpositionPriority + 1
      }
    })

    const updates: Record<string, any> = {}
    let requestId: string | null = null

    if (isEditEmployee) {
      updates['Employees/' + (applicant as IEmployee).uid + '/priority'] =
        priority
      updates['Employees/' + (applicant as IEmployee).uid + '/positions'] =
        positions

      // take all old positionIds
      // filter the ones that are present in new positions
      // iterate over the rest to remove them as managers
      const oldPositionIds = Array.from(
        new Set(
          (applicant as IEmployee).positions?.map(
            position => position.categoryId
          ) || []
        )
      )

      if (oldPositionIds.length) {
        const newPositionIds = Array.from(
          new Set(positions.map(position => position.categoryId))
        )

        const positionIdsToRemove = oldPositionIds.filter(
          positionId => !newPositionIds.includes(positionId)
        )

        positionIdsToRemove.forEach(positionId => {
          updates[
            'Managers/' +
              currentCompany.key +
              '/' +
              applicant.uid +
              '/' +
              positionId
          ] = null
        })
      }
    } else {
      if (applicant.status === 'direct') {
        const notificationKey = firebase
          .database()
          .ref('Notifications/' + currentCompany.key + '/')
          .push().key
        updates['Notifications/' + currentCompany.key + '/' + notificationKey] =
          {
            userId: applicant.userId,
            byEmployer: true,
            type: 'direct',
            companyId: currentCompany.key,
            group: 'people',
            time: firebase.database.ServerValue.TIMESTAMP,
            statusEmployer: 'accepted',
            createdAt: firebase.database.ServerValue.TIMESTAMP
          }
      }
      updates['Applicants/' + applicant.uid + '/status'] = 'hired'
      // TODO: only save fields that we need
      updates['Employees/' + applicant.uid] = {
        ...applicant,
        hiring: applicant.hiring || firebase.database.ServerValue.TIMESTAMP,
        priority,
        positions,
        interviewType: null,
        key: null,
        uid: applicant.uid,
        message: null,
        startTime: null,
        status: null,
        category: null,
        lastPositions: null
      }

      requestId =
        Object.keys(unreadDirectInputRequests).find(
          directInputId =>
            unreadDirectInputRequests[directInputId].applicantId ===
            applicant.uid
        ) || null

      if (requestId) {
        updates[`RequestsToAnswer/${currentCompany.key}/company/${requestId}`] =
          null
      }

      const notification = applicantsNotifications.find(
        notification =>
          (notification as NewApplicantRequestNotification).keyApplicant ===
          applicant.uid
      )
      if (notification) {
        const employeeId = (currentEmployee as IEmployee).uid
        updates[
          'Notifications/' +
            currentCompany.key +
            '/' +
            notification.key +
            '/seenBy/' +
            employeeId
        ] = true

        updates[
          'Notifications/' +
            currentCompany.key +
            '/' +
            notification.key +
            '/hide'
        ] = true
      } else {
        rollbar.error('Applicant notification not found', {
          applicant
        })
      }
    }

    firebase
      .database()
      .ref()
      .update(updates)
      .then(() => {
        if (wasHired) {
          close()
        }
        toastr.success(
          applicant.name +
            ' ' +
            applicant.surname +
            (isEditEmployee ? ' saved' : ' hired')
        )
        if (requestId) {
          updateRequestsToAnswer(state => {
            const copy = cloneDeep(state)
            delete copy[requestId!]
            return copy
          })
        }
        if (onSave) {
          onSave({ priority, positions })
        }
        closeModal()
      })
      .catch(error => toastr.error(error.message))

    if (markNotificationAsRead) {
      markNotificationAsRead()
    }

    setIsProcessing(false)
  }

  const onSelect = (jobId: string, subcategoryId: string) => {
    const isAllowed = isAllowedToClick(jobId)
    if (!isAllowed) {
      return toastr.warning(
        I18n.t('employees.not_allowed_to_add_delete_this_position')
      )
    }
    const newSelectedPositions = cloneDeep(selectedPositions)
    const findedIndex = findIndex(
      newSelectedPositions,
      position => position.subcategoryId === subcategoryId
    )
    if (findedIndex > -1) {
      newSelectedPositions.splice(findedIndex, 1)
    } else {
      newSelectedPositions.push({
        jobId,
        jobName: currentCompany.jobs[jobId].name,
        subcategoryId,
        subcategoryName:
          currentCompany.jobs[jobId].subcategories[subcategoryId].name
      })
    }
    setSelectedPositions(newSelectedPositions)
  }

  const closeModal = () => {
    setActivePositionId('')
    dispatch(showModal('hire', false))
    if (wasHired) {
      close()
    }
  }

  const getSubpositionAmount = (jobId: string) => {
    return selectedPositions.filter(position => position.jobId === jobId).length
  }

  const { name, surname } = applicant

  const initialSubcategories =
    applicant.positions?.map(position => position.subcategoryId) || []
  const currentSubcategories = selectedPositions
    .map(
      position => jobs[position.jobId]?.subcategories[position.subcategoryId]
    )
    .filter(Boolean)
  const isChanged = !isEqual(initialSubcategories, currentSubcategories)

  const isDisabled = (isEditEmployee && !isChanged) || isProcessing

  return (
    <div>
      <Modal
        show={showHire || wasHired}
        onHide={closeModal}
        bsClass='modal'
        className='assign_interview newHire default-modal'
      >
        <Modal.Header closeButton>
          <div>
            <div style={{ display: 'flex', alignItems: 'center', flex: 1 }}>
              <EmployeeInfoStyled>
                <AvatarInitials employee={applicant as IEmployee} />
                <EmployeeInfoNameStyled>
                  {name} {surname}
                </EmployeeInfoNameStyled>
              </EmployeeInfoStyled>
              <button
                className='common-button common-button_green'
                style={{ position: 'unset' }}
                onClick={onClickAccept}
                disabled={isDisabled}
              >
                {isEditEmployee
                  ? I18n.t('employees.applyChanges')
                  : I18n.t('hireForWork.hireForWork')}
              </button>
            </div>
          </div>
        </Modal.Header>
        <Modal.Body>
          {!isEditEmployee && (
            <div className='topPart'>
              <span style={{ fontWeight: '500' }}>
                {isEditEmployee
                  ? I18n.t('employees.hired')
                  : I18n.t('hireForWork.applicationSubmitted')}
              </span>
              <p>{moment().format('DD.MM.YYYY')}</p>
            </div>
          )}
          <div className='middlePart inputShadow'>
            <div className='leftBlock'>
              <div className='scroll-block'>
                <div className='scroll-block_wrap'>
                  <div className='leftBlock-block'>
                    {selectedPositions.map((position, index) => (
                      <div
                        key={index}
                        className='blockRow inputShadow'
                      >
                        {position.jobName + ' - ' + position.subcategoryName}
                        <span
                          onClick={() =>
                            onSelect(position.jobId, position.subcategoryId)
                          }
                        >
                          x
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
            <div
              className='rightBlock inputShadow'
              id='rightBlock'
            >
              <div className='rightBody'>
                <div className='scroll-block'>
                  <div className='scroll-block_wrap'>
                    <div className='positions-block'>
                      {map(jobs, (job, jobId) => (
                        <div
                          key={jobId}
                          className='positionRow'
                        >
                          <div
                            className={
                              jobId === activePositionId
                                ? 'positionRow_header active'
                                : 'positionRow_header'
                            }
                            onClick={() =>
                              setActivePositionId(
                                activePositionId === jobId ? '' : jobId
                              )
                            }
                          >
                            <span
                              className={
                                jobId === activePositionId
                                  ? 'arrowClose'
                                  : 'arrowOpen'
                              }
                            />
                            {job.name} {'(' + getSubpositionAmount(jobId) + ')'}
                          </div>
                          {jobId === activePositionId && (
                            <div className='subpositions-block'>
                              {map(
                                job.subcategories,
                                (subcategory, subcategoryId) => (
                                  <div
                                    key={subcategoryId}
                                    className='subpositionRow'
                                    onClick={() =>
                                      onSelect(jobId, subcategoryId)
                                    }
                                  >
                                    <div className='roundBlock'>
                                      {selectedPositions.find(
                                        position =>
                                          position.subcategoryId ===
                                          subcategoryId
                                      ) && <div className='selected' />}
                                    </div>
                                    {subcategory.name}
                                  </div>
                                )
                              )}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Modal.Body>
      </Modal>
    </div>
  )
}

export default HireApplicantModal

const EmployeeInfoStyled = styled.div`
  display: flex;
  align-items: center;

  gap: 1rem;
  margin-right: auto;
`

const EmployeeInfoNameStyled = styled.p`
  color: ${theme.colors.darkGrey};
  font-size: 1rem;
  font-family: ${theme.fonts.normal};
`
