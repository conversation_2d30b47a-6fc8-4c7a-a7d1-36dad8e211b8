import React, {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState
} from 'react'
import Overlay from 'react-bootstrap/Overlay'
import Popover from 'react-bootstrap/Popover'
import useOnclickOutside from 'react-cool-onclickoutside'
import { useSelector } from 'react-redux'
import { I18n } from 'react-redux-i18n'
import { useHistory } from 'react-router-dom'
import { toast } from 'react-toastify'

import { useRollbar } from '@rollbar/react'
import dayjs from 'dayjs'
import firebase from 'firebase'
import {
  cloneDeep,
  forEach,
  isEmpty,
  isNumber,
  map,
  reduce,
  round,
  some
} from 'lodash'
import moment from 'moment'
import { LogArgument } from 'rollbar'
import { RootState } from 'store/reducers'
import styled from 'styled-components'
import { theme } from 'styles/theme'
import toastr from 'toastr'
import zipcelx, { ZipCelXConfig } from 'zipcelx'

import ScheduleWeekSlider from '../SchedulePage/components/ScheduleHeader/ScheduleWeekSlider'
import Loader from 'components/ui/Loader'

import { AppContext, database } from '../../index'
import LaborCostPopover from './LaborCostView/LaborCostPopover'
import LaborCostRow from './LaborCostView/LaborCostRow'
import EditShiftPopover from './TimeSheetView/EditShiftPopover'
import TimesheetRow from './TimeSheetView/TimeSheetRow'
import { generateByShiftExport } from './export-formats/generateByShiftExport'
import { generateEmployerDExportData } from './export-formats/generateEmployerDExportData'
import { generateExportData } from './export-formats/generateExportData'
import { generateNethrisExport } from './export-formats/generateNethrisExport'
import { generatePayevolutionExport } from './export-formats/generatePayevolutionExport'
import ExportPayPeriodModal from './modals/ExportPayPeriodModal'
import PayrollConflictModal from './modals/PayrollConflictModal'
import PayrollSettingsModal from './modals/PayrollSettingsModal'
import {
  addZeros,
  getBreaksLength,
  getCutAmount,
  getTimeSince,
  matchClockInWithScheduledShift,
  roundTime
} from './payrollUtils'

import {
  formatDateToMonthStringAndDayNumber,
  momentToMinutes
} from '../../utils/time'
import { getDayOfWeekIndex } from '../SchedulePage/utils/getDayOfWeekIndex'
import { OverlappingShifts, checkShiftOverlap } from 'utils/attendance'
import {
  ALL_DAYS,
  EMPLOYEURD,
  MAITRE_D,
  MAITRE_D_SETTINGS,
  MYR,
  MYR_SETTINGS,
  NETHRIS,
  SALARY_TYPE_HOURLY,
  SALARY_TYPE_YEARLY,
  VELOCE,
  VELOCE_SETTINGS
} from 'utils/constants'
import { getTotalSalary } from 'utils/employees'
import { useCanViewFinancialData } from 'utils/hooks/useCanViewFinancialData'
import { useEmployeeIntegrationIds } from 'utils/hooks/useEmployeeIntegrationIds'
import { usePayrollIntegrations } from 'utils/hooks/usePayrollIntegrations'
import { containsString } from 'utils/removeDiacriticsString'
import { isOwner } from 'utils/roles'
import { getShiftLength } from 'utils/schedule'
import { getOverlapDuration } from 'utils/time'

import {
  AttendanceSettings,
  AttendanceShift,
  AttendanceShifts,
  DailyCuts,
  IAllConflictingAttendanceShifts,
  IAttendanceEnhancedShift,
  IAttendanceEnhancedShifts,
  OvertimeCalculationMode,
  SalesPerShift,
  VeloceInvoice,
  WeeklyData
} from 'types/attendance'
import { Company } from 'types/company'
import { IEmployee } from 'types/employee'
import { ScheduleRaw, Shift } from 'types/schedule'

import ExportIcon from 'img/IconsHover/ExportIcon'
import SettingsIcon from 'img/IconsHover/SettingsFilledIcon'
import arrowOpen from 'img/icons/arrow_down.svg'
import arrowClose from 'img/icons/arrow_up.svg'
import checkGreenIcon from 'img/icons/checkGreenFillIcon.svg'
import clockIcon from 'img/icons/clockGreyIcon.svg'
import dollarIcon from 'img/icons/dollarGrey.svg'
import searchIcon from 'img/icons/searchIcon.svg'

// THINGS to discuss: labor cost persistance - do we check employees rate there??

// bonus:
// - need an ability to edit rate / additional salary on web for a specific shift

// add isWeekConfirmed

// by clicking on "2nd shift", it must show the 2nd shift selected.
// or mark as confirmed at the backend/mobile and it will solve this issue as well
// Can delete shift (with second confirmation)

// NOW
// No more yellow dot on module

// LATER
// The notification yellow dot always appears on the module until all conflicting shifts have been solved -> we need to load data for current pay period
// If there are unsolved conflicting shifts, this pop-over always appear by opening "attendance" module. To make it disappear, employer has to click anywhere else on the screen or open the "conflicting shifts" pop-over by clicking on the button.

const CONFLICTING_SHIFT_THRESHOLD_START = 15
const CONFLICTING_SHIFT_THRESHOLD_END = 30
const MINUTES_IN_DAY = 24 * 60

export const getDefaultStartOfPeriod = (firstDayOfWeek = 'Monday') => {
  const dayNumber = ALL_DAYS[firstDayOfWeek]
  // Start from 2023-02-06 and go back to the specified day of the week
  let date = dayjs('2023-02-06').day(dayNumber)

  // If the calculated date is in the future, go back one week
  if (date.isAfter(dayjs('2023-02-06'))) {
    date = date.subtract(1, 'week')
  }

  return date.format('YYYY-MM-DD')
}

export const getClosestPayPeriodStart = (
  startingWeek: string,
  payrollStartingDay: string = 'Monday',
  payrollFrequency: string,
  beforeDate?: string
) => {
  // Parse the starting week into a dayjs object
  let startDate = dayjs(startingWeek, 'YYYY-MM-DD')

  // Current date for reference
  let currentDate = beforeDate ? dayjs(beforeDate, 'YYYY-MM-DD') : dayjs()

  // Find the first occurrence of `payrollStartingDay` on or after `startingWeek`
  let targetDay = startDate.isoWeekday(ALL_DAYS[payrollStartingDay])
  if (startDate.isAfter(targetDay)) {
    targetDay = targetDay.add(1, 'week')
  }

  // Determine the number of weeks between the current date and the target start day
  const frequency = payrollFrequency === 'weekly' ? 1 : 2 // 1 week for weekly, 2 weeks for biweekly
  while (targetDay.isBefore(currentDate, 'day')) {
    targetDay = targetDay.add(frequency, 'week') // move ahead based on the payroll frequency
  }

  // If our calculation goes past the current date, step back to find the previous target date
  if (targetDay.isAfter(currentDate, 'day')) {
    targetDay = targetDay.subtract(frequency, 'week')
  }

  return targetDay
}

function Attendance() {
  const allEmployees = useSelector(
    (state: RootState) => state.employees.allEmployees
  )
  const companyId = useSelector((state: RootState) => state.currentCompanyId)
  const currentCompany = useSelector(
    (state: RootState) =>
      state.companies.find(company => company.key === state.currentCompanyId) ||
      ({} as Company)
  )

  const { maxHoursPerWeek } = useContext(AppContext).companySettings

  const locale = useSelector((state: RootState) => state.i18n.locale)
  const role = useSelector((state: RootState) => state.app.role)

  const [selectedPositionId, setselectedPositionId] = useState('')
  const [searchTerm, setsearchTerm] = useState('')
  const [attendanceData, setAttendanceData] = useState<AttendanceShifts>({})
  const [isDataLoaded, setisDataLoaded] = useState(false)
  const [showSettingsModal, setShowSettingsModal] = useState(false)

  const [selectedShiftKey, setSelectedShiftKey] = useState('')
  const [schedule, setSchedule] = useState<ScheduleRaw>({})
  const [isScheduleLoaded, setIsScheduleLoaded] = useState(false)
  const [showChangeView, setShowChangeView] = useState(false)
  const [isTimeSheetView, setIsTimeSheetView] = useState(true)
  const [showConflictPopover, setShowConflictPopover] = useState(false)
  // prevent flickering(repositioning) popovers
  const [selectedEmployeeId, setselectedEmployeeId] = useState('')
  const [selectedDate, setselectedDate] = useState('')
  const [popoverType, setPopoverType] = useState('')

  const [showConflictModal, setShowConflictModal] = useState(false)
  const conflictPopoverElement = useRef(null)

  const shiftPopoverElement = useRef(null)

  const [conflictsModalShownOnInit, setConflictsModalShownOnInit] =
    useState(false)
  const [showExportModal, setShowExportModal] = useState(false)
  const [attendanceSettings, setAttendanceSettings] =
    useState<AttendanceSettings>({
      roundingTime: 5,
      positionSettings: {},
      startingWeek: getDefaultStartOfPeriod(currentCompany.payrollStartingDay),
      tipsDeclaration: '',
      tipsDistribution: 'daily',
      tipsExport: '',
      cutExport: '',
      overtimeExport: '',
      declarationExport: '',
      cutDistribution: 'daily' as AttendanceSettings['cutDistribution'],
      cutPercentage: '',
      cutGroups: [],
      cutRoundingValue: '',
      cutRoundingType: 'down' as AttendanceSettings['cutRoundingType'],
      payrollFrequency: 'biweekly',
      overtimeCalculationMode: 'weekly' as OvertimeCalculationMode
    })

  const settingsLoaded = useRef(false)
  // this could be maitred now
  const [sales, setSales] = useState<Record<string, VeloceInvoice>>({})
  const [startingWeekSet, setStartingWeekSet] = useState(false)
  const [CO_NUMBER, setCO_NUMBER] = useState('')
  const [integrationType, setIntegrationType] = useState('')
  const [isSettingsLoaded, setIsSettingsLoaded] = useState(false)
  const [salesProvider, setSalesProvider] = useState('')

  const rollbar = useRollbar()
  const [showBreakPopover, setShowBreakPopover] = useState(false)

  const integrationsIds = useEmployeeIntegrationIds(companyId)

  const {
    hasEmployerDIntegration,
    hasNethrisIntegration,
    hasAcombaIntegration
  } = usePayrollIntegrations(companyId)

  const handleError = useCallback(
    (error: LogArgument, start: number, timezone: string) => {
      rollbar.error(error, {
        start,
        timezone
      })
      toastr.error(I18n.t('common.error_general'))
    },
    [rollbar]
  )

  const {
    roundingTime,
    positionSettings,
    cutPercentage,
    cutDistribution,
    cutGroups = [],
    cutRoundingValue,
    cutRoundingType,
    payrollFrequency = 'biweekly',
    startingWeek,
    overtimeCalculationMode = 'weekly',
    employeeOrder = 'surname'
  } = attendanceSettings

  const payrollLength = payrollFrequency === 'weekly' ? 7 : 14

  const canViewFinancialData = useCanViewFinancialData()
  const history = useHistory()

  useEffect(() => {
    if (!canViewFinancialData) {
      history.push('/schedule')
    }
  }, [canViewFinancialData, history])

  const [startOfPeriod, setStartOfPeriod] = useState(
    getClosestPayPeriodStart(
      startingWeek,
      currentCompany.payrollStartingDay,
      payrollFrequency
    )
  )

  const week1 = startOfPeriod.format('YYYY-MM-DD')
  const startOfPeriodStr = startOfPeriod.format('YYYY-MM-DD')
  const endOfPeriodStr = startOfPeriod
    .clone()
    .add(payrollLength - 1, 'days')
    .format('YYYY-MM-DD')
  let week2: string | null = null

  if (payrollFrequency === 'biweekly') {
    week2 = startOfPeriod.clone().add(1, 'week').format('YYYY-MM-DD')
  }

  const firstDayOfWeek = startOfPeriod.clone().locale('en').format('dddd')

  const employeesWithIntegrations = useMemo<IEmployee[]>(() => {
    if (!integrationsIds || !allEmployees) return Object.values(allEmployees)
    return Object.values(allEmployees).map((employee: IEmployee) => ({
      ...employee,
      ...integrationsIds[employee.uid]
    }))
  }, [allEmployees, integrationsIds])

  const hasEmployeurDNumbers =
    Object.values(employeesWithIntegrations).some(
      c => c.payrollId !== undefined
    ) &&
    (hasEmployerDIntegration || hasNethrisIntegration)

  const keyToSortBy =
    employeeOrder === 'employee-number'
      ? hasEmployeurDNumbers
        ? 'payrollId'
        : 'customId'
      : employeeOrder

  const employeesToDisplay = employeesWithIntegrations
    .filter(
      c =>
        !c.isAdmin &&
        // either have postions or have shifts this week
        (c.positions?.length ||
          some(attendanceData, dayShifts => Boolean(dayShifts[c.uid])))
    )
    .map(c => {
      if (c.lastPositions) {
        return {
          ...c,
          positions: c.lastPositions
        }
      }
      return c
    })
    .sort((a, b) => {
      if (employeeOrder === 'employee-number') {
        const aNumber = +(a[keyToSortBy] || Infinity)
        const bNumber = +(b[keyToSortBy] || Infinity)

        return aNumber > bNumber ? 1 : -1
      }

      return a[keyToSortBy]! > b[keyToSortBy]! ? 1 : -1
    })

  useEffect(() => {
    const fetchCoNumber = async (companyId: string) => {
      const integrations = [
        { type: NETHRIS, path: `NethrisSettings/${companyId}/CO_NUMBER` },
        { type: EMPLOYEURD, path: `EmployerDSettings/${companyId}/CO_NUMBER` }
      ]

      for (const integration of integrations) {
        const CO_NUMBER = await database
          .ref(integration.path)
          .get()
          .then(snapshot => snapshot.val())

        if (CO_NUMBER) {
          setCO_NUMBER(CO_NUMBER)
          setIntegrationType(integration.type)
          break
        }
      }
    }

    if (companyId) {
      fetchCoNumber(companyId)
    }
  }, [companyId])

  // payrollStartingDay updated
  useEffect(() => {
    const payrollStartingDay =
      currentCompany.key === '-NV_EKjClc6nC9ue871J'
        ? 'Friday'
        : currentCompany.payrollStartingDay
    if (payrollStartingDay && payrollStartingDay !== firstDayOfWeek) {
      setStartOfPeriod(
        getClosestPayPeriodStart(
          attendanceSettings.startingWeek,
          currentCompany.payrollStartingDay,
          payrollFrequency
        )
      )
    }
  }, [
    attendanceSettings.startingWeek,
    currentCompany.key,
    currentCompany.payrollStartingDay,
    firstDayOfWeek,
    payrollFrequency
  ])

  // startingWeek updated
  useEffect(() => {
    // TODO: this if is always true
    if (attendanceSettings.startingWeek) {
      setStartOfPeriod(
        getClosestPayPeriodStart(
          attendanceSettings.startingWeek,
          currentCompany.payrollStartingDay,
          payrollFrequency
        )
      )
      setTimeout(() => {
        // better load schedule data only when settings are loaded!
        setStartingWeekSet(true)
      }, 1000)
    }
  }, [
    attendanceSettings.startingWeek,
    currentCompany.payrollStartingDay,
    payrollFrequency
  ])

  useEffect(() => {
    if (companyId) {
      database
        .ref('AttendanceSettings/' + companyId)
        .once('value')
        .then(s => {
          const settings = s.val()

          if (settings) {
            setAttendanceSettings(state => ({
              ...state,
              ...settings
            }))
          }
          settingsLoaded.current = true
          setIsSettingsLoaded(true)
        })
    }
  }, [companyId])

  useEffect(() => {
    const checkSetting = async (setting: string) => {
      return database
        .ref(`${setting}/${companyId}`)
        .once('value')
        .then(s => Boolean(s.val()))
    }

    const loadIntegrations = async () => {
      let provider = ''

      if (await checkSetting(VELOCE_SETTINGS)) {
        provider = VELOCE
      } else if (await checkSetting(MAITRE_D_SETTINGS)) {
        provider = MAITRE_D
      } else if (await checkSetting(MYR_SETTINGS)) {
        provider = MYR
      }

      setSalesProvider(provider)
    }

    loadIntegrations()
  }, [companyId])

  useEffect(() => {
    let attendanceRef: firebase.database.Query,
      scheduleRef: firebase.database.Query

    const loadData = async () => {
      if (companyId) {
        attendanceRef = database
          .ref('Attendance/' + companyId)
          .orderByKey()
          .startAt(startOfPeriodStr)
          .endAt(endOfPeriodStr)

        attendanceRef.on('value', s => {
          const data = s.val() || {}

          setAttendanceData(data)

          setisDataLoaded(true)
        })

        scheduleRef = database
          .ref('WeeklySchedule/' + companyId)
          .orderByKey()
          .startAt(startOfPeriodStr)
          .endAt(endOfPeriodStr)

        scheduleRef.on('value', s => {
          const data = s.val() || {}

          setSchedule(data)
          setIsScheduleLoaded(true)
        })
      }
    }

    loadData()

    return () => {
      if (attendanceRef) {
        attendanceRef.off()
      }
      if (scheduleRef) {
        scheduleRef.off()
      }
      setIsScheduleLoaded(false)
      setisDataLoaded(false)
    }
  }, [companyId, startOfPeriodStr, endOfPeriodStr])

  useEffect(() => {
    if (salesProvider && companyId) {
      const salerProviderCapital =
        salesProvider.charAt(0).toUpperCase() + salesProvider.slice(1)
      const salesRef = database
        .ref(`${salerProviderCapital}Invoices/${companyId}`)
        .orderByChild('openLocalTime')
        .startAt(startOfPeriodStr)
        .endAt(endOfPeriodStr)

      salesRef.on('value', s => {
        const data = s.val() || {}
        setSales(data)
      })

      return () => {
        salesRef.off()
      }
    }
  }, [companyId, salesProvider, startOfPeriodStr, endOfPeriodStr])

  useEffect(() => {
    if (companyId && settingsLoaded.current) {
      database.ref('AttendanceSettings/' + companyId).set(attendanceSettings)
    }
  }, [attendanceSettings, companyId])

  const tableCellsRefs = useRef({})

  const selectRef = useOnclickOutside(
    () => {
      setShowChangeView(false)
    },
    { disabled: !showChangeView }
  )
  const { defaultDuration } = currentCompany

  const employeeKeyToUse =
    salesProvider === VELOCE
      ? 'veloceId'
      : salesProvider === MAITRE_D
        ? 'maitreDId'
        : ''

  const { weeklyData, dailyCuts } = useMemo(() => {
    const weeklyData: WeeklyData = {}
    const dailyCuts: DailyCuts = {}
    forEach(sales, sale => {
      // let total = 0
      // let totalAll = 0
      // let totalMatched = 0

      // const nonWaiterSales = {}
      // const employeeMatched = employeesToDisplay.find(
      //   c => c[employeeKeyToUse] === sale.carrierEmployeeId
      // )
      const isWaiterSale = employeesToDisplay.some(
        c =>
          employeeKeyToUse &&
          c[employeeKeyToUse] === sale.carrierEmployeeId &&
          c.positions!.some(
            ({ categoryId }) =>
              positionSettings[categoryId]?.salaryType === 'wage_tips'
          )
      )

      // sale.closeLocalTime.includes('2023-05-05') &&
      //   console.log(
      //     sale.closeLocalTime,
      //     Math.round(sale.total * 100) / 100,
      //     employeeMatched
      //       ? 'sale by matched employee'
      //       : 'sale by unknown employee'
      //   )

      // if (employeeMatched && !isWaiterSale) {
      //   const name = `${employeeMatched.name} ${employeeMatched.surname}`
      //   console.log(employeeMatched)
      //   // totalMatched += sale.total
      //   if (!nonWaiterSales[name]) {
      //     nonWaiterSales[name] = 0
      //   }
      //   nonWaiterSales[name] += Math.round(sale.total)
      // }

      if (isWaiterSale && sale.closeLocalTime !== '1970-01-01T00:00:00Z') {
        const date = dayjs(sale.openLocalTime)
          .startOf('week')
          .format('YYYY-MM-DD')

        const closeDate = dayjs(sale.closeLocalTime).format('YYYY-MM-DD')
        const isMorningShift = sale.closeLocalTime < closeDate + 'T17:00:00'

        if (!dailyCuts[closeDate]) {
          dailyCuts[closeDate] = {
            morning: {
              sales: 0,
              cutHours: {}
            },
            evening: {
              sales: 0,
              cutHours: {}
            }
          }
        }

        const key = isMorningShift ? 'morning' : 'evening'

        dailyCuts[closeDate][key].sales += sale.total

        if (!weeklyData[date]) {
          weeklyData[date] = {
            sales: 0,
            cutHours: {}
          }
        }

        weeklyData[date].sales += sale.total

        weeklyData[date].sales = round(weeklyData[date].sales, 2)
      }
    })
    return { weeklyData, dailyCuts }
  }, [employeesToDisplay, positionSettings, sales, employeeKeyToUse])

  const cutToPayPercentage = cutPercentage || 0

  const salesPerShift = useMemo(() => {
    const salesPerShift: SalesPerShift = {}
    forEach(sales, sale => {
      const carrierEmployeeId = sale.carrierEmployeeId

      const employee = employeesToDisplay.find(
        c => employeeKeyToUse && c[employeeKeyToUse] === carrierEmployeeId
      )

      if (!employee) {
        return
      }

      const saleStart = dayjs(sale.openLocalTime)
      const saleEnd = dayjs(sale.closeLocalTime)
      const shiftDate = saleStart.format('YYYY-MM-DD')

      const attendanceShifts = attendanceData?.[shiftDate]?.[employee.uid] || {}

      if (isEmpty(attendanceShifts)) {
        return
      }

      const dayShifts: (AttendanceShift & {
        shiftKey: string
      })[] = Object.keys(attendanceShifts).map(shiftKey => ({
        ...attendanceShifts[shiftKey],
        shiftKey
      }))

      const shift = {
        start: momentToMinutes(saleStart),
        end: momentToMinutes(saleEnd)
      }

      if (shift.end === null || shift.start === null) {
        return
      }

      if (shift.end < shift.start) {
        shift.end += 1440
      }

      // find a shift from dayShifts that has highest overlap duration with current shift
      // using getOverlapDuration function to get the duration
      // shiftDate === '2023-04-08' &&
      //   console.log(sale, dayShifts, startOfPeriod.format('YYYY-MM-DD'))
      const shiftWithHighestOverlap = dayShifts
        .filter(c => c.end !== undefined && c.end !== null)
        .reduce(
          (
            acc: {
              shift:
                | (AttendanceShift & {
                    shiftKey: string
                  })
                | null
              overlapDuration: number
            },
            shift2
          ) => {
            if (shift2.end === undefined || shift2.start === undefined) {
              return acc
            }
            const overlapDuration = getOverlapDuration(
              shift as {
                start: number
                end: number
              },
              shift2 as {
                start: number
                end: number
              },
              defaultDuration
            )

            if (overlapDuration > acc.overlapDuration) {
              return {
                shift: shift2,
                overlapDuration
              }
            }

            return acc
          },
          {
            shift: null,
            overlapDuration: 0
          }
        )

      // shiftDate === '2023-04-08' &&
      //   console.log(sale, shift, shiftWithHighestOverlap)

      if (
        shiftWithHighestOverlap.overlapDuration > 0 &&
        shiftWithHighestOverlap.shift?.shiftKey
      ) {
        if (!salesPerShift[shiftDate]) {
          salesPerShift[shiftDate] = {}
        }
        if (!salesPerShift[shiftDate][employee.uid]) {
          salesPerShift[shiftDate][employee.uid] = {}
        }
        if (
          !salesPerShift[shiftDate][employee.uid][
            shiftWithHighestOverlap.shift.shiftKey
          ]
        ) {
          salesPerShift[shiftDate][employee.uid][
            shiftWithHighestOverlap.shift.shiftKey
          ] = {
            tips: 0,
            sales: 0,
            cashSales: 0
          }
        }
        salesPerShift[shiftDate][employee.uid][
          shiftWithHighestOverlap.shift.shiftKey
        ].tips += sale.tipsTotalAmount
        salesPerShift[shiftDate][employee.uid][
          shiftWithHighestOverlap.shift.shiftKey
        ].sales += sale.total
        salesPerShift[shiftDate][employee.uid][
          shiftWithHighestOverlap.shift.shiftKey
        ].cashSales += sale.paidByCash || 0

        salesPerShift[shiftDate][employee.uid][
          shiftWithHighestOverlap.shift.shiftKey
        ].tips =
          Math.round(
            salesPerShift[shiftDate][employee.uid][
              shiftWithHighestOverlap.shift.shiftKey
            ].tips * 100
          ) / 100
        salesPerShift[shiftDate][employee.uid][
          shiftWithHighestOverlap.shift.shiftKey
        ].sales =
          Math.round(
            salesPerShift[shiftDate][employee.uid][
              shiftWithHighestOverlap.shift.shiftKey
            ].sales * 100
          ) / 100
        salesPerShift[shiftDate][employee.uid][
          shiftWithHighestOverlap.shift.shiftKey
        ].cashSales =
          Math.round(
            salesPerShift[shiftDate][employee.uid][
              shiftWithHighestOverlap.shift.shiftKey
            ].cashSales * 100
          ) / 100
      }
    })

    return salesPerShift
  }, [
    attendanceData,
    employeesToDisplay,
    defaultDuration,
    sales,
    employeeKeyToUse
  ])

  let numberOfConflicts = 0

  const { attendanceDataEnhanced, allConflictingShifts, overlappingsDates } =
    useMemo(() => {
      // Reset overlapping dates for each render
      const overlappingsDates = new Set<string>()

      type PartiallyEnhancedShift = Omit<
        IAttendanceEnhancedShift,
        | 'overtimeDuration'
        | 'salary'
        | 'totalSales'
        | 'cashSales'
        | 'totalDue'
        | 'cutToPay'
        | 'cutToReceive'
        | 'tips'
        | 'cutPercentageToShare'
      >
      const temp: {
        [date: string]: {
          [employeeId: string]: {
            [shiftKey: string]: PartiallyEnhancedShift
          }
        }
      } = {}

      const allConflictingShifts: IAllConflictingAttendanceShifts = {}

      const result: IAttendanceEnhancedShifts = {}

      forEach(attendanceData, (dayEmployees, date) => {
        const isToday = dayjs().format('YYYY-MM-DD') === date
        const startOfWeek = dayjs(date).startOf('week').format('YYYY-MM-DD')

        forEach(dayEmployees, (employeeShifts, employeeId) => {
          forEach(employeeShifts, (shift, shiftKey) => {
            const nextDayShifts =
              attendanceData[dayjs(date).add(1, 'day').format('YYYY-MM-DD')]?.[
                employeeId
              ] || {}

            const previousDayShifts =
              attendanceData[
                dayjs(date).subtract(1, 'day').format('YYYY-MM-DD')
              ]?.[employeeId] || {}
            // Combine all shifts
            const shiftsToCheck = {
              employeeShifts,
              nextDayShifts,
              previousDayShifts
            }

            let overlappedShifts: OverlappingShifts = {}

            if (dayjs(date).isAfter('2024-12-15')) {
              overlappedShifts = checkShiftOverlap(shiftsToCheck)
              const hasShiftOverlaps = Object.values(overlappedShifts).some(
                shift => shift.isStartOverlapping || shift.isEndOverlapping
              )

              if (hasShiftOverlaps) {
                overlappingsDates.add(date)
              }
            }

            const shiftStartRounded = roundTime(shift.start, roundingTime)
            const notClockedOut = shift.end === undefined || shift.end === null
            const shiftEndRounded = notClockedOut
              ? null
              : roundTime(shift.end, roundingTime)

            let isClockInDifferent = true
            let isClockOutDiffrent = true

            let shiftToCheckAgainst = null

            const scheduledPositions = schedule?.[date]?.[employeeId] || {}

            if (!isEmpty(scheduledPositions) && shift.start !== undefined) {
              const dayShifts: Shift[] = []

              forEach(scheduledPositions, (subpositions, positionId) => {
                forEach(subpositions, (subpositionShifts, subcategoryId) => {
                  forEach(subpositionShifts, (scheduledShift, shiftKey) => {
                    dayShifts.push({
                      ...scheduledShift,
                      positionId,
                      subcategoryId,
                      shiftKey
                    })
                  })
                })
              })

              const matchedShift = matchClockInWithScheduledShift({
                dayShifts,
                shiftStartRounded,
                defaultDuration,
                shift: {
                  start: shiftStartRounded,
                  end: shiftEndRounded
                }
              })

              if (matchedShift) {
                shiftToCheckAgainst = matchedShift
              }
            }

            if (shiftToCheckAgainst) {
              // Check if clocked-in more than 15 minutes earlier than scheduled
              isClockInDifferent =
                shiftToCheckAgainst.start - shiftStartRounded >=
                CONFLICTING_SHIFT_THRESHOLD_START

              const scheduledShiftEnd = shiftToCheckAgainst.end

              if (
                scheduledShiftEnd === undefined ||
                scheduledShiftEnd === null
              ) {
                isClockOutDiffrent = false
              } else {
                // Convert all times to minutes since midnight
                const shiftEndInMinutes = shiftEndRounded
                const scheduledEndInMinutes = +scheduledShiftEnd
                const shiftStartInMinutes = shiftToCheckAgainst.start

                // Handle shifts that cross midnight by adding a day's worth of minutes
                const shiftEndAdjustedForOvernight =
                  shiftEndInMinutes < shiftStartInMinutes
                    ? shiftEndInMinutes + MINUTES_IN_DAY
                    : shiftEndInMinutes

                const scheduledEndAdjustedForOvernight =
                  scheduledEndInMinutes < shiftStartInMinutes
                    ? scheduledEndInMinutes + MINUTES_IN_DAY
                    : scheduledEndInMinutes

                // Check if the clock-out time exceeded the scheduled end by more than the threshold
                isClockOutDiffrent =
                  shiftEndAdjustedForOvernight -
                    scheduledEndAdjustedForOvernight >
                  CONFLICTING_SHIFT_THRESHOLD_END
              }
            }

            let expectedShiftLength = null

            if (isToday && notClockedOut) {
              if (shiftToCheckAgainst) {
                expectedShiftLength = getShiftLength(
                  shiftToCheckAgainst.start,
                  shiftToCheckAgainst.end,
                  defaultDuration
                )
              } else {
                expectedShiftLength = defaultDuration
              }
            }

            const isWorking = Boolean(
              isToday &&
                notClockedOut &&
                expectedShiftLength &&
                getTimeSince(
                  shiftStartRounded,
                  currentCompany.timezone,
                  handleError
                ) < expectedShiftLength
            )

            const isConflictingShift =
              !shift.isConfirmed &&
              (isClockInDifferent ||
                isClockOutDiffrent ||
                !shift.positionId ||
                (notClockedOut && !isWorking))

            let shiftLength =
              isNumber(shiftStartRounded) && isNumber(shiftEndRounded)
                ? getShiftLength(
                    shiftStartRounded,
                    shiftEndRounded,
                    defaultDuration
                  )
                : 0

            const breaksLength = getBreaksLength(shift.breaks, roundingTime)

            if (shiftLength && breaksLength) {
              shiftLength -= breaksLength

              if (shiftLength < 0) {
                shiftLength = 0
              }
            }

            const shiftLengthHours = round(shiftLength / 60, 2)

            let positionId = ''
            const shiftPeriods: {
              key: 'morning' | 'evening'
              overlapHours: number
            }[] = []

            if (
              (!isConflictingShift || shift.isConfirmed) &&
              (shift.positionId || shiftToCheckAgainst?.positionId)
            ) {
              positionId = shift.positionId || shiftToCheckAgainst!.positionId
              if (positionSettings[positionId]?.salaryType === 'wage_cut') {
                if (!weeklyData[startOfWeek]) {
                  weeklyData[startOfWeek] = {
                    sales: 0,
                    cutHours: {}
                  }
                }

                const endsEvening =
                  shiftEndRounded >= 17 * 60 || shiftEndRounded < 300
                const startsMorning =
                  shiftStartRounded < 17 * 60 && shiftStartRounded >= 300

                const periodKeys: ('morning' | 'evening')[] = endsEvening
                  ? startsMorning
                    ? ['morning', 'evening']
                    : ['evening']
                  : ['morning']

                if (!dailyCuts[date]) {
                  dailyCuts[date] = {
                    morning: {
                      sales: 0,
                      cutHours: {}
                    },
                    evening: {
                      sales: 0,
                      cutHours: {}
                    }
                  }
                }

                periodKeys.forEach(key => {
                  const positionsToUpdate = cutGroups.find(group =>
                    group.includes(positionId as string)
                  ) || [positionId]

                  const shiftToCompareWith =
                    key === 'morning'
                      ? {
                          start: 300,
                          end: 17 * 60
                        }
                      : {
                          start: 17 * 60,
                          end: 300
                        }

                  const overlapDuration = getOverlapDuration(
                    {
                      start: shiftStartRounded,
                      end: shiftEndRounded
                    },
                    shiftToCompareWith,
                    defaultDuration
                  )

                  const overlapHours = round(overlapDuration / 60, 2)

                  shiftPeriods.push({
                    key,
                    overlapHours
                  })

                  positionsToUpdate.forEach(positionId => {
                    if (!positionId) {
                      return
                    }
                    if (!dailyCuts[date][key].cutHours[positionId]) {
                      dailyCuts[date][key].cutHours[positionId] = 0
                    }
                    dailyCuts[date][key].cutHours[positionId] += overlapHours

                    if (!weeklyData[startOfWeek].cutHours[positionId]) {
                      weeklyData[startOfWeek].cutHours[positionId] = 0
                    }

                    weeklyData[startOfWeek].cutHours[positionId] += overlapHours
                  })
                })
              }
            }

            // If schedule shift started and punchedIn
            if (shift.start && shift.positionId) {
              positionId = shift.positionId
            }

            if (!temp[date]) {
              temp[date] = {}
            }
            if (!temp[date][employeeId]) {
              temp[date][employeeId] = {}
            }

            // add shift sales / shift sales by cash / shift tips
            // / shift cut (ideally) / shift total due
            temp[date][employeeId][shiftKey] = {
              ...shift,
              isConflictingShift,
              shiftLengthHours,
              shiftEndRounded,
              shiftStartRounded,
              shiftPeriods,
              isClockInDifferent,
              isClockOutDiffrent,
              notClockedOut,
              isWorking,
              isConfirmed: Boolean(shift.isConfirmed),
              positionId,
              isStartOverlapping:
                overlappedShifts[shiftKey]?.isStartOverlapping || false,
              isEndOverlapping:
                overlappedShifts[shiftKey]?.isEndOverlapping || false
            }

            if (shiftToCheckAgainst) {
              temp[date][employeeId][shiftKey].scheduledShift =
                shiftToCheckAgainst
            }

            if (isScheduleLoaded && isDataLoaded) {
              if (isConflictingShift && !isWorking) {
                if (!allConflictingShifts[date]) {
                  allConflictingShifts[date] = {}
                }
                if (!allConflictingShifts[date][employeeId]) {
                  allConflictingShifts[date][employeeId] = {}
                }
                allConflictingShifts[date][employeeId][shiftKey] = {
                  ...shift,
                  isClockInDifferent,
                  isClockOutDiffrent,
                  neverClockedOut: isWorking ? false : notClockedOut,
                  shiftStartRounded,
                  shiftEndRounded,
                  scheduledShift: shiftToCheckAgainst,
                  missingPosition: !shift.positionId
                }
                numberOfConflicts++
              }
            }
          })
        })
      })

      forEach(temp, (dayEmployees, date) => {
        forEach(dayEmployees, (employeeShifts, employeeId) => {
          forEach(employeeShifts, (shift, shiftKey) => {
            const {
              shiftLengthHours,
              shiftPeriods = [],
              rate = 0,
              additionalSalary = 0,
              type = SALARY_TYPE_HOURLY
            } = shift

            const isSecondWeekShift = week2 && date >= week2

            const pastShiftsDuration = reduce(
              temp,
              (acc, dayEmployees, date2) => {
                if (!dayEmployees[employeeId]) {
                  return acc
                }

                // if overtimeCalculationMode === weekly
                // then we only need hours for that week
                if (
                  overtimeCalculationMode === 'weekly' &&
                  payrollFrequency === 'biweekly'
                ) {
                  const isSecondWeek = week2 && date2 >= week2

                  if (isSecondWeek !== isSecondWeekShift) {
                    return acc
                  }
                }

                let dayShifts: PartiallyEnhancedShift[] = []

                if (date2 < date) {
                  dayShifts = Object.values(dayEmployees[employeeId]).filter(
                    c => c.isConfirmed || !c.isConflictingShift
                  )
                }

                // make sure we don't include current shift
                // and make sure we don't include shifts later that day
                if (date2 === date) {
                  dayShifts = Object.values(dayEmployees[employeeId]).filter(
                    c =>
                      !c.isConflictingShift &&
                      c.start !== undefined &&
                      shift.start !== undefined &&
                      c.start > shift.start
                  )
                }

                const dayShiftsDuration = dayShifts.reduce(
                  (acc, shift) => acc + shift.shiftLengthHours,
                  0
                )

                return acc + dayShiftsDuration
              },
              0
            )

            let overtimeDuration = 0
            const overtimeThreshold =
              overtimeCalculationMode === 'weekly'
                ? maxHoursPerWeek
                : maxHoursPerWeek * 2
            const isOvertimeShift =
              overtimeCalculationMode === 'not-calculated'
                ? false
                : pastShiftsDuration + shiftLengthHours > overtimeThreshold

            if (isOvertimeShift) {
              overtimeDuration =
                pastShiftsDuration > overtimeThreshold
                  ? shiftLengthHours
                  : pastShiftsDuration + shiftLengthHours - overtimeThreshold
            }

            let salary =
              // TODO: handle overtime when the last shift is yearly salary
              overtimeDuration && type !== SALARY_TYPE_YEARLY
                ? getTotalSalary({
                    rate: +rate * 1.5,
                    additionalSalary: 0,
                    shiftLengthHours: overtimeDuration,
                    type
                  }) +
                  getTotalSalary({
                    rate: +rate,
                    additionalSalary: +additionalSalary,
                    shiftLengthHours: shiftLengthHours - overtimeDuration,
                    type
                  })
                : getTotalSalary({
                    rate: +rate,
                    additionalSalary: +additionalSalary,
                    shiftLengthHours: shiftLengthHours,
                    type
                  })

            salary = round(salary, 2)

            if (!result[date]) {
              result[date] = {}
            }
            if (!result[date][employeeId]) {
              result[date][employeeId] = {}
            }

            const {
              sales = 0,
              cashSales = 0,
              tips = 0
            } = salesPerShift[date]?.[employeeId]?.[shiftKey] || {}

            let { salaryType = 'wage' } =
              positionSettings[shift.positionId] || {}

            if (!salaryType) {
              salaryType = 'wage'
            }

            const isPayingCut = salaryType === 'wage_tips'
            const isReceivingCut = salaryType === 'wage_cut'

            let cutToPay = 0
            let totalDue = 0
            let cutToReceive = 0
            let cutPercentageToShare = 0

            if (isPayingCut) {
              cutToPay = round((+cutToPayPercentage / 100) * sales, 2)

              totalDue = round(-tips + cashSales + cutToPay, 2)

              if (totalDue > 0) {
                totalDue = 0
              }
            }

            const startOfWeek = dayjs(date).startOf('week').format('YYYY-MM-DD')

            if (isReceivingCut && shift.positionId) {
              const positionToSumUp = cutGroups.find(group =>
                group.includes(shift.positionId)
              ) || [shift.positionId]

              cutPercentageToShare = positionToSumUp.reduce(
                (acc, positionId) => {
                  const { percentage = 0 } = positionSettings[positionId] || {}
                  return acc + +percentage
                },
                0
              )

              shiftPeriods.forEach(period => {
                cutToReceive += getCutAmount(cutDistribution, {
                  weeklyData,
                  dailyCuts,
                  startOfWeek,
                  date,
                  positionId: shift.positionId,
                  shiftLengthHours: period.overlapHours,
                  cutGroups,
                  positionSettings,
                  cutRoundingValue,
                  cutRoundingType,
                  period: period.key
                })
              })

              cutToReceive = round(cutToReceive, 2)
            }

            result[date][employeeId][shiftKey] = {
              ...shift,
              overtimeDuration: Math.round(overtimeDuration * 100) / 100,
              salary,
              additionalSalary,
              totalSales: sales,
              cashSales,
              totalDue,
              cutToPay,
              cutToReceive,
              tips,
              cutPercentageToShare
            }
          })
        })
      })

      return {
        attendanceDataEnhanced: result,
        allConflictingShifts,
        overlappingsDates
      }
    }, [
      attendanceData,
      currentCompany.timezone,
      cutDistribution,
      cutGroups,
      cutRoundingType,
      cutRoundingValue,
      cutToPayPercentage,
      dailyCuts,
      defaultDuration,
      handleError,
      isDataLoaded,
      isScheduleLoaded,
      maxHoursPerWeek,
      numberOfConflicts,
      overtimeCalculationMode,
      payrollFrequency,
      positionSettings,
      roundingTime,
      salesPerShift,
      schedule,
      week2,
      weeklyData
    ])

  const summary = useMemo(() => {
    const result: {
      [key: string]: {
        totalHours: number
        totalSalary: number
        positionHours: {
          [key: string]: number
        }
        totalCut: number
        totalDue: number
        weeklyHours: {
          [key: string]: number
        }
        weeklyCut: {
          [key: string]: number
        }
        weeklyTips: {
          [key: string]: number
        }
        weeklySalary: {
          [key: string]: number
        }
        weeklyTotalDue: {
          [key: string]: number
        }
        weekKeys: string[]
      }
    } = {}

    employeesToDisplay.forEach(employee => {
      result[employee.uid] = {
        totalHours: 0,
        totalSalary: 0,
        positionHours: {},
        totalCut: 0,
        totalDue: 0,
        weeklyHours: {
          [week1]: 0
        },
        weeklyCut: {
          [week1]: 0
        },
        weeklyTips: {
          [week1]: 0
        },
        weeklySalary: {
          [week1]: 0
        },
        weeklyTotalDue: {
          [week1]: 0
        },
        weekKeys: [week1]
      }

      if (payrollFrequency === 'biweekly' && week2) {
        result[employee.uid].weeklyHours[week2] = 0
        result[employee.uid].weeklyCut[week2] = 0
        result[employee.uid].weeklyTips[week2] = 0
        result[employee.uid].weeklySalary[week2] = 0
        result[employee.uid].weeklyTotalDue[week2] = 0
        result[employee.uid].weekKeys.push(week2)
      }

      forEach(attendanceDataEnhanced, (dayEmployees, date) => {
        forEach(dayEmployees[employee.uid], shift => {
          const {
            shiftLengthHours,
            salary,
            totalDue,
            cutToPay,
            cutToReceive,
            tips
          } = shift

          if (!result[employee.uid].positionHours[shift.positionId]) {
            result[employee.uid].positionHours[shift.positionId] = 0
          }

          const startOfWeek = !week2 || date < week2 ? week1 : week2

          const { salaryType = 'wage' } =
            positionSettings[shift.positionId] || {}

          result[employee.uid].totalHours += shiftLengthHours
          result[employee.uid].positionHours[shift.positionId] +=
            shiftLengthHours

          const salaryPerShift = salary

          result[employee.uid].totalSalary += salaryPerShift
          result[employee.uid].weeklyHours[startOfWeek] += shiftLengthHours

          if (salaryType === 'wage_cut') {
            result[employee.uid].totalCut += cutToReceive
            result[employee.uid].weeklyCut[startOfWeek] += cutToReceive
          }

          // if tips => bonus included in total due
          // otherwise include it in total salary
          if (salaryType === 'wage_tips') {
            result[employee.uid].weeklyTips[startOfWeek] += tips - cutToPay

            result[employee.uid].weeklyTotalDue[startOfWeek] += -totalDue
            result[employee.uid].totalDue += -totalDue
          }

          result[employee.uid].weeklySalary[startOfWeek] += salaryPerShift
          // result[employee.uid].totalSalary += tips - cutToPay
        })
      })

      result[employee.uid].totalHours = round(
        result[employee.uid].totalHours,
        2
      )
      result[employee.uid].totalSalary = round(
        result[employee.uid].totalSalary,
        2
      )
      result[employee.uid].weeklyHours[week1] = round(
        result[employee.uid].weeklyHours[week1],
        2
      )

      result[employee.uid].weeklyCut[week1] = round(
        result[employee.uid].weeklyCut[week1],
        2
      )

      result[employee.uid].weeklyTips[week1] = round(
        result[employee.uid].weeklyTips[week1],
        2
      )

      result[employee.uid].weeklySalary[week1] = round(
        result[employee.uid].weeklySalary[week1],
        2
      )

      result[employee.uid].weeklyTotalDue[week1] = round(
        result[employee.uid].weeklyTotalDue[week1],
        2
      )

      if (week2) {
        result[employee.uid].weeklyHours[week2] = round(
          result[employee.uid].weeklyHours[week2],
          2
        )
        result[employee.uid].weeklyCut[week2] = round(
          result[employee.uid].weeklyCut[week2],
          2
        )
        result[employee.uid].weeklyTips[week2] = round(
          result[employee.uid].weeklyTips[week2],
          2
        )
        result[employee.uid].weeklySalary[week2] = round(
          result[employee.uid].weeklySalary[week2],
          2
        )
        result[employee.uid].weeklyTotalDue[week2] = round(
          result[employee.uid].weeklyTotalDue[week2],
          2
        )
      }
    })

    return result
  }, [
    employeesToDisplay,
    week1,
    payrollFrequency,
    attendanceDataEnhanced,
    week2,
    positionSettings
  ])

  // Check if position is archived and there are shifts for that position
  const shouldDisplayPosition = (
    position: Company['jobs'][string],
    key: string
  ) => {
    return !position.archived
      ? true
      : some(attendanceDataEnhanced, dayShifts => {
          return some(dayShifts, employeeShifts => {
            return some(employeeShifts, shift => {
              return (
                shift.scheduledShift?.positionId === key ||
                shift.positionId === key
              )
            })
          })
        })
  }

  // we only need positions that are not archived or have shifts

  const jobs: {
    [key: string]: Company['jobs'][string]
  } = {}

  forEach(currentCompany.jobs, (job, key) => {
    const shouldInclude = shouldDisplayPosition(job, key)

    if (shouldInclude) {
      jobs[key] = job
    }
  })

  const onClose = () => {
    setselectedDate('')
    setselectedEmployeeId('')
    setPopoverType('')
    setSelectedShiftKey('')
  }

  const onExport = async (
    option: string,
    exportFormat?: 'simplified' | 'detailed' | 'xls' | 'txt'
  ) => {
    const startAsDate = startOfPeriod.format('YYYY-MM-DD')
    const endAsDate = startOfPeriod
      .clone()
      .add(payrollLength - 1, 'days')
      .format('YYYY-MM-DD')

    if (option === 'excel') {
      const data = generateExportData({
        startOfPeriod,
        startAsDate,
        endAsDate,
        employees: employeesToDisplay,
        jobs,
        name: currentCompany.name,
        shifts: attendanceDataEnhanced,
        isTwoWeekPayPeriod: payrollFrequency === 'biweekly',
        hasEmployeurDNumbers,
        exportFormat: exportFormat as 'simplified' | 'detailed',
        maxHoursPerWeek: maxHoursPerWeek
      })

      const config = {
        filename: `${I18n.t(
          'attendance.by_week_format_title'
        )}_${startAsDate}_${endAsDate}`,
        sheet: {
          data
        }
      } as ZipCelXConfig

      zipcelx(config)
    }

    if (option === 'by-shift') {
      const data = generateByShiftExport({
        startAsDate,
        endAsDate,
        employees: employeesToDisplay,
        jobs,
        name: currentCompany.name,
        shifts: attendanceDataEnhanced,
        hasEmployeurDNumbers,
        hasAcombaIntegration
      })

      const config = {
        filename: `${I18n.t(
          'attendance.by_shift_format_title'
        )}_${startAsDate}_${endAsDate}`,
        sheet: {
          data
        }
      } as ZipCelXConfig

      zipcelx(config)
    }

    if (option === 'payroll') {
      if (!CO_NUMBER) {
        toastr.error(I18n.t('attendance.employerD_integration_error'))
        setShowExportModal(false)
        return
      }

      const employeesSorted =
        employeeOrder !== 'employee-number'
          ? employeesToDisplay
          : cloneDeep(employeesToDisplay).sort((a, b) =>
              +(a.payrollId || Infinity) > +(b.payrollId || Infinity) ? 1 : -1
            )

      try {
        if (exportFormat === 'xls') {
          const data = generateNethrisExport({
            employees: employeesSorted,
            shiftsByDay: attendanceDataEnhanced,
            startAsDate,
            positionSettings: attendanceSettings.positionSettings,
            CO_NUMBER,
            overtimeSection: attendanceSettings.overtimeExport,
            isTwoWeekPayPeriod: payrollFrequency === 'biweekly',
            endAsDate,
            companyId,
            maxHoursPerWeek: maxHoursPerWeek
          })

          const config = {
            filename: `${integrationType}_export_${startAsDate}-${endAsDate}`,
            sheet: {
              data
            }
          } as ZipCelXConfig
          zipcelx(config)
        } else {
          const txtContent = generateEmployerDExportData({
            employees: employeesSorted,
            startOfPeriod,
            CO_NUMBER,
            attendanceDataEnhanced,
            salesPerShift,
            weeklyData,
            attendanceSettings,
            cutDistribution,
            dailyCuts,
            cutGroups,
            cutRoundingValue,
            cutRoundingType,
            endAsDate,
            maxHoursPerWeek: maxHoursPerWeek
          })

          const filename = `Payroll ${startAsDate}-${endAsDate}.txt`

          // Create a new Blob object
          const blob = new Blob([txtContent], { type: 'text/plain' })

          // Create a new anchor element and set its href attribute to the Blob object
          const anchor = document.createElement('a')
          anchor.href = window.URL.createObjectURL(blob)

          // Set the anchor's download attribute to the filename
          anchor.download = filename

          // Click the anchor element to prompt the user to download the file
          anchor.click()
        }
      } catch (error: any) {
        console.log(error)
        toastr.error(error.message)
      }
    }
    if (option === 'pay-evolution') {
      const data = generatePayevolutionExport({
        employees: employeesToDisplay,
        shiftsByDay: attendanceDataEnhanced,
        startAsDate,
        isTwoWeekPayPeriod: payrollFrequency === 'biweekly'
      })

      const config = {
        filename: `Payevolution_export`,
        sheet: {
          data
        }
      } as ZipCelXConfig
      zipcelx(config)
    }

    setShowExportModal(false)
  }

  const onSave = (newShift: { [key: string]: AttendanceShift }) => {
    const copy = cloneDeep(attendanceData)

    if (!copy[selectedDate]) {
      copy[selectedDate] = {}
    }

    if (!copy[selectedDate][selectedEmployeeId]) {
      copy[selectedDate][selectedEmployeeId] = {}
    }

    copy[selectedDate][selectedEmployeeId] = {
      ...copy[selectedDate][selectedEmployeeId],
      ...newShift
    }

    setSelectedShiftKey(Object.keys(newShift)[0])
    setAttendanceData(copy)
    onClose()
  }

  const onDeleteShift = (shiftKey: string) => {
    const copy = cloneDeep(attendanceData)

    if (copy[selectedDate] && copy[selectedDate][selectedEmployeeId]) {
      delete copy[selectedDate][selectedEmployeeId][shiftKey]
    }

    setAttendanceData(copy)
  }

  if (isEmpty(currentCompany)) {
    return false
  }

  const selectedShifts =
    (selectedDate &&
      attendanceDataEnhanced?.[selectedDate]?.[selectedEmployeeId]) ||
    {}

  const isCurrentPayPeriod = dayjs().isBetween(
    startOfPeriod,
    startOfPeriod.clone().add(2, 'weeks'),
    undefined,
    '[]'
  )
  const isPast = dayjs().isAfter(startOfPeriod)
  const hasDataToExport = false
  const filteredEmployees = employeesToDisplay
    .filter(
      employee =>
        !searchTerm ||
        containsString(employee.name + ' ' + employee.surname, searchTerm)
    )
    .filter(
      employee =>
        !selectedPositionId ||
        employee.positions?.some(
          ({ categoryId }) => categoryId === selectedPositionId
        ) ||
        some(attendanceData, dayShifts =>
          some(
            dayShifts[employee.uid] || {},
            shift => shift.positionId === selectedPositionId
          )
        )
    )

  if (
    numberOfConflicts > 1 &&
    !conflictsModalShownOnInit &&
    startingWeekSet &&
    currentCompany.payrollStartingDay
  ) {
    setShowConflictModal(true)
    setConflictsModalShownOnInit(true)
  }

  const isAllowedToSeeSalary = ['owner', 'head-office'].includes(role)

  if (!isSettingsLoaded) {
    return <Loader />
  }

  return (
    <PageWrapperStyled id='attendance-page'>
      {showConflictModal && (
        <PayrollConflictModal
          showModal={showConflictModal}
          onClose={() => setShowConflictModal(false)}
          conflicts={allConflictingShifts}
          roundingTime={roundingTime}
          companyId={companyId}
          jobs={jobs}
        />
      )}
      {showExportModal && (
        <ExportPayPeriodModal
          showModal={showExportModal}
          onClose={() => setShowExportModal(false)}
          onClick={onExport}
          integrationType={integrationType}
        />
      )}
      <PayrollSettingsModal
        showModal={showSettingsModal}
        onClose={() => setShowSettingsModal(false)}
        jobs={jobs}
        attendanceSettings={attendanceSettings}
        setAttendanceSettings={setAttendanceSettings}
        maxHoursPerWeek={maxHoursPerWeek}
      />

      {popoverType === 'timesheet' && (
        <Overlay
          rootClose
          show={Boolean(selectedEmployeeId && popoverType)}
          target={() => shiftPopoverElement.current}
          placement={
            getDayOfWeekIndex(selectedDate, currentCompany.payrollStartingDay) >
            2
              ? 'left'
              : 'right'
          }
          onHide={() => {
            if (showBreakPopover) {
              return
            }
            onClose()
          }}
          key={`${selectedEmployeeId}-${selectedDate}-${selectedShiftKey}`}
          container={() => shiftPopoverElement.current}
        >
          <EditShiftPopover
            id='attendance-shift-popover'
            onClose={onClose}
            shifts={selectedShifts}
            employee={
              employeesToDisplay.find(e => e.uid === selectedEmployeeId)!
            }
            date={selectedDate}
            jobs={jobs}
            roundingTime={roundingTime}
            companyId={companyId}
            onSave={onSave}
            shiftKey={selectedShiftKey}
            onDeleteShift={onDeleteShift}
            isOnLeft={
              getDayOfWeekIndex(
                selectedDate,
                currentCompany.payrollStartingDay
              ) > 2
            }
            showBreakPopover={showBreakPopover}
            setShowBreakPopover={setShowBreakPopover}
          />
        </Overlay>
      )}
      {popoverType === 'labor-cost' && (
        <Overlay
          rootClose
          show={Boolean(selectedEmployeeId && popoverType)}
          target={() => shiftPopoverElement.current}
          placement={
            getDayOfWeekIndex(selectedDate, currentCompany.payrollStartingDay) >
            2
              ? 'left'
              : 'right'
          }
          onHide={() => {
            if (showBreakPopover) {
              return
            }
            onClose()
          }}
          key={`${selectedEmployeeId}-${selectedDate}-${selectedShiftKey}`}
          container={() => shiftPopoverElement.current}
        >
          <LaborCostPopover
            id='attendance-shift-popover-labor-cost'
            onClose={onClose}
            shifts={selectedShifts}
            employee={
              employeesToDisplay.find(e => e.uid === selectedEmployeeId)!
            }
            date={selectedDate}
            jobs={jobs}
            companyId={companyId}
            shiftKey={selectedShiftKey}
            onDeleteShift={onDeleteShift}
            setPopoverType={setPopoverType}
            attendanceSettings={attendanceSettings}
            allConflictingShifts={allConflictingShifts}
            salesPerShift={salesPerShift}
            weeklyData={weeklyData}
            dailyCuts={dailyCuts}
            schedule={schedule}
            cutDistribution={cutDistribution}
            cutGroups={cutGroups}
            cutRoundingValue={cutRoundingValue}
            cutRoundingType={cutRoundingType}
            defaultDuration={defaultDuration}
            isOnLeft={
              getDayOfWeekIndex(
                selectedDate,
                currentCompany.payrollStartingDay
              ) > 2
            }
          />
        </Overlay>
      )}

      {(showConflictPopover || !isDataLoaded) && <DarkOverlayStyled />}
      <HeaderBlockStyled>
        <HeaderBlockInfoStyled>
          <ScheduleWeekSlider
            interval={payrollLength}
            scheduleState={''}
            currentMonday={moment(
              startOfPeriod.format('YYYY-MM-DD'),
              'YYYY-MM-DD'
            )}
            changeWeek={diff => {
              setisDataLoaded(false)
              setIsScheduleLoaded(false)
              setStartOfPeriod(startOfPeriod.clone().add(diff, 'weeks'))
            }}
            defaultWeekStatusStyledText={
              isCurrentPayPeriod
                ? I18n.t('attendance.currentPayPeriod')
                : isPast
                  ? hasDataToExport
                    ? I18n.t('attendance.readyToExport')
                    : I18n.t('attendance.pastPeriod')
                  : I18n.t('attendance.upcoming')
            }
            weekStatusStyledProps={{
              $color: isCurrentPayPeriod
                ? theme.colors.blue
                : isPast && hasDataToExport
                  ? theme.colors.green
                  : ''
            }}
            title={
              locale === 'fr'
                ? startOfPeriod.format('D') +
                  ' - ' +
                  startOfPeriod
                    .clone()
                    .add(payrollLength - 1, 'days')
                    .format('D') +
                  ' ' +
                  startOfPeriod
                    .clone()
                    .add(payrollLength - 1, 'days')
                    .format('MMM')
                    .toLowerCase()
                : startOfPeriod.format('MMM D') +
                  ' - ' +
                  startOfPeriod
                    .clone()
                    .add(payrollLength - 1, 'days')
                    .format('D')
            }
            goToWeek={date => {
              setStartOfPeriod(dayjs(date, 'YYYY-MM-DD'))
              setisDataLoaded(false)
            }}
            type='attendance'
          />

          <SettingsButtonStyled
            isActiveIcon={showSettingsModal}
            onClick={() => setShowSettingsModal(!showSettingsModal)}
          >
            <SettingsIcon />
          </SettingsButtonStyled>

          <ConflictShiftButtonStyled
            className='common-button'
            ref={conflictPopoverElement}
            onClick={() => {
              setShowConflictPopover(false)
              setShowConflictModal(true)
            }}
            showConflictModal={showConflictModal}
            zIndex={showConflictPopover ? '100' : ''}
            boxShadow={
              showConflictPopover ? '0px 0px 10px -3px rgba(0, 0, 0, 0.4)' : ''
            }
            disabled={!numberOfConflicts}
          >
            <span>{numberOfConflicts}</span>
            {I18n.t('attendance.conflictingShifts')}
          </ConflictShiftButtonStyled>

          <Overlay
            show={showConflictPopover}
            onHide={() => setShowConflictPopover(false)}
            placement='bottom'
            rootClose
            target={() => conflictPopoverElement.current}
          >
            <PopoverStyled id='attendance-conflict-shifts-popover'>
              <PopoverConflictContainerStyled>
                <p>{I18n.t('attendance.youMustSovleConflictsBeforeExport')}</p>
              </PopoverConflictContainerStyled>
            </PopoverStyled>
          </Overlay>
        </HeaderBlockInfoStyled>

        {isOwner() && (
          <ExportButtonStyled
            className='common-button common-button_purple-filled'
            onClick={() => {
              if (
                overlappingsDates.size > 0 &&
                dayjs(endOfPeriodStr).isAfter('2024-12-15')
              ) {
                toast.error(
                  Array.from(overlappingsDates)
                    .filter(date =>
                      dayjs(date).isBetween(
                        startOfPeriod,
                        endOfPeriodStr,
                        'day',
                        '[]'
                      )
                    )
                    .map(date =>
                      formatDateToMonthStringAndDayNumber(dayjs(date), locale)
                    )
                    .join(', ') +
                    '\n' +
                    I18n.t('attendance.overlappingShifts'),
                  {
                    autoClose: 5000
                  }
                )

                return
              }
              if (numberOfConflicts > 0) {
                return setShowConflictPopover(true)
              }
              setShowExportModal(true)
            }}
            visible={!showExportModal}
          >
            <ExportIcon />
            {I18n.t('attendance.export')}
          </ExportButtonStyled>
        )}
      </HeaderBlockStyled>
      <TableStyled>
        <TableHeadStyled>
          <TableHeadCellStyled>
            <SelectContainerStyled
              ref={selectRef}
              onClick={() => {
                if (isAllowedToSeeSalary) {
                  setShowChangeView(!showChangeView)
                }
              }}
              disabled={!isAllowedToSeeSalary}
            >
              <CustomSelectStyled isOpened={showChangeView}>
                <p>
                  {isTimeSheetView
                    ? I18n.t('attendance.timesheet')
                    : I18n.t('attendance.laborCost')}
                </p>
                {isAllowedToSeeSalary && (
                  <img
                    src={showChangeView ? arrowClose : arrowOpen}
                    alt=''
                  />
                )}
              </CustomSelectStyled>
              {showChangeView && (
                <CustomSelectListStyled>
                  <CustomSelectItemStyled
                    onClick={() => setIsTimeSheetView(true)}
                    isSelected={isTimeSheetView}
                  >
                    <p>{I18n.t('attendance.timesheet')}</p>
                    {isTimeSheetView && (
                      <img
                        src={checkGreenIcon}
                        alt=''
                      />
                    )}
                  </CustomSelectItemStyled>
                  <CustomSelectItemStyled
                    onClick={() => setIsTimeSheetView(false)}
                    isSelected={!isTimeSheetView}
                  >
                    <p>{I18n.t('attendance.laborCost')}</p>
                    {!isTimeSheetView && (
                      <img
                        src={checkGreenIcon}
                        alt=''
                      />
                    )}
                  </CustomSelectItemStyled>
                </CustomSelectListStyled>
              )}
            </SelectContainerStyled>
          </TableHeadCellStyled>
          <TableHeadCellStyled>
            <SearchInputStyled
              type='text'
              className='common-input inputShadow'
              placeholder={I18n.t('schedule.searchEmployee')}
              value={searchTerm}
              onChange={e => setsearchTerm(e.target.value)}
            />
          </TableHeadCellStyled>
          <TableHeadCellStyled>
            <img
              src={isTimeSheetView ? clockIcon : dollarIcon}
              alt=''
            />
          </TableHeadCellStyled>

          {[1, 2, 3, 4, 5, 6, 7].map(item => {
            const dayMoment = startOfPeriod.clone().add(item - 1, 'days')
            const isToday =
              dayjs().format('ddd') === dayjs(dayMoment).format('ddd')

            return (
              <TableHeadCellStyled
                $isToday={isToday}
                key={item}
              >
                <p>{dayMoment.format('ddd')}</p>
              </TableHeadCellStyled>
            )
          })}
        </TableHeadStyled>
        <TableBodyStyled>
          <TablePositionsStyled>
            <ul>
              <TablePositionsItemStyled
                activePosition={!selectedPositionId}
                onClick={() => setselectedPositionId('')}
              >
                <div>{I18n.t('attendance.allPositions')}</div>
              </TablePositionsItemStyled>
              {map(jobs, (job, key) => (
                <TablePositionsItemStyled
                  key={key}
                  activePosition={selectedPositionId === key}
                  onClick={() => setselectedPositionId(key)}
                >
                  <div>{`${job.name} ${
                    job.archived ? `(${I18n.t('attendance.archived')})` : ''
                  }`}</div>
                </TablePositionsItemStyled>
              ))}
            </ul>
          </TablePositionsStyled>
          <TableContainerStyled>
            {filteredEmployees.map((employee, k) => {
              const employeeSummary = summary[employee.uid] || {}
              const {
                totalHours = 0,
                totalSalary = 0,
                totalCut = 0,
                totalDue = 0
              } = employeeSummary

              const employeeNumber =
                employee.payrollId || employee.customId || addZeros(k + 1)

              return (
                <TableRowStyled
                  isHighlighted={selectedEmployeeId === employee.uid}
                  key={employee.uid}
                >
                  <EmployeeInfoStyled
                    $isFullOvertime={
                      totalHours >= 80 && overtimeCalculationMode === 'biweekly'
                    }
                  >
                    <div>
                      <EmployeeInfoNumberStyled>
                        {employeeNumber}
                      </EmployeeInfoNumberStyled>
                      <EmployeeInfoTextStyled employeeName>
                        {employee.name} {employee.surname}{' '}
                      </EmployeeInfoTextStyled>
                    </div>
                    <div>
                      <EmployeeInfoTotalStyled>
                        {I18n.t('attendance.totalHours')}
                      </EmployeeInfoTotalStyled>
                      <EmployeeInfoTextStyled>
                        {totalHours} h
                      </EmployeeInfoTextStyled>
                      {isAllowedToSeeSalary && (
                        <>
                          <EmployeeInfoTotalStyled>
                            {I18n.t('attendance.totalRevenue')}
                          </EmployeeInfoTotalStyled>
                          <EmployeeInfoTextStyled>
                            {Math.round(
                              (totalSalary + totalDue + totalCut) * 100
                            ) / 100}
                            $
                          </EmployeeInfoTextStyled>
                        </>
                      )}
                    </div>
                  </EmployeeInfoStyled>

                  <TableWeekContainerStyled>
                    {(payrollLength === 14 ? [0, 1] : [0]).map(week => {
                      const startOfWeek = startOfPeriod
                        .clone()
                        .add(week, 'weeks')
                      const startOfWeekStr = startOfWeek.format('YYYY-MM-DD')
                      const weekDates = Array(7)
                        .fill(null)
                        .map((a, i) =>
                          startOfWeek
                            .clone()
                            .add(i, 'days')
                            .format('YYYY-MM-DD')
                        )
                      let weekHours =
                        summary[employee.uid].weeklyHours[startOfWeekStr] || 0
                      let weekSalary =
                        summary[employee.uid].weeklySalary[startOfWeekStr] || 0
                      // const weeklyTips =
                      //   summary[employee.uid].weeklyTips[startOfWeekStr] || 0
                      const weeklyCut =
                        summary[employee.uid].weeklyCut[startOfWeekStr] || 0
                      const weeklyTotalDue =
                        summary[employee.uid].weeklyTotalDue[startOfWeekStr] ||
                        0

                      let isWeekConfirmed = false

                      // isWeekConfirmed = Object.values(dayShifts).every(
                      //   (shift) => shift.isConfirmed
                      // )
                      // isWeekConfirmed && console.log(dayShifts);

                      return (
                        <TableWeekRowStyled key={week}>
                          <TableBodyCellStyled>
                            <EmployeeWeekStyled
                              isHighlighted={
                                selectedEmployeeId === employee.uid
                              }
                              isCurrentWeek={
                                weekDates[0] ===
                                dayjs().startOf('week').format('YYYY-MM-DD')
                              }
                            >
                              {payrollLength === 14
                                ? `${I18n.t('attendance.week')} ${week + 1}`
                                : ''}

                              <p>
                                {startOfWeek.format('MMM D')}-
                                {startOfWeek.clone().add(6, 'days').format('D')}
                              </p>
                            </EmployeeWeekStyled>
                          </TableBodyCellStyled>
                          {isTimeSheetView ? (
                            <TimesheetRow
                              isWeekConfirmed={isWeekConfirmed}
                              weekHours={weekHours}
                              weekDates={weekDates}
                              attendanceData={attendanceDataEnhanced}
                              employee={employee}
                              roundingTime={roundingTime}
                              allConflictingShifts={allConflictingShifts}
                              selectedEmployeeId={selectedEmployeeId}
                              selectedDate={selectedDate}
                              selectedShiftKey={selectedShiftKey}
                              setselectedEmployeeId={setselectedEmployeeId}
                              setselectedDate={setselectedDate}
                              setSelectedShiftKey={setSelectedShiftKey}
                              tableCellsRefs={tableCellsRefs}
                              shiftPopoverElement={shiftPopoverElement}
                              setPopoverType={setPopoverType}
                              overtimeCalculationMode={overtimeCalculationMode}
                              selectedPositionId={selectedPositionId}
                              maxHoursPerWeek={maxHoursPerWeek}
                            />
                          ) : (
                            <LaborCostRow
                              isWeekConfirmed={isWeekConfirmed}
                              weekHours={weekHours}
                              weekDates={weekDates}
                              attendanceData={attendanceDataEnhanced}
                              employee={employee}
                              allConflictingShifts={allConflictingShifts}
                              selectedEmployeeId={selectedEmployeeId}
                              selectedDate={selectedDate}
                              selectedShiftKey={selectedShiftKey}
                              setselectedEmployeeId={setselectedEmployeeId}
                              setselectedDate={setselectedDate}
                              setSelectedShiftKey={setSelectedShiftKey}
                              tableCellsRefs={tableCellsRefs}
                              shiftPopoverElement={shiftPopoverElement}
                              weekSalary={weekSalary}
                              positionSettings={positionSettings}
                              setPopoverType={setPopoverType}
                              weeklyCut={weeklyCut}
                              weeklyTotalDue={weeklyTotalDue}
                              overtimeCalculationMode={overtimeCalculationMode}
                              selectedPositionId={selectedPositionId}
                              maxHoursPerWeek={maxHoursPerWeek}
                            />
                          )}
                        </TableWeekRowStyled>
                      )
                    })}
                  </TableWeekContainerStyled>
                </TableRowStyled>
              )
            })}
          </TableContainerStyled>
        </TableBodyStyled>
      </TableStyled>
    </PageWrapperStyled>
  )
}

export default Attendance

const PopoverStyled = styled(Popover)`
  max-width: 16.5rem;
  margin-top: 1rem;

  border: unset;
  border-radius: 0.8rem;
  background-color: #fff;
  .arrow:before {
    display: none;
  }
`

const PopoverConflictContainerStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;

  padding: 1rem;

  p {
    color: #69748f;
    font-family: SFProBold;
    text-align: center;
  }
`

const PageWrapperStyled = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;

  max-width: 100%;
  height: 100%;
  padding: 1.5rem 1.5rem 0;
  width: 100%;
`

const HeaderBlockStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;

  width: 100%;
  margin-bottom: ${theme.rem(25)};
  padding-right: ${theme.rem(5)};
`
const HeaderBlockInfoStyled = styled.div`
  display: flex;
  align-items: center;
`

const SettingsButtonStyled = styled.button<{ isActiveIcon: boolean }>`
  display: flex;
  justify-content: center;
  align-items: center;

  height: ${theme.rem(50)};
  width: ${theme.rem(50)};
  margin: 0 ${theme.rem(40)};

  background-color: ${({ isActiveIcon }) =>
    isActiveIcon ? `${theme.colors.blue}!important` : '#dbe3eb'};
  border-radius: 50%;
  border: none;
  /* visibility: ${({ isActiveIcon }) => (isActiveIcon ? 'hidden' : null)}; */

  svg {
    height: ${theme.rem(26)};
    width: ${theme.rem(26)};
    fill: ${({ isActiveIcon }) => (isActiveIcon ? '#fff!important' : null)};
    stroke: ${({ isActiveIcon }) => (isActiveIcon ? '#fff!important' : null)};
  }

  &:hover {
    background-color: #e8edf1;
  }
`

const ConflictShiftButtonStyled = styled.button<{
  showConflictModal: boolean
  zIndex: string
  boxShadow: string
}>`
  display: ${({ showConflictModal }) => (showConflictModal ? 'none' : 'flex')};
  justify-content: flex-start;
  align-items: center;

  height: ${theme.rem(50)};
  min-width: ${theme.rem(220)};
  width: unset;
  padding: ${theme.rem(10)} ${theme.rem(20)} ${theme.rem(10)} ${theme.rem(10)};

  background-color: #f9b63a;
  border-radius: ${theme.rem(16)};
  border: none;

  color: #69748f;
  font-family: ${theme.fonts.bold};

  z-index: ${props => props.zIndex};
  box-shadow: ${props => props.boxShadow};

  span {
    display: flex;
    justify-content: center;
    align-items: center;

    min-height: ${theme.rem(28)};
    width: ${theme.rem(34)};
    margin-right: ${theme.rem(15)};

    background-color: #fdeacd;
    border-radius: ${theme.rem(9)};

    color: #f9b63a;
    font-size: ${theme.remFont(17)};
  }

  &:hover {
    background-color: #f9c36f;
    box-shadow: none;
  }

  &:disabled {
    color: #dbe3eb;
    background-color: #ecf1f5;

    span {
      background-color: #fff;
      color: #dbe3eb;
    }
  }
`

const ExportButtonStyled = styled.button<{ visible: boolean }>`
  display: ${({ visible }) => (visible ? 'flex' : 'none')};

  height: ${theme.rem(50)};
  min-width: ${theme.rem(150)};
  width: unset;
  padding: ${theme.rem(10)} ${theme.rem(16)};

  border: none;

  svg {
    height: ${theme.rem(24)};
    width: ${theme.rem(24)};
    margin-right: ${theme.rem(14)};
  }

  &:disabled {
    color: #dbe3eb !important;
    background-color: #ecf1f5 !important;

    svg path {
      fill: #d9e1e9;
    }
  }
`
const TableStyled = styled.div`
  display: flex;
  flex-direction: column;

  width: 100%;
  height: 100%;
  min-height: 0px;
`
const TableBodyStyled = styled.div`
  display: flex;
  flex-direction: row;

  flex: 1;
  min-height: 0px;
`

const TableHeadStyled = styled.div`
  display: grid;

  grid-column-start: 1;
  grid-column-end: 3;
  grid-template-columns: 17% 17% ${theme.rem(160)} repeat(7, 1fr);
  padding: 8px 0;
  margin-right: ${theme.rem(5)};

  background-color: #69748f;
  border-radius: ${theme.rem(20)} ${theme.rem(20)} ${theme.rem(20)} 0;

  @media (max-width: 1400px) {
    grid-template-columns: 16% 16.3% ${theme.rem(155)} repeat(7, 1fr);
  }
`
const TableHeadCellStyled = styled.div<{ $isToday?: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;

  padding: 0 ${theme.rem(20)};

  position: relative;

  font-size: ${theme.remFont(15)};
  font-family: ${theme.fonts.bold};

  &:not(:last-child) {
    border-right: 2px solid ${theme.colors.midGrey600};
  }

  p {
    font-size: inherit;
    font-family: inherit;
    color: ${({ $isToday }) => ($isToday ? theme.colors.blue : '#d9e1e9')};
  }

  &:after {
    content: ${({ $isToday }) => ($isToday ? "''" : 'unset')};
    width: ${theme.rem(11)};
    height: ${theme.rem(11)};

    position: absolute;
    right: 0.5rem;
    top: 0px;

    background-color: ${theme.colors.blue};
    border-radius: 50%;
  }

  img {
    width: ${theme.rem(20)};
    height: ${theme.rem(20)};
  }
`
const SearchInputStyled = styled.input`
  padding: 0 ${theme.rem(44)} 0 ${theme.rem(30)};
  max-width: 100%;
  width: 100%;

  background: url(${searchIcon}) ${theme.colors.midGrey600};
  background-position: right ${theme.rem(24)} center;
  background-size: ${theme.rem(16)};
  background-repeat: no-repeat;
  border: none;
  border-radius: ${theme.rem(14)};

  color: #fff;
  font-family: ${theme.fonts.bold};

  &:hover {
    box-shadow: 0px 0px 10px -3px rgba(0, 0, 0, 0.4);
  }

  &::placeholder {
    color: #d9e1e9;
    font-size: ${theme.rem(18)};
    font-family: ${theme.fonts.regular};
  }
`

const TablePositionsStyled = styled.div`
  display: flex;

  width: 17%;

  ul {
    width: 100%;
    margin: 0;
    padding: 0;

    background-color: #ecf1f5;
    list-style: none;
    overflow: auto;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  @media (max-width: 1400px) {
    width: 16%;
  }
`
const TablePositionsItemStyled = styled.li<{ activePosition: boolean }>`
  display: flex;
  align-items: center;
  justify-content: flex-start;

  width: 100%;
  padding: ${theme.rem(8)} 0 ${theme.rem(8)} ${theme.rem(8)};
  cursor: pointer;

  div {
    padding: ${theme.rem(14)} 4px ${theme.rem(14)} ${theme.rem(20)};
    width: 100%;

    position: relative;

    background-color: ${({ activePosition }) =>
      activePosition ? `${theme.colors.midGrey600}!important` : ''};
    border-radius: ${theme.rem(20)} 0 0 ${theme.rem(20)};

    color: ${({ activePosition }) =>
      activePosition ? '#fff' : theme.colors.midGrey600};
    font-size: ${theme.remFont(18)};
    font-family: ${theme.fonts.bold};
    text-align: left;
    line-height: normal;

    &:after {
      content: ${({ activePosition }) => (activePosition ? "''" : 'unset')};

      position: absolute;
      right: 0px;
      top: 0px;

      background-color: ${theme.colors.blue};
      width: 4px;
      height: 100%;
    }

    &:hover {
      background-color: #dbe3eb;
    }
  }
`

const TableContainerStyled = styled.div`
  display: flex;
  flex-direction: column;

  flex: 1;
  padding: ${theme.rem(20)} ${theme.rem(5)} 0 ${theme.rem(20)};
  overflow: auto;
  scroll-behavior: smooth;

  &::-webkit-scrollbar {
    display: none;
  }
`

const TableRowStyled = styled.div<{ isHighlighted: boolean }>`
  display: flex;

  width: 100%;
  margin-bottom: ${theme.rem(18)};
  position: relative;

  background-color: ${({ isHighlighted }) =>
    isHighlighted ? '#f9fcff' : '#ecf1f5'};
  border-radius: ${theme.rem(20)};
  box-shadow: ${({ isHighlighted }) =>
    isHighlighted ? '0px 0px 8px 3px rgb(172 190 208 / 50%)' : ''};
`

const EmployeeInfoTextStyled = styled.p<{ employeeName?: boolean }>`
  color: ${theme.colors.darkGrey};
  font-size: ${({ employeeName }) =>
    employeeName ? theme.remFont(16) : theme.remFont(14)};
  line-height: 1;
  font-family: ${theme.fonts.bold};
`

const EmployeeInfoStyled = styled.div<{ $isFullOvertime: boolean }>`
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  width: calc(20% - ${theme.rem(80)} - ${theme.rem(18)} + 5px);
  padding: ${theme.rem(20)};

  div {
    min-height: 25%;

    &:last-child {
      display: flex;
      flex-direction: column;

      height: 50%;

      span {
        margin-top: 3px;
      }
    }
  }

  ${EmployeeInfoTextStyled} {
    color: ${({ $isFullOvertime }) =>
      $isFullOvertime ? theme.colors.red : null};
  }

  @media (max-width: 1400px) {
    width: calc(19% - ${theme.rem(70)} - ${theme.rem(21)} + 5px);
    padding: ${theme.rem(20)} ${theme.rem(15)} ${theme.rem(20)} ${theme.rem(20)};
  }
`
const EmployeeInfoNumberStyled = styled.span`
  color: ${theme.colors.midGrey600};
  font-size: ${theme.rem(15)};
  font-family: ${theme.fonts.boldItalic};
  line-height: normal;
`

const EmployeeInfoTotalStyled = styled(EmployeeInfoNumberStyled)`
  font-size: ${theme.rem(13)};
`

const TableWeekRowStyled = styled.div`
  display: grid;

  grid-template-columns: ${theme.rem(80)} ${theme.rem(165)} repeat(7, 1fr);
  width: 100%;
  height: 100%;

  &:not(:first-child) {
    border-top: 2px solid #dbe3eb;
  }

  @media (max-width: 1400px) {
    grid-template-columns: ${theme.rem(75)} ${theme.rem(155)} repeat(7, 1fr);
  }
`

const TableBodyCellStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;

  padding: ${theme.rem(21)} ${theme.rem(6)} ${theme.rem(9)};

  position: relative;

  border-right: 2px dashed #dbe3eb;
  color: ${theme.colors.midGrey600};
  font-family: ${theme.fonts.bold};
  font-size: ${theme.remFont(17)};

  &:first-child {
    border-right: none;
    padding: ${theme.rem(15)} 0;
  }

  &:nth-child(9) {
    border-right: none;
  }

  &:nth-child(2) {
    padding: ${theme.rem(15)} ${theme.rem(6)} ${theme.rem(15)} 0;
  }

  @media (max-width: 1400px) {
    padding: ${theme.rem(21)} ${theme.rem(4)} ${theme.rem(9)};
  }
`
const EmployeeWeekStyled = styled.div<{
  isHighlighted: boolean
  isCurrentWeek: boolean
}>`
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;

  width: 100%;
  padding: ${theme.rem(6)} ${theme.rem(3)};
  margin-right: -3px;

  position: relative;

  background-color: ${({ isHighlighted }) =>
    isHighlighted ? '#ecf1f5' : '#ffffff'};
  border-radius: ${theme.rem(18)} 0 0 ${theme.rem(18)};

  color: ${theme.colors.darkGrey};
  font-size: ${theme.remFont(12)};
  line-height: normal;

  p {
    font-size: ${theme.remFont(11)};
    font-style: italic;
    color: ${theme.colors.midGrey600};
    text-align: center;
  }

  &:before {
    content: ${({ isCurrentWeek }) => (isCurrentWeek ? "''" : 'unset')};

    height: ${theme.rem(12)};
    width: ${theme.rem(12)};

    position: absolute;
    left: -${theme.rem(18)};

    background-color: ${theme.colors.blue};
    border-radius: 50%;
  }
`

const TableWeekContainerStyled = styled.div`
  display: flex;
  flex-direction: column;
  flex: 1;
`

const DarkOverlayStyled = styled.div<{ rowOverlay?: boolean }>`
  display: flex;

  width: 100%;
  height: 100%;

  position: absolute;
  top: 0;
  left: 0;
  z-index: 101;

  opacity: 0.7;
  background: #ecf1f5;
`

const SelectContainerStyled = styled.div<{ disabled: boolean }>`
  display: flex;

  flex: 1;
  height: 100%;

  position: relative;

  background-color: ${theme.colors.midGrey600};
  border-radius: ${theme.rem(14)};
  cursor: ${({ disabled }) => (disabled ? '' : 'pointer')};
`

const CustomSelectStyled = styled.div<{ isOpened: boolean }>`
  display: flex;
  align-items: center;
  justify-content: space-between;

  flex: 1;
  padding: 0 ${theme.rem(25)};

  border-radius: ${({ isOpened }) => (isOpened ? theme.rem(14) : null)};
  background: ${({ isOpened }) => (isOpened ? '#dbe3eb' : null)};
  z-index: ${({ isOpened }) => (isOpened ? 2 : null)};

  p {
    color: ${({ isOpened }) => (isOpened ? theme.colors.darkGrey : null)};
  }

  img {
    width: ${theme.rem(14)};
    height: ${theme.rem(14)};
  }
`

const CustomSelectListStyled = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;

  width: 100%;
  padding: ${theme.rem(20)} ${theme.rem(10)} 0;
  margin-top: ${theme.rem(-10)};

  position: absolute;
  top: 100%;
  z-index: 1;

  background-color: #fff;
  border-bottom-left-radius: ${theme.rem(14)};
  border-bottom-right-radius: ${theme.rem(14)};
  box-shadow: 0px 0 12px 0px rgb(172 190 208 / 50%);
  cursor: pointer;

  p {
    color: ${theme.colors.darkGrey};
  }
`
const CustomSelectItemStyled = styled.div<{ isSelected: boolean }>`
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;

  width: 100%;
  margin-bottom: ${theme.rem(10)};
  padding: ${theme.rem(4)} ${theme.rem(8)} ${theme.rem(4)} ${theme.rem(14)};

  border-radius: ${theme.rem(20)};
  background-color: ${({ isSelected }) =>
    isSelected ? '#cdf5e8!important' : '#fff'};

  font-size: ${theme.remFont(14)};

  &:hover {
    background-color: #ecf1f5;
  }

  img {
    width: ${theme.rem(18)};
    height: ${theme.rem(18)};
  }
`
