import dayjs, { Dayjs } from 'dayjs'

// TODO Ben
// a) shifts is not shifts but rather weeks data
// b) const shift = shifts[dayShiftsDate] is super misleading
// as it's not a shift but rather a day's data and may contain multiple shifts
export const splitShiftsPerWeek = (
  dayData: { [date: string]: any },
  middleDate: Dayjs
) => {
  const shiftsOfFirstWeek: { [date: string]: any } = {}
  const shiftsOfLastWeek: { [date: string]: any } = {}

  for (const dayShiftsDate in dayData) {
    const shift = dayData[dayShiftsDate]
    const date = dayjs(dayShiftsDate, 'YYYY-MM-DD')
    if (date < middleDate) {
      shiftsOfFirstWeek[dayShiftsDate] = shift
    } else {
      shiftsOfLastWeek[dayShiftsDate] = shift
    }
  }

  return { shiftsOfFirstWeek, shiftsOfLastWeek }
}