export const removeUnstartedShifts = (
  shifts: { [date: string]: any }
) => {
// remove shift where end or start is missing
  for (const dayShiftsDate in shifts) {
    const shift = shifts[dayShiftsDate]
    for (const employeeId in shift) {
      const employeeShifts = shift[employeeId]
      for (const shiftId in employeeShifts) {
        const shift = employeeShifts[shiftId]
        if (!shift.end) {
          delete employeeShifts[shiftId]
        }
      }
    }
  }
  return shifts
}
