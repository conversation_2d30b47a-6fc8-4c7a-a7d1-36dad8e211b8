import dayjs, { Dayjs } from 'dayjs'

export const getShiftsForThePeriod = (
  shiftsByDay: { [key: string]: any },
  startAsDate: string,
  isTwoWeekPayPeriod: boolean
) => {
  const endDate = dayjs(startAsDate).add(isTwoWeekPayPeriod ? 14 : 7, 'day')
  return Object.fromEntries(
    Object.keys(shiftsByDay)
      .filter(key => {
        const currentDate: Dayjs = dayjs(key)
        return currentDate.isBetween(startAsDate, endDate, 'day', '[)')
      })
      .map(key => [key, shiftsByDay[key]])
  )
}
