import React, { useCallback, useState } from 'react'
import Mo<PERSON> from 'react-bootstrap/Modal'
import { I18n } from 'react-redux-i18n'

import styled from 'styled-components'
import { theme } from 'styles/theme'

import ExportOptions from '../components/ExportOptions'
import { OutlineButton } from 'components/ui/OutlineButton'

import { EMPLOYEURD } from 'utils/constants'

import checkIcon from 'img/icons/checkPurpleFat.svg'
import closeIcon from 'img/icons/closePurple.svg'
import { ReactComponent as ExportIcon } from 'img/icons/exportIcon.svg'

const getExportOptions = (integrationType: string) => [
  {
    value: 'excel',
    title: I18n.t('attendance.excelSpreadsheet'),
    description: I18n.t('attendance.willDownloadExcel'),
    exportOptions: ['simplified', 'detailed']
  },
  {
    value: 'payroll',
    title:
      integrationType === EMPLOYEURD
        ? I18n.t('attendance.employeurd_export_format')
        : I18n.t('attendance.nethris_export_format'),
    description: I18n.t('attendance.will_be_download_as_a_text_document'),
    exportOptions: ['txt', 'xls']
  },
  {
    value: 'by-shift',
    title: I18n.t('attendance.by_shift_export_format'),
    description: I18n.t('attendance.willDownloadExcel')
  },
  {
    value: 'pay-evolution',
    title: I18n.t('attendance.payEvolutionExportFormat'),
    description: I18n.t('attendance.willDownloadExcel')
  }
]

type Props = {
  showModal: boolean
  onClose: () => void
  onClick: (
    option: string,
    selectedExportOption: 'simplified' | 'detailed' | 'xls' | 'txt'
  ) => void
  integrationType: string
}

function ExportPayPeriodModal({
  showModal,
  onClose,
  onClick,
  integrationType
}: Props) {
  const [option, setOption] = useState('excel')
  const [exportFormat, setExportFormat] = useState('simplified')

  const onExport = useCallback(
    () =>
      onClick(
        option,
        exportFormat as 'simplified' | 'detailed' | 'xls' | 'txt'
      ),
    [onClick, option, exportFormat]
  )

  const exportOptions = getExportOptions(integrationType)

  const updateExportOption = (option: string) => {
    setOption(option)
    switch (option) {
      case 'excel':
        setExportFormat('simplified')
        break
      case 'payroll':
        setExportFormat('txt')
        break
    }
  }

  return (
    <ModalStyled
      show={showModal}
      onHide={onClose}
    >
      <ModalHeaderStyled>
        <ExportIcon />
        {I18n.t('attendance.exportPayPeriod')}
        <button onClick={onClose}>
          <img
            src={closeIcon}
            alt=''
          />
        </button>
      </ModalHeaderStyled>

      <ModalBodyStyled>
        <SubtitleStyled>
          {I18n.t('attendance.howToExportPayPeriod')}
        </SubtitleStyled>
        {exportOptions.map(item => {
          const { value, title, description } = item
          const isActive = value === option
          return (
            <RowStyled
              key={value}
              onClick={() => updateExportOption(value)}
            >
              <CheckboxStyled checked={isActive}>
                <img
                  src={checkIcon}
                  alt=''
                />
              </CheckboxStyled>
              <RowBlockStyled>
                <RowLabelStyled selected={isActive}>{title}</RowLabelStyled>
                <RowTextStyled>{description}</RowTextStyled>
              </RowBlockStyled>
              {item.value === option && item.exportOptions && (
                <ExportOptions
                  key={title}
                  currentOption={exportFormat}
                  setExportOption={setExportFormat}
                  exportOptions={item.exportOptions}
                />
              )}
            </RowStyled>
          )
        })}
      </ModalBodyStyled>

      <ModalFooterStyled>
        <ButtonStyled
          color='purple'
          onClick={onExport}
        >
          <ExportIcontStyled />
          {I18n.t('attendance.export')}
        </ButtonStyled>
      </ModalFooterStyled>
    </ModalStyled>
  )
}

export default ExportPayPeriodModal

const ModalStyled = styled(Modal)`
  .modal-content {
    width: 60vw;
    max-width: 760px;
    margin: auto;

    @media (max-width: 1440px) {
      max-width: 680px;
    }
  }
`

const ModalHeaderStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;

  width: 100%;
  padding: ${theme.rem(25)};

  position: relative;

  background-color: #e5d8fa;
  border-radius: 0.8rem 0.8rem 0 0;

  color: #a984d2;
  font-family: ${theme.fonts.bold};
  font-size: ${theme.remFont(17)};

  svg {
    width: ${theme.rem(24)};
    height: ${theme.rem(24)};
    margin-right: ${theme.rem(10)};
  }

  button {
    display: flex;
    align-items: center;
    justify-content: center;

    height: ${theme.rem(30)};
    width: ${theme.rem(30)};
    padding: 0;

    position: absolute;
    right: ${theme.rem(15)};

    background-color: #f3e9ff;
    border: none;
    border-radius: 50%;

    img {
      height: ${theme.rem(13)};
      width: ${theme.rem(13)};
    }
  }
`

const ModalBodyStyled = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;

  padding: ${theme.rem(60)} ${theme.rem(15)} ${theme.rem(60)} 14%;
  width: 100%;
  gap: 0.4rem;
`

const SubtitleStyled = styled.p`
  margin-bottom: ${theme.rem(20)};

  font-size: ${theme.remFont(15)};
  font-family: ${theme.fonts.bold};
  color: ${theme.colors.midGrey600};
`

const CheckboxStyled = styled.div<{ checked: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;

  height: ${theme.rem(34)};
  width: ${theme.rem(34)};
  margin-right: ${theme.rem(15)};
  margin-bottom: 1rem;

  background-color: ${({ checked }) => (checked ? '#f3e9ff' : '#ecf1f5')};
  border-radius: 6px;

  img {
    display: ${({ checked }) => (checked ? 'flex' : 'none')};

    height: ${theme.rem(21)};
    width: ${theme.rem(21)};
  }
`

const RowBlockStyled = styled.div`
  display: flex;
  align-items: flex-start;
  flex-direction: column;

  margin-right: ${theme.rem(25)};
`

const RowLabelStyled = styled.p<{ selected: boolean }>`
  font-size: ${theme.remFont(15)};
  font-family: ${theme.fonts.bold};
  color: ${({ selected }) => (selected ? '#a984d2' : theme.colors.midGrey600)};
  line-height: normal;

  span {
    margin-left: ${theme.rem(10)};

    color: #f9b63a;
    font-family: ${theme.fonts.boldItalic};
    font-size: ${theme.remFont(13)};
  }
`
const RowStyled = styled.button`
  display: flex;
  align-items: center;

  position: relative;
  padding: 0;
  background-color: unset;
  border: none;

  &:hover {
    ${RowLabelStyled} {
      color: #a984d2;
    }
    ${CheckboxStyled} {
      background-color: #f3e9ff;
    }
  }
`
const RowTextStyled = styled.p`
  font-size: ${theme.remFont(13)};
  font-family: ${theme.fonts.bold};
  color: ${theme.colors.midGrey500};
`

const ModalFooterStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;

  margin: 0 ${theme.rem(20)};
  padding: ${theme.rem(24)};
  border-top: 1px solid #f3e9ff;
`

const ExportIcontStyled = styled(ExportIcon)`
  height: ${theme.rem(24)};
  width: ${theme.rem(24)};
`
const ButtonStyled = styled(OutlineButton)`
  border-color: transparent;
  padding-inline: 1rem;
  gap: 0.7rem;

  &:hover {
    color: #fff;
    background-color: #a984d2;

    svg path {
      fill: #fff;
    }
  }
`
