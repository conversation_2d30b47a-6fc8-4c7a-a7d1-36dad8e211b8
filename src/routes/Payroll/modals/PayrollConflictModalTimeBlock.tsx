import React, { useState } from 'react'

import styled from 'styled-components'
import { theme } from 'styles/theme'

import CustomTimePicker from 'components/ui/TimePicker'

import { minutesToMoment, momentToMinutes } from 'utils/time'

interface IProps {
  title: string
  initialValue: number
  roundingTime: number
  onApply: (e: number | null) => void
  isConfirmed: boolean
  isDifferentTime: boolean
}

const PayrollConflictModalTimeBlock = ({
  title,
  initialValue,
  roundingTime,
  onApply,
  isConfirmed,
  isDifferentTime
}: IProps) => {
  const initialTime = minutesToMoment(initialValue)
  const [value, setValue] = useState(initialTime)

  return (
    <div>
      <p>{title}</p>
      <InputWrapStyled
        $isDifferentTime={isDifferentTime}
        $notClocked={initialValue === null}
        $shiftConfirmed={isConfirmed}
      >
        <CustomTimePickerStyled
          minuteStep={roundingTime}
          onChange={e => {
            if (e) {
              setValue(e)
              onApply(momentToMinutes(e))
            }
          }}
          value={value}
          defaultValue={initialValue !== null ? initialTime : null}
          placeholder='-'
          hideArrow={isConfirmed}
        />
      </InputWrapStyled>
    </div>
  )
}

export default PayrollConflictModalTimeBlock

const CustomTimePickerStyled = styled(CustomTimePicker)``

interface InputWrapStyledProps {
  $isDifferentTime: boolean
  $notClocked: boolean
  $shiftConfirmed: boolean
}

const InputWrapStyled = styled.div<InputWrapStyledProps>`
  margin: 2px 0;

  ${CustomTimePickerStyled} {
    padding: 0.5rem 0.8rem;
    padding-right: ${({ $shiftConfirmed }) =>
      $shiftConfirmed ? null : '1.4rem'};

    background: ${props =>
      props.$notClocked
        ? '#fee6eb'
        : props.$isDifferentTime
          ? '#fdeacd'
          : props.$shiftConfirmed
            ? '#cdf5e8'
            : '#ecf1f5'};
    border-color: transparent;

    color: ${props =>
      props.$notClocked
        ? '#ec758a'
        : props.$isDifferentTime
          ? '#f9b63a'
          : theme.colors.darkGrey};

    ::placeholder {
      color: inherit;
    }

    & + svg {
      width: 0.6rem;
      height: 0.6rem;

      stroke: ${props =>
        props.$notClocked
          ? '#ec758a'
          : props.$isDifferentTime
            ? '#f9b63a'
            : theme.colors.darkGrey};
    }
  }
`
