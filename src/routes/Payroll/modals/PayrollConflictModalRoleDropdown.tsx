import React, { useEffect, useState } from 'react'
import useOnclickOutside from 'react-cool-onclickoutside'
import { I18n } from 'react-redux-i18n'

import styled from 'styled-components'
import { theme } from 'styles/theme'

import { OutlineButton } from 'components/ui/OutlineButton'

import ArrowDown from 'img/IconsHover/ArrowDown'
import { ReactComponent as CheckIcon } from 'img/icons/checkGreenFat.svg'

interface ISelectedOption {
  positionId: string
}

interface IProps {
  title: string
  initialValue: string
  roundingTime: number
  onApply: (e: ISelectedOption) => void
  employee: any
  jobs: any
  isConfirmed: boolean
}

interface TableItemCardClockButtonStyledProps {
  notClocked?: boolean
  isDifferentTime?: boolean
  shiftConfirmed?: boolean
}

interface TimePickerPopoverStyledProps {
  visible: boolean
}

interface DropdownOptionProps {
  id: string
  name: string
  isSelected: boolean
  positionId: string
}

interface ListItemLabelStyledProps {
  $isSelected: boolean
}

const PayrollConflictModalRoleDropdown = ({
  title,
  initialValue,
  employee,
  jobs,
  onApply,
  isConfirmed
}: IProps) => {
  const [isOpened, setIsOpened] = useState(false)
  const [value, setValue] = useState(initialValue)

  let options: DropdownOptionProps[] = []
  employee.positions.forEach(({ categoryId }: { categoryId: string }) => {
    if (jobs[categoryId] && !options.some(c => c.positionId === categoryId)) {
      options.push({
        id: categoryId,
        name: jobs[categoryId].name,
        isSelected: categoryId === value,
        positionId: categoryId
      })
    }
  })

  const selectedOption = options.find(option => option.isSelected)
  const selectedOptionName = selectedOption?.name

  useEffect(() => {
    if (!value && options.length === 1) {
      setValue(options[0].id)
      onApply({
        positionId: options[0].id
      })
    }
  }, [value, options, onApply])

  const ref = useOnclickOutside(
    () => {
      setIsOpened(false)
    },
    { disabled: !isOpened }
  )

  return (
    <div style={{ width: '100%' }}>
      <p>{title}</p>
      <TableItemCardClockButtonStyled
        notClocked={!value}
        onClick={() => !isConfirmed && setIsOpened(true)}
      >
        {selectedOptionName ? <span>{selectedOptionName}</span> : '\u00A0'}
        {!isConfirmed && <ArrowDown />}
      </TableItemCardClockButtonStyled>
      <CustomPopoverStyled
        visible={isOpened}
        ref={ref}
      >
        <CustomPopoverTitleStyled>
          <p>{I18n.t('attendance.role')}</p>
        </CustomPopoverTitleStyled>
        <ListStyled>
          {options.map(option => (
            <ListItemStyled key={option.id}>
              <ListItemLabelStyled $isSelected={option.isSelected}>
                <CustomCheckboxStyled>
                  <CheckIconStyled />
                  <HiddenCheckBoxStyled
                    type='checkbox'
                    checked={option.isSelected}
                    onChange={() => {
                      if (!option.isSelected) {
                        setValue(option.id)
                      }
                    }}
                  />
                </CustomCheckboxStyled>
                <PositionNameStyled>{option.name}</PositionNameStyled>
              </ListItemLabelStyled>
            </ListItemStyled>
          ))}
        </ListStyled>
        <TimePickerPopoverButtonsStyled>
          <OutlineButton
            color='red'
            onClick={() => {
              setValue(initialValue)
              setIsOpened(false)
            }}
          >
            {I18n.t('common.cancel')}
          </OutlineButton>
          <OutlineButton
            color='green'
            disabled={!selectedOption}
            onClick={() => {
              if (selectedOption) {
                setIsOpened(false)
                onApply({
                  positionId: selectedOption.positionId
                })
              }
            }}
          >
            {I18n.t('attendance.apply')}
          </OutlineButton>
        </TimePickerPopoverButtonsStyled>
      </CustomPopoverStyled>
    </div>
  )
}

export default PayrollConflictModalRoleDropdown

const TableItemCardButtonsStyled = styled.div`
  display: flex;
  justify-content: space-between;

  width: 100%;
  padding: ${theme.rem(12)};

  button {
    height: ${theme.rem(35)};
    width: calc(50% - ${theme.rem(5)});

    box-shadow: none;
    border-radius: 9px;

    font-size: ${theme.remFont(14)};
    line-height: inherit;
  }
`
const TimePickerPopoverButtonsStyled = styled(TableItemCardButtonsStyled)`
  position: absolute;
  bottom: 0;

  button {
    border-radius: 9px;
  }
`

const CustomPopoverStyled = styled.div<TimePickerPopoverStyledProps>`
  display: ${({ visible }) => (visible ? 'flex' : 'none')};
  flex-direction: column;
  align-items: center;

  width: calc(100% + ${theme.rem(16)});
  min-height: 10rem;
  max-height: 15rem;
  padding-top: 0.5rem;
  padding-bottom: 3rem;

  position: absolute;
  left: -0.4rem;
  top: 0;
  z-index: 1;

  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0px 0px 10px -3px rgba(0, 0, 0, 0.4);
`

const TimePickerPopoverTimeStyled = styled.button`
  display: flex;
  justify-content: center;
  align-items: center;

  margin: 2px 0;
  padding: ${theme.rem(6)} ${theme.rem(20)};

  position: relative;

  border-radius: 9px;
  border: 2px solid ${theme.colors.blue};
  background: #fff;

  color: ${theme.colors.blue};
  font-size: ${theme.remFont(15)};
  font-family: ${theme.fonts.heavy};

  span {
    margin-right: ${theme.rem(10)};
  }
  svg {
    width: ${theme.rem(11)};
    height: ${theme.rem(11)};

    position: absolute;
    right: ${theme.rem(10)};

    g {
      stroke: ${theme.colors.blue};
    }
  }
`

const TableItemCardClockButtonStyled = styled(
  TimePickerPopoverTimeStyled
)<TableItemCardClockButtonStyledProps>`
  width: 100%;
  padding: 0.3rem 1rem;

  background: ${props =>
    props.notClocked
      ? '#fee6eb'
      : props.isDifferentTime
        ? '#fdeacd'
        : props.shiftConfirmed
          ? '#cdf5e8'
          : '#ecf1f5'};
  border-color: transparent;

  color: ${props =>
    props.notClocked
      ? '#ec758a'
      : props.isDifferentTime
        ? '#f9b63a'
        : theme.colors.darkGrey};
  span {
    margin-right: unset;
    width: 100%;

    position: relative;
    white-space: nowrap;
    overflow: hidden;
    &:after {
      content: '';
      position: absolute;
      right: 0;
      height: 100%;
      width: 10px;

      background: linear-gradient(
        to right,
        rgba(255, 255, 255, 0),
        #ecf1f5 50%,
        #ecf1f5
      );
    }
  }
  svg g {
    stroke: ${props =>
      props.notClocked
        ? '#ec758a'
        : props.isDifferentTime
          ? '#f9b63a'
          : theme.colors.darkGrey};
  }
`

const CustomPopoverTitleStyled = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;

  p {
    margin: 0 ${theme.rem(10)};
  }
`

const ListStyled = styled.ul`
  display: flex;
  flex-direction: column;

  gap: 0.15rem;
  padding: 0.4rem;
  height: 100%;
  margin: 0;
  width: 100%;

  overflow: auto;
  list-style: none;
  ::-webkit-scrollbar {
    display: none;
  }
`

const CustomCheckboxStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;

  width: 1.2rem;
  height: 1.2rem;
  margin-right: 0.5rem;

  border-radius: 0.3rem;
  background-color: #8892a8;
`
const CheckIconStyled = styled(CheckIcon)`
  display: none;
  width: 0.8rem;
  height: 0.8rem;
`

const HiddenCheckBoxStyled = styled.input`
  visibility: hidden;
  margin: 0 !important;
  position: absolute;
`

const ListItemStyled = styled.li``

const PositionNameStyled = styled.div`
  display: flex;

  flex: 1;

  position: relative;
  white-space: nowrap;
  overflow: hidden;
  &:after {
    content: '';
    position: absolute;
    right: 0;
    height: 100%;
    width: 10px;

    background: linear-gradient(
      to right,
      rgba(255, 255, 255, 0),
      #fff 50%,
      #fff
    );
  }
`

const ListItemLabelStyled = styled.label<ListItemLabelStyledProps>`
  display: flex;
  align-items: center;

  width: 100%;
  padding: 0.4rem 1rem;
  margin: 0;

  background-color: ${({ $isSelected }) => ($isSelected ? '#c9e8e0' : null)};
  border-radius: 0.6rem;
  border: none;

  color: ${theme.colors.darkGrey};
  font-size: ${theme.remFont(15)};
  font-family: ${theme.fonts.heavy};
  cursor: pointer;
  &:hover {
    background-color: #c9e8e0;
    ${CheckIconStyled} {
      display: initial;
    }
    ${PositionNameStyled} {
      &:after {
        background: linear-gradient(
          to right,
          rgba(255, 255, 255, 0),
          #c9e8e0 50%,
          #c9e8e0
        );
      }
    }
  }
  ${CustomCheckboxStyled} {
    background-color: ${({ $isSelected }) =>
      $isSelected ? theme.colors.green : null};
  }
  ${CheckIconStyled} {
    display: ${({ $isSelected }) => ($isSelected ? 'initial' : null)};
    fill: ${({ $isSelected }) =>
      $isSelected ? '#c9e8e0' : theme.colors.white};
  }
  ${PositionNameStyled} {
    &:after {
      background: ${({ $isSelected }) =>
        $isSelected
          ? 'linear-gradient(to right,rgba(255, 255, 255, 0),#c9e8e0 50%,#c9e8e0);'
          : null};
    }
  }
`
