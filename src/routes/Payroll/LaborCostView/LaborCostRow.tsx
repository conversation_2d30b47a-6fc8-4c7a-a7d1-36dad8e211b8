import React from 'react'
import { I18n } from 'react-redux-i18n'

import dayjs from 'dayjs'
import _ from 'lodash'
import styled from 'styled-components'
import { theme } from 'styles/theme'

import { SALARY_TYPE_YEARLY } from 'utils/constants'

import type {
  AttendanceSettings,
  IAllConflictingAttendanceShifts,
  IAttendanceEnhancedShifts,
  OvertimeCalculationMode
} from 'types/attendance'
import type { IEmployee } from 'types/employee'

import checkGreenIcon from 'img/icons/checkGreenFillIcon.svg'
import clockFilledBlue from 'img/icons/clockFilledBlue.svg'
import clockFilledOrange from 'img/icons/clockFilledOrange.svg'
import closeFilledIcon from 'img/icons/closeFilledIcon.svg'
import exclamationOrange from 'img/icons/exclamationOrange.svg'
import exclamationRed from 'img/icons/exclamationRed.svg'
import triangleIcon from 'img/icons/triangleDarkIcon.svg'

type Props = {
  employee: IEmployee
  attendanceData: IAttendanceEnhancedShifts
  weekDates: string[]
  weekSalary: number
  isWeekConfirmed: boolean
  allConflictingShifts: IAllConflictingAttendanceShifts
  setselectedEmployeeId: (id: string) => void
  selectedEmployeeId: string
  selectedDate: string
  selectedShiftKey: string
  positionSettings: AttendanceSettings['positionSettings']
  weeklyCut: number
  weekHours: number
  weeklyTotalDue: number
  setselectedDate: (date: string) => void
  setSelectedShiftKey: (shiftKey: string) => void
  tableCellsRefs: {
    current: {
      [key: string]: HTMLDivElement | null
    }
  }
  shiftPopoverElement: {
    current: HTMLDivElement | null
  }
  setPopoverType: (type: string) => void
  overtimeCalculationMode: OvertimeCalculationMode

  selectedPositionId: string
  maxHoursPerWeek: number
}

const LaborCostRow = ({
  employee,
  attendanceData,
  weekDates,
  weekSalary,
  isWeekConfirmed,
  allConflictingShifts,
  setselectedEmployeeId,
  selectedEmployeeId,
  selectedDate,
  setselectedDate,
  tableCellsRefs,
  shiftPopoverElement,
  selectedShiftKey,
  positionSettings,
  setSelectedShiftKey,
  setPopoverType,
  weeklyCut,
  weekHours,
  weeklyTotalDue,
  overtimeCalculationMode,

  selectedPositionId,
  maxHoursPerWeek
}: Props) => (
  <>
    <TableBodyCellStyled>
      <EmpoyeeStatsStyled
        weekConfirmed={isWeekConfirmed}
        overtime={
          weekHours > maxHoursPerWeek &&
          overtimeCalculationMode !== 'not-calculated'
        }
      >
        <EmpoyeeStatsRowStyled>
          <p>{I18n.t('attendance.salary')}</p>
          <span>{Math.round(weekSalary * 100) / 100}$</span>
        </EmpoyeeStatsRowStyled>
        {/* I18n.t('attendance.splitTipShort') */}
        {Boolean(weeklyTotalDue) && (
          <EmpoyeeStatsRowStyled>
            <p>{I18n.t('attendance.totalDue')}</p>
            <span>{weeklyTotalDue}$</span>
          </EmpoyeeStatsRowStyled>
        )}
        {Boolean(weeklyCut) && (
          <EmpoyeeStatsRowStyled>
            <p>{I18n.t('attendance.cut')}</p>
            <span>{weeklyCut}$</span>
          </EmpoyeeStatsRowStyled>
        )}
      </EmpoyeeStatsStyled>
    </TableBodyCellStyled>

    {weekDates.map(date => {
      const dayMoment = dayjs(date, 'YYYY-MM-DD')
      const isToday = dayMoment.isSame(dayjs(), 'day')
      const dayShifts =
        (attendanceData[date] && attendanceData[date][employee.uid]) || {}

      const isEmpty = _.isEmpty(dayShifts)

      if (isEmpty) {
        return (
          <TableBodyCellStyled
            // broken ref
            ref={el => (tableCellsRefs.current[`${employee.uid}-${date}`] = el)}
            key={date}
          >
            <TableBodyWeekDayStyled
              isToday={isToday}
              disabled={false}
            >
              {isToday ? I18n.t('attendance.today') : dayMoment.format('D')}
            </TableBodyWeekDayStyled>
            <TableBodyCellEmptyStyled disabled={true}>
              +
            </TableBodyCellEmptyStyled>
          </TableBodyCellStyled>
        )
      }

      return (
        <TableBodyCellStyled key={date}>
          <TableBodyWeekDayStyled isToday={isToday}>
            {isToday ? I18n.t('attendance.today') : dayMoment.format('D')}
          </TableBodyWeekDayStyled>

          {_.map(dayShifts, (shift, shiftKey) => {
            const isAutoShift =
              shift.type === SALARY_TYPE_YEARLY && shift.start === undefined
            const { salary, totalDue, cutToReceive, isWorking } = shift

            const {
              isClockInDifferent = false,
              isClockOutDiffrent = false,
              neverClockedOut = false
            } = allConflictingShifts?.[date]?.[employee.uid]?.[shiftKey] || {}

            const isConflictingShift =
              isClockInDifferent ||
              isClockOutDiffrent ||
              !shift.positionId ||
              (neverClockedOut && !isWorking)

            const salaryType =
              positionSettings[shift.positionId]?.salaryType || 'wage'

            const isConfirmed = Boolean(
              !isWorking && (shift.isConfirmed || !isConflictingShift)
            )

            const totalStyles: {
              isWorking?: boolean
              notClocked?: boolean
              isDifferentTime?: boolean
            } = {}
            if (!isConfirmed) {
              if (isWorking) {
                totalStyles.isWorking = true
              } else if (neverClockedOut) {
                totalStyles.notClocked = true
              } else if (isConflictingShift) {
                totalStyles.isDifferentTime = true
              }
            }

            const isShiftSelected =
              selectedEmployeeId === employee.uid &&
              selectedDate === date &&
              selectedShiftKey === shiftKey

            const total =
              Math.round((salary + -+totalDue + cutToReceive) * 100) / 100

            let totalCut = 0

            if (salaryType === 'wage_cut') {
              totalCut = cutToReceive
            }

            totalCut = Math.round(totalCut * 100) / 100

            return (
              <TableCellShiftStyled
                key={shiftKey}
                // ref={shiftPopoverElement}
                shiftConfirmed={Boolean(isShiftSelected) && isConfirmed}
                isConflicting={
                  Boolean(isShiftSelected) && !isConfirmed && isConflictingShift
                }
                ref={el =>
                  (tableCellsRefs.current[
                    `${employee.uid}-${date}-${shiftKey}`
                  ] = el)
                }
                onClick={() => {
                  shiftPopoverElement.current =
                    tableCellsRefs.current[
                      `${employee.uid}-${date}-${shiftKey}`
                    ]
                  setselectedEmployeeId(employee.uid)
                  setselectedDate(date)
                  setSelectedShiftKey(shiftKey)
                  setPopoverType('labor-cost')
                }}
                $withOpacity={
                  isAutoShift ||
                  (selectedPositionId !== shift.positionId &&
                    selectedPositionId !== '')
                }
              >
                {isConfirmed ? null : isWorking ? (
                  // Time clocked-in is different by +/= 15 minutes
                  <ClockIconStyled orange={isClockInDifferent} />
                ) : neverClockedOut ? (
                  <CloseIconStyled />
                ) : isClockInDifferent ? (
                  <ExclamationIconStyled orange={true} />
                ) : null}

                <TableCellShiftTimeStyled>
                  {isWorking ? (
                    <TableCellShiftWorkingStyled>
                      {I18n.t('attendance.currentlyWorking')}
                    </TableCellShiftWorkingStyled>
                  ) : (
                    <>
                      <TableCellShiftInStyled
                        isDifferentTime={!isConfirmed && isClockInDifferent}
                        notClocked={neverClockedOut} // Employee never clocked-out
                      >
                        <span>{I18n.t('attendance.salary')}</span>
                        <p>{salary ? `${salary}$` : '-'}</p>
                      </TableCellShiftInStyled>

                      {Boolean(salaryType !== 'wage' || totalCut) &&
                        !isAutoShift && (
                          <TimeTriangleIconStyled
                            src={triangleIcon}
                            alt=''
                          />
                        )}
                      {Boolean(salaryType === 'wage_cut' || totalCut) &&
                        !isAutoShift && (
                          <TableCellShiftOutStyled
                            isDifferentTime={false}
                            isWorking={isWorking} // Employee is currently working
                            notClocked={!isConfirmed && neverClockedOut} // Employee never clocked-out
                          >
                            <>
                              <span>{I18n.t('attendance.cut')}</span>
                              <p>{totalCut ? `${totalCut}$` : '-'}</p>
                            </>
                          </TableCellShiftOutStyled>
                        )}
                      {salaryType === 'wage_tips' && !isAutoShift && (
                        <TableCellShiftOutStyled
                          isDifferentTime={false}
                          isWorking={isWorking} // Employee is currently working
                          notClocked={!isConfirmed && neverClockedOut} // Employee never clocked-out
                        >
                          <>
                            <span>{I18n.t('attendance.due')}</span>
                            <p>{totalDue ? `${-totalDue}$` : '-'}</p>
                          </>
                        </TableCellShiftOutStyled>
                      )}
                    </>
                  )}
                </TableCellShiftTimeStyled>
                <TableCellShiftTotalStyled {...totalStyles}>
                  <span>{I18n.t('attendance.total')}</span>
                  <p>{total ? `${total}$` : '-'}</p>
                </TableCellShiftTotalStyled>
              </TableCellShiftStyled>
            )
          })}
        </TableBodyCellStyled>
      )
    })}
  </>
)

export default LaborCostRow

const TableBodyCellStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;

  padding: ${theme.rem(21)} ${theme.rem(4)} ${theme.rem(8)};

  position: relative;

  border-right: 2px dashed #dbe3eb;

  color: ${theme.colors.midGrey600};
  font-family: ${theme.fonts.bold};
  font-size: ${theme.remFont(17)};

  &:first-child {
    border-right: none;
    padding: ${theme.rem(15)} 0;
  }
  &:nth-child(9) {
    border-right: none;
  }
  &:nth-child(2) {
    padding: ${theme.rem(15)} ${theme.rem(6)} ${theme.rem(15)} 0;
  }

  @media (max-width: 1400px) {
    padding: ${theme.rem(21)} ${theme.rem(4)} ${theme.rem(9)};
  }
`
const TableBodyCellEmptyStyled = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;

  width: 100%;
  height: 100%;
  max-height: ${theme.rem(82)};
  background-color: unset;
  border: unset;

  border-radius: 12px;

  pointer-events: ${({ disabled }) => (disabled ? 'none' : null)};
  opacity: ${({ disabled }) => (disabled ? '0.4' : null)};

  &:hover {
    box-shadow: ${({ disabled }) =>
      disabled ? theme.colors.blue : '0px 0px 10px -3px rgba(0, 0, 0, 0.4)'};
  }
`
const TableBodyWeekDayStyled = styled.span<{
  isToday: boolean
  disabled?: boolean
}>`
  position: absolute;
  top: 0;
  left: ${theme.rem(6)};

  font-size: ${theme.remFont(12)};
  color: ${({ isToday }) => (isToday ? theme.colors.blue : 'inherit')};
  font-style: italic;

  opacity: ${({ disabled }) => (disabled ? '0.4' : null)};
`

const EmpoyeeStatsStyled = styled.div<{
  weekConfirmed: boolean
  overtime: boolean
}>`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  padding: 0 ${theme.rem(6)};
  height: ${theme.rem(60)};
  width: 100%;

  position: relative;

  background-color: ${props =>
    props.weekConfirmed
      ? '#cdf5e8'
      : props.overtime
        ? theme.colors.lightRed
        : '#dbe3eb'};
  border-radius: ${theme.rem(18)};

  color: ${theme.colors.darkGrey};
  line-height: normal;
  font-size: ${theme.remFont(13)};
  cursor: default;

  &:after {
    content: ${({ weekConfirmed }) => (weekConfirmed ? "''" : 'unset')};
    width: ${theme.rem(22)};
    height: ${theme.rem(22)};

    position: absolute;
    right: ${theme.rem(-4)};
    top: ${theme.rem(-8)};

    background: url(${checkGreenIcon});
  }
`
const EmpoyeeStatsRowStyled = styled.div`
  display: flex;
  align-items: center;

  gap: ${theme.rem(10)};
  width: 100%;

  p {
    display: flex;
    justify-content: flex-end;

    flex: 0.5;

    font-size: ${theme.remFont(11)};
    font-style: italic;
    color: ${theme.colors.midGrey600};
    /* white-space: nowrap; */
    text-align: center;
    line-height: 1;
  }
  span {
    display: flex;
    justify-content: flex-start;

    flex: 0.6;
  }
`

const TableCellShiftStyled = styled.div<{
  $withOpacity: boolean
  shiftConfirmed: boolean
  isConflicting: boolean
}>`
  display: flex;
  flex-direction: column;

  width: 100%;
  border-radius: ${theme.rem(18)};
  background-color: #fff;

  position: relative;
  cursor: pointer;

  box-shadow: ${props =>
    props.shiftConfirmed
      ? `0px 0px 3px 3px ${theme.colors.green}!important`
      : props.isConflicting
        ? '0px 0px 3px 3px #ec758a!important'
        : ''};

  &:hover {
    box-shadow: 0px 0px 10px -3px rgba(0, 0, 0, 0.4);
  }
  &:nth-child(3),
  &:nth-child(4),
  &:nth-child(5) {
    margin-top: ${theme.rem(10)};
  }
  &:after {
    content: ${({ $withOpacity }) => ($withOpacity ? "''" : null)};
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: rgba(236, 241, 245, 0.4);
    border-radius: ${theme.rem(18)};
    pointer-events: none;
  }
`

const TableCellShiftTotalStyled = styled.div<{
  isWorking?: boolean
  notClocked?: boolean
  isDifferentTime?: boolean
}>`
  display: flex;
  align-items: center;
  justify-content: center;

  width: 100%;
  padding: ${theme.rem(4)} ${theme.rem(6)};

  background-color: ${props =>
    props.isWorking
      ? '#aee9ff'
      : props.isDifferentTime
        ? '#fddbaa'
        : props.notClocked
          ? '#fac7d5'
          : '#cdf5e8'};
  border-radius: 0 0 ${theme.rem(18)} ${theme.rem(18)};

  span {
    margin-right: ${theme.rem(12)};

    font-size: ${theme.remFont(12)};
    font-style: italic;
    color: ${theme.colors.midGrey600};
  }
  p {
    color: ${({ isWorking, notClocked }) =>
      isWorking
        ? theme.colors.blue
        : notClocked
          ? '#ec758a'
          : theme.colors.darkGrey};
    font-size: ${theme.remFont(13)};
  }

  @media (max-width: 1400px) {
    span {
      font-size: ${theme.remFont(11)};
    }
    p {
      font-size: ${theme.remFont(12)};
    }
  }
`
const TableCellShiftInStyled = styled.div<{
  isDifferentTime: boolean
  notClocked: boolean
}>`
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;

  flex: 1;
  padding: ${theme.rem(3)} ${theme.rem(3)};

  border-radius: ${theme.rem(14)};
  background-color: #fff;

  line-height: normal;

  span {
    font-size: ${theme.remFont(12)};
    font-style: italic;
    color: ${theme.colors.midGrey600};
    white-space: nowrap;
  }
  p {
    color: ${props =>
      props.notClocked
        ? '#ec758a' //'#fee6eb'
        : props.isDifferentTime
          ? '#f9b63a'
          : theme.colors.darkGrey};
    font-size: ${theme.remFont(13)};
  }

  @media (max-width: 1400px) {
    padding: ${theme.rem(3)} ${theme.rem(4)};

    span {
      font-size: ${theme.remFont(11)};
    }
    p {
      font-size: ${theme.remFont(12)};
    }
  }
`

const TableCellShiftWorkingStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;

  min-height: ${theme.rem(41)};

  color: ${theme.colors.blue};
  font-size: ${theme.remFont(12)};
  font-style: italic;

  @media (max-width: 1400px) {
    font-size: ${theme.remFont(11)};
  }
`

const TableCellShiftTimeStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;

  width: 100%;
  padding: ${theme.rem(5)};

  @media (max-width: 1400px) {
    padding: ${theme.rem(4)};
  }
`
const TableCellShiftOutStyled = styled(TableCellShiftInStyled)<{
  isWorking: boolean
  notClocked: boolean
  isDifferentTime: boolean
}>`
  background-color: ${props =>
    props.isWorking
      ? '#d5f5ff'
      : props.notClocked
        ? '#fee6eb'
        : props.isDifferentTime
          ? '#fdeacd'
          : '#fff'};
  p {
    color: ${props =>
      props.isWorking
        ? theme.colors.blue
        : props.notClocked
          ? '#ec758a'
          : props.isDifferentTime
            ? '#f9b63a'
            : theme.colors.darkGrey};
  }
`
const TimeTriangleIconStyled = styled.img`
  display: flex;
  align-self: flex-end;

  width: ${theme.rem(11)};
  height: ${theme.rem(11)};
  margin-bottom: ${theme.rem(8)};
  margin-left: ${theme.rem(-2)};

  @media (max-width: 1400px) {
    width: ${theme.rem(11)};
    height: ${theme.rem(11)};
  }
`

const ExclamationIconStyled = styled.div<{
  orange: boolean
  small?: boolean
  top?: number
  zIndex?: boolean
}>`
  display: flex;
  justify-content: center;
  align-items: center;

  width: ${({ small }) => (small ? theme.rem(20) : theme.rem(23))};
  height: ${({ small }) => (small ? theme.rem(20) : theme.rem(23))};

  position: absolute;
  right: -${theme.rem(6)};
  top: ${({ top }) => (top ? top : theme.rem(-8))};
  z-index: ${({ zIndex }) => (zIndex ? '1' : '')};

  background-image: url(${({ orange }) =>
    orange ? exclamationOrange : exclamationRed});
  background-color: ${({ orange }) => (orange ? '#f9b63a' : '#ec758a')};
  background-size: ${({ small }) => (small ? theme.rem(1.5) : theme.rem(2.5))};
  background-position: center;
  background-repeat: no-repeat;
  border-radius: 50%;
  z-index: 1;
`

const ClockIconStyled = styled.div<{ orange?: boolean }>`
  display: flex;
  justify-content: center;
  align-items: center;

  width: ${theme.rem(23)};
  height: ${theme.rem(23)};

  position: absolute;
  right: -${theme.rem(6)};
  top: -${theme.rem(8)};

  background-image: url(${({ orange }) =>
    orange ? clockFilledOrange : clockFilledBlue});
  background-color: ${({ orange }) => (orange ? '#f9b63a' : theme.colors.blue)};
  background-size: ${theme.rem(15)};
  background-position: center;
  background-repeat: no-repeat;
  border-radius: 50%;
  z-index: 1;
`

const CloseIconStyled = styled(ClockIconStyled)<{ zIndex?: boolean }>`
  background-image: url(${closeFilledIcon});
  background-color: unset;
  background-size: 100%;

  z-index: ${({ zIndex }) => (zIndex ? '1' : '')};
`
