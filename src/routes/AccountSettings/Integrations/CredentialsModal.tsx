import React, { useMemo, useState } from 'react'
import Modal from 'react-bootstrap/Modal'
import { I18n } from 'react-redux-i18n'

import styled from 'styled-components'
import { theme } from 'styles/theme'

import {
  BlockStyled,
  InputStyled,
  InputWrapStyled,
  LabelStyled,
  RowStyled
} from './components/AddPosModalStyle'
import ToggleShowPassword from './components/ToggleShowPassword'

import { ReactComponent as BriefcaseIcon } from 'img/icons/briefCase.svg'

interface CredentialsModalProps {
  show: boolean
  onClose: () => void
  currentIntegration: IntegrationData
  currentIntegrationName: string
}

const CredentialsModal = ({
  show,
  onClose,
  currentIntegration,
  currentIntegrationName
}: CredentialsModalProps) => {
  // Memoize the configuration to prevent recalculation on every render
  const config = useMemo(() => {
    const configFn = INTEGRATION_CONFIGS[currentIntegrationName]
    return configFn ? configFn(currentIntegration) : { inputs: [] }
  }, [currentIntegrationName, currentIntegration])

  const { inputs, associationValue } = config
  return (
    <ModalStyled
      show={show}
      onHide={onClose}
    >
      <ModalHeaderStyled>
        {I18n.t('accountsettings.account_connected_to')}
        <span>{currentIntegrationName}</span>
      </ModalHeaderStyled>

      <BodyStyled>
        <RowStyled hasExtraColumn={inputs.length === 3}>
          {inputs.map((input, index) => (
            <CredentialInput
              key={`${input.label}-${index}`}
              {...input}
            />
          ))}
        </RowStyled>

        {associationValue && (
          <InputBlockStyled>
            <LabelStyled>{I18n.t('accountsettings.association')}</LabelStyled>
            <InputWrapStyled>
              <InputIconStyled
                type='text'
                value={associationValue}
                readOnly
                aria-label={I18n.t('accountsettings.association')}
              />
              <BriefcaseIconStyled aria-hidden='true' />
            </InputWrapStyled>
          </InputBlockStyled>
        )}
      </BodyStyled>
    </ModalStyled>
  )
}

// Type definitions for better type safety
interface IntegrationData {
  locationID?: string
  apiKey?: string
  serial?: string
  email?: string
  password?: string
  businessCode?: string
  userCode?: string
  userPassword?: string
  CO_NUMBER?: string
  posApiKey?: string
  posSerial?: string
}

interface InputField {
  label: string
  value: string | undefined
  type?: 'text' | 'password'
}

interface IntegrationConfig {
  inputs: InputField[]
  associationValue?: string
}

// Configuration object to reduce switch statement complexity
const INTEGRATION_CONFIGS: Record<
  string,
  (integration: IntegrationData) => IntegrationConfig
> = {
  myr: integration => ({
    inputs: [],
    associationValue: integration.locationID
  }),

  cluster: integration => ({
    inputs: [
      {
        label: I18n.t('accountsettings.cluster_pos_api_key'),
        value: integration.posApiKey
      },
      {
        label: I18n.t('accountsettings.cluster_pos_serial'),
        value: integration.posSerial
      }
    ]
  }),

  veloce: integration => ({
    inputs: [
      { label: I18n.t('common.email'), value: integration.email },
      {
        label: I18n.t('common.password'),
        value: integration.password,
        type: 'password'
      }
    ],
    associationValue: integration.locationID
  }),

  nethris: integration => ({
    inputs: [
      {
        label: I18n.t('accountsettings.business_code'),
        value: integration.businessCode
      },
      {
        label: I18n.t('accountsettings.user_code'),
        value: integration.userCode
      },
      {
        label: I18n.t('accountsettings.user_password'),
        value: integration.userPassword,
        type: 'password'
      }
    ],
    associationValue: integration.CO_NUMBER
  }),

  employeurD: integration => ({
    inputs: [
      {
        label: I18n.t('accountsettings.business_code'),
        value: integration.businessCode
      },
      {
        label: I18n.t('accountsettings.user_code'),
        value: integration.userCode
      },
      {
        label: I18n.t('accountsettings.user_password'),
        value: integration.userPassword,
        type: 'password'
      }
    ],
    associationValue: integration.CO_NUMBER
  }),

  libro: integration => ({
    inputs: [],
    associationValue: integration.locationID
  })
}

const CredentialInput = ({ label, value, type = 'text' }: InputField) => {
  const [showPassword, setShowPassword] = useState(false)

  return (
    <BlockStyled>
      <LabelStyled>{label}</LabelStyled>
      <InputWrapStyled>
        <InputStyled
          type={
            type === 'password' ? (showPassword ? 'text' : 'password') : type
          }
          value={value || ''}
          readOnly
          aria-label={label}
          $hasButton={type === 'password'}
        />
        {type === 'password' && (
          <ShowPasswordButtonWrapperStyled>
            <ToggleShowPassword
              showPassword={showPassword}
              toggleShowPassword={() => setShowPassword(!showPassword)}
            />
          </ShowPasswordButtonWrapperStyled>
        )}
      </InputWrapStyled>
    </BlockStyled>
  )
}

export default CredentialsModal

const ModalStyled = styled(Modal)`
  .modal-content {
    width: 50vw;
  }
`

const ModalHeaderStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: flex-start;

  gap: 0.2rem;
  width: 100%;
  padding: 1.5rem 2.5rem 0rem;

  position: relative;

  border-radius: 0.8rem 0.8rem 0 0;
  background-color: ${theme.colorsNew.white};

  color: ${theme.colorsNew.darkGrey700};
  font-family: ${theme.fonts.bold};
  font-size: 1.1rem;

  span {
    &::first-letter {
      text-transform: uppercase;
    }
  }
`

const BodyStyled = styled.div`
  display: flex;
  align-items: flex-start;
  flex-direction: column;

  gap: 1rem;
  width: 100%;
  padding: 1.5rem 2.5rem;

  text-align: left;
`

const InputBlockStyled = styled(BlockStyled)`
  width: 66%;
`

const BriefcaseIconStyled = styled(BriefcaseIcon)`
  width: 2rem;
  height: 1.4rem;
  position: absolute;
  left: 0.3rem;
  z-index: 9999;
`
const InputIconStyled = styled(InputStyled)`
  width: 100%;
  padding-left: 2.6rem;
`

const ShowPasswordButtonWrapperStyled = styled.button`
  padding: 0;

  position: absolute;
  right: 0.5rem;
  z-index: 1;

  border: none;
  background-color: transparent;
`
