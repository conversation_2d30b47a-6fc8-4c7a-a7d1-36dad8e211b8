import React, { createContext, useContext, useState, useEffect, useMemo, ReactNode } from 'react'
import { useSelector } from 'react-redux'
import dayjs, { Dayjs } from 'dayjs'
import { RootState } from 'store/reducers'
import { getDefaultStartOfPeriod, getClosestPayPeriodStart, getPayrollPeriodDates } from 'utils/payroll/payrollPeriodUtils'
import { AttendanceSettings } from 'types/attendance'

// Period state interface
export interface PeriodState {
  // Base period configuration (from attendance settings)
  basePeriodStart: Dayjs
  payrollLength: number
  payrollFrequency: 'weekly' | 'biweekly'
  
  // Current period (default - based on current date)
  defaultPeriodOffset: number
  
  // User-selected period (can be different from default)
  userPeriodOffset: number | null
  
  // Computed current period (user selection or default)
  currentPeriodOffset: number
  currentPeriodStart: Dayjs
  currentPeriodEnd: Dayjs
  
  // Period display information
  periodDisplay: {
    startStr: string
    endStr: string
    isCurrentPeriod: boolean
    isPastPeriod: boolean
    isFuturePeriod: boolean
  }
  
  // Period dates for database queries
  startOfPeriodStr: string
  endOfPeriodStr: string
}

// Period actions interface
export interface PeriodActions {
  // Update user-selected period
  setUserPeriodOffset: (offset: number | null) => void
  
  // Navigation actions
  goToPreviousPeriod: () => void
  goToNextPeriod: () => void
  
  // Reset to default period
  resetToDefaultPeriod: () => void
  
  // Update attendance settings (triggers recalculation)
  updateAttendanceSettings: (settings: Partial<AttendanceSettings>) => void
}

// Combined context interface
export interface PeriodContextType {
  period: PeriodState
  actions: PeriodActions
  isLoading: boolean
}

// Create context
const PeriodContext = createContext<PeriodContextType | null>(null)

// Provider props
interface PeriodProviderProps {
  children: ReactNode
  initialAttendanceSettings?: Partial<AttendanceSettings>
}

// Period Provider Component
export const PeriodProvider: React.FC<PeriodProviderProps> = ({ 
  children, 
  initialAttendanceSettings 
}) => {
  const currentCompany = useSelector((state: RootState) =>
    state.companies.find(company => company.key === state.currentCompanyId) ||
    { payrollStartingDay: 'Monday' } as any
  )
  const isLocaleFr = useSelector((state: RootState) => state.i18n.locale) === 'fr'
  
  // Attendance settings state
  const [attendanceSettings, setAttendanceSettings] = useState<AttendanceSettings>({
    roundingTime: 5,
    positionSettings: {},
    startingWeek: getDefaultStartOfPeriod(currentCompany.payrollStartingDay),
    tipsDeclaration: '',
    tipsDistribution: 'daily',
    tipsExport: '',
    cutExport: '',
    overtimeExport: '',
    declarationExport: '',
    cutDistribution: 'daily' as AttendanceSettings['cutDistribution'],
    cutPercentage: '',
    cutGroups: [],
    cutRoundingValue: '',
    cutRoundingType: 'down' as AttendanceSettings['cutRoundingType'],
    payrollFrequency: 'biweekly',
    overtimeCalculationMode: 'weekly',
    ...initialAttendanceSettings
  })

  // Update attendance settings when external settings change
  const updateAttendanceSettings = (newSettings: Partial<AttendanceSettings>) => {
    setAttendanceSettings(prev => ({
      ...prev,
      ...newSettings
    }))
  }
  
  // User-selected period offset (null means use default)
  const [userPeriodOffset, setUserPeriodOffset] = useState<number | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  // Calculate base period configuration
  const basePeriodStart = useMemo(() => {
    return getClosestPayPeriodStart(
      attendanceSettings.startingWeek,
      currentCompany.payrollStartingDay || 'Monday',
      attendanceSettings.payrollFrequency || 'biweekly'
    )
  }, [attendanceSettings.startingWeek, currentCompany.payrollStartingDay, attendanceSettings.payrollFrequency])

  // Reset user period offset when settings change to avoid confusion
  useEffect(() => {
    setUserPeriodOffset(null)
  }, [attendanceSettings.startingWeek, attendanceSettings.payrollFrequency, currentCompany.payrollStartingDay])

  const payrollLength = attendanceSettings.payrollFrequency === 'weekly' ? 7 : 14

  // Calculate default period offset (always 0 for current period)
  const defaultPeriodOffset = 0

  // Calculate current period offset (user selection or default)
  const currentPeriodOffset = userPeriodOffset !== null ? userPeriodOffset : defaultPeriodOffset

  // Calculate current period dates
  const currentPeriodStart = useMemo(() => {
    return basePeriodStart.clone().add(currentPeriodOffset * payrollLength, 'days')
  }, [basePeriodStart, currentPeriodOffset, payrollLength])

  const currentPeriodEnd = useMemo(() => {
    return currentPeriodStart.clone().add(payrollLength - 1, 'days')
  }, [currentPeriodStart, payrollLength])

  // Calculate period display information
  const periodDisplay = useMemo(() => {
    return {
      startStr: currentPeriodStart.format(isLocaleFr ? 'DD' : 'MMM DD'),
      endStr: currentPeriodEnd.format(isLocaleFr ? 'DD MMM' : 'MMM DD'),
      isCurrentPeriod: currentPeriodOffset === 0,
      isPastPeriod: currentPeriodOffset < 0,
      isFuturePeriod: currentPeriodOffset > 0
    }
  }, [currentPeriodStart, currentPeriodEnd, currentPeriodOffset, isLocaleFr])

  // Calculate period dates for database queries
  const { startOfPeriodStr, endOfPeriodStr } = useMemo(() => {
    return getPayrollPeriodDates(currentPeriodStart, attendanceSettings.payrollFrequency || 'biweekly')
  }, [currentPeriodStart, attendanceSettings.payrollFrequency])

  // Period state object
  const periodState: PeriodState = {
    basePeriodStart,
    payrollLength,
    payrollFrequency: attendanceSettings.payrollFrequency || 'biweekly',
    defaultPeriodOffset,
    userPeriodOffset,
    currentPeriodOffset,
    currentPeriodStart,
    currentPeriodEnd,
    periodDisplay,
    startOfPeriodStr,
    endOfPeriodStr
  }

  // Period actions
  const periodActions: PeriodActions = {
    setUserPeriodOffset: (offset: number | null) => {
      setUserPeriodOffset(offset)
    },
    
    goToPreviousPeriod: () => {
      const newOffset = currentPeriodOffset - 1
      setUserPeriodOffset(newOffset)
    },
    
    goToNextPeriod: () => {
      const newOffset = currentPeriodOffset + 1
      setUserPeriodOffset(newOffset)
    },
    
    resetToDefaultPeriod: () => {
      setUserPeriodOffset(null)
    },
    
    updateAttendanceSettings: (settings: Partial<AttendanceSettings>) => {
      setAttendanceSettings(prev => ({ ...prev, ...settings }))
    }
  }

  // Context value
  const contextValue: PeriodContextType = {
    period: periodState,
    actions: periodActions,
    isLoading
  }

  // Initialize loading state
  useEffect(() => {
    setIsLoading(false)
  }, [])

  return (
    <PeriodContext.Provider value={contextValue}>
      {children}
    </PeriodContext.Provider>
  )
}

// Custom hook to use period context
export const usePeriod = (): PeriodContextType => {
  const context = useContext(PeriodContext)
  if (!context) {
    throw new Error('usePeriod must be used within a PeriodProvider')
  }
  return context
}

// Export context for advanced usage
export { PeriodContext }
