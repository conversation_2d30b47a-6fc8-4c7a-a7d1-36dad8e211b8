import { IEmployeeAvailability, IEmployeePosition } from './employee'

export type Applicant = {
  address: string
  appliedAt: number
  availabilities: IEmployeeAvailability
  bornDate: number
  companyId: string
  direct: boolean
  email: string
  emergencyContact: string
  emergencyPhone: string
  emergencyPhoneCode: string
  name: string
  priority?: {
    [key: string]: {
      [key: string]: number
    }
  }
  positions?: IEmployeePosition[]
  status?: string
  surname: string
  userId: string
  uid?: string
  hiring?: number
  avatar?: string
}
