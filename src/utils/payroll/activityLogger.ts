import { database } from '../../index'

import { AttendanceShift } from 'types/attendance'

export type ActivityAction =
  | 'start-end'
  | 'role'
  | 'start'
  | 'end'
  | 'delete-shift'
  | 'add-shift'
  | 'break'
  | 'unplanned'
  | 'add-break'
  | 'delete-break'

export type ActivityStatus = 'modified' | 'added' | 'deleted'

export interface ActivityLogEntry {
  timestamp: number
  authorId: string
  authorName: string
  authorAvatar?: string
  action: ActivityAction
  oldValue?: string | number
  newValue?: string | number
  status: ActivityStatus
  shiftKey?: string
}

/**
 * Logs an activity to the Firebase database
 */
export const logActivity = async (
  companyId: string,
  date: string,
  employeeId: string,
  activity: Omit<ActivityLogEntry, 'timestamp'>
): Promise<void> => {
  try {
    const activityRef = database.ref(
      `AttendanceActivityLog/${companyId}/${date}/${employeeId}`
    )

    await activityRef.push({
      ...activity,
      timestamp: Date.now()
    })
  } catch (error) {
    console.error('Failed to log activity:', error)
  }
}

/**
 * Logs shift changes by comparing old and new shift data
 */
export const logShiftChanges = async (
  companyId: string,
  date: string,
  employeeId: string,
  oldShift: AttendanceShift | null,
  newShift: AttendanceShift,
  shiftKey: string,
  authorId: string,
  authorName: string,
  authorAvatar?: string
): Promise<void> => {
  const activities: Omit<ActivityLogEntry, 'timestamp'>[] = []

  // Helper function to format time for display
  const formatTime = (minutes: number | undefined): string => {
    if (minutes === undefined || minutes === null) return 'N/A'
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`
  }

  if (!oldShift) {
    // New shift added
    activities.push({
      authorId,
      authorName,
      authorAvatar,
      action: 'add-shift',
      status: 'added',
      shiftKey,
      newValue: `${formatTime(newShift.start)} - ${formatTime(newShift.end)}`
    })
  } else {
    // Check for start time changes
    if (oldShift.start !== newShift.start) {
      activities.push({
        authorId,
        authorName,
        authorAvatar,
        action: 'start',
        status: 'modified',
        shiftKey,
        oldValue: formatTime(oldShift.start),
        newValue: formatTime(newShift.start)
      })
    }

    // Check for end time changes
    if (oldShift.end !== newShift.end) {
      activities.push({
        authorId,
        authorName,
        authorAvatar,
        action: 'end',
        status: 'modified',
        shiftKey,
        oldValue: formatTime(oldShift.end),
        newValue: formatTime(newShift.end)
      })
    }

    // Check for both start and end time changes (combined)
    if (oldShift.start !== newShift.start && oldShift.end !== newShift.end) {
      activities.push({
        authorId,
        authorName,
        authorAvatar,
        action: 'start-end',
        status: 'modified',
        shiftKey,
        oldValue: `${formatTime(oldShift.start)} - ${formatTime(oldShift.end)}`,
        newValue: `${formatTime(newShift.start)} - ${formatTime(newShift.end)}`
      })
    }

    // Check for position/role changes
    if (oldShift.positionId !== newShift.positionId) {
      activities.push({
        authorId,
        authorName,
        authorAvatar,
        action: 'role',
        status: 'modified',
        shiftKey,
        oldValue: oldShift.positionId || 'Unassigned',
        newValue: newShift.positionId || 'Unassigned'
      })
    }
  }

  // Log all activities
  for (const activity of activities) {
    await logActivity(companyId, date, employeeId, activity)
  }
}

/**
 * Logs shift deletion
 */
export const logShiftDeletion = async (
  companyId: string,
  date: string,
  employeeId: string,
  deletedShift: AttendanceShift,
  shiftKey: string,
  authorId: string,
  authorName: string,
  authorAvatar?: string
): Promise<void> => {
  const formatTime = (minutes: number | undefined): string => {
    if (minutes === undefined || minutes === null) return 'N/A'
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`
  }

  await logActivity(companyId, date, employeeId, {
    authorId,
    authorName,
    authorAvatar,
    action: 'delete-shift',
    status: 'deleted',
    shiftKey,
    oldValue: `${formatTime(deletedShift.start)} - ${formatTime(deletedShift.end)}`
  })
}

/**
 * Gets the current user information for logging
 * This should be called from a component that has access to user context
 */
export const getCurrentUserInfo = (
  user: any,
  currentEmployee: any
): { id: string; name: string; avatar?: string } => {
  if (currentEmployee) {
    return {
      id: user?.uid || 'unknown-user',
      name:
        `${currentEmployee.name || ''} ${currentEmployee.surname || ''}`.trim() ||
        'Unknown User',
      avatar: currentEmployee.avatar
    }
  }

  if (user) {
    return {
      id: user.uid,
      name:
        `${user.name || ''} ${user.surname || ''}`.trim() ||
        user.email ||
        'Unknown User',
      avatar: user.avatar
    }
  }

  return {
    id: 'unknown-user',
    name: 'Unknown User',
    avatar: undefined
  }
}
