import dayjs from 'dayjs'
import { ALL_DAYS } from 'utils/constants'

/**
 * Gets the default start of period based on the first day of the week
 * @param firstDayOfWeek - The first day of the week (default: 'Monday')
 * @returns The default start of period in YYYY-MM-DD format
 */
export const getDefaultStartOfPeriod = (firstDayOfWeek = 'Monday') => {
  const dayNumber = ALL_DAYS[firstDayOfWeek]
  // Start from 2023-02-06 and go back to the specified day of the week
  let date = dayjs('2023-02-06').day(dayNumber)

  // If the calculated date is in the future, go back one week
  if (date.isAfter(dayjs('2023-02-06'))) {
    date = date.subtract(1, 'week')
  }

  return date.format('YYYY-MM-DD')
}

/**
 * Gets the closest payroll period start date
 * @param startingWeek - The starting week in YYYY-MM-DD format
 * @param payrollStartingDay - The day of the week payroll starts (default: 'Monday')
 * @param payrollFrequency - The payroll frequency ('weekly' or 'biweekly')
 * @param beforeDate - Optional date to calculate before (default: current date)
 * @returns The closest payroll period start date as a dayjs object
 */
export const getClosestPayPeriodStart = (
  startingWeek: string,
  payrollStartingDay: string = 'Monday',
  payrollFrequency: string,
  beforeDate?: string
) => {
  // Parse the starting week into a dayjs object
  let startDate = dayjs(startingWeek, 'YYYY-MM-DD')

  // Current date for reference
  let currentDate = beforeDate ? dayjs(beforeDate, 'YYYY-MM-DD') : dayjs()

  // Find the first occurrence of `payrollStartingDay` on or after `startingWeek`
  let targetDay = startDate.isoWeekday(ALL_DAYS[payrollStartingDay])
  if (startDate.isAfter(targetDay)) {
    targetDay = targetDay.add(1, 'week')
  }

  // Determine the number of weeks between the current date and the target start day
  const frequency = payrollFrequency === 'weekly' ? 1 : 2 // 1 week for weekly, 2 weeks for biweekly
  while (targetDay.isBefore(currentDate, 'day')) {
    targetDay = targetDay.add(frequency, 'week') // move ahead based on the payroll frequency
  }

  // If our calculation goes past the current date, step back to find the previous target date
  if (targetDay.isAfter(currentDate, 'day')) {
    targetDay = targetDay.subtract(frequency, 'week')
  }

  return targetDay
}

/**
 * Calculates the payroll period dates
 * @param startOfPeriod - The start of the payroll period as a dayjs object
 * @param payrollFrequency - The payroll frequency ('weekly' or 'biweekly')
 * @returns An object containing period information
 */
export const getPayrollPeriodDates = (
  startOfPeriod: dayjs.Dayjs,
  payrollFrequency: string
) => {
  const payrollLength = payrollFrequency === 'weekly' ? 7 : 14
  
  const week1 = startOfPeriod.format('YYYY-MM-DD')
  const startOfPeriodStr = startOfPeriod.format('YYYY-MM-DD')
  const endOfPeriodStr = startOfPeriod
    .clone()
    .add(payrollLength - 1, 'days')
    .format('YYYY-MM-DD')
  
  let week2: string | null = null
  if (payrollFrequency === 'biweekly') {
    week2 = startOfPeriod.clone().add(1, 'week').format('YYYY-MM-DD')
  }

  const firstDayOfWeek = startOfPeriod.clone().locale('en').format('dddd')

  return {
    week1,
    week2,
    startOfPeriodStr,
    endOfPeriodStr,
    firstDayOfWeek,
    payrollLength
  }
}
