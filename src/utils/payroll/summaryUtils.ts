import { forEach, round } from 'lodash'
import { IEmployee } from 'types/employee'
import { IAttendanceEnhancedShifts } from 'types/attendance'

export interface PayrollSummary {
  [employeeId: string]: {
    totalHours: number
    totalSalary: number
    positionHours: { [positionId: string]: number }
    totalCut: number
    totalDue: number
    weeklyHours: { [week: string]: number }
    weeklyCut: { [week: string]: number }
    weeklyTips: { [week: string]: number }
    weeklySalary: { [week: string]: number }
    weeklyTotalDue: { [week: string]: number }
    weekKeys: string[]
  }
}

/**
 * Calculates payroll summary for all employees
 * @param employeesToDisplay - Array of employees to include in summary
 * @param attendanceDataEnhanced - Enhanced attendance data with calculations
 * @param week1 - First week date
 * @param week2 - Second week date (for biweekly payroll)
 * @param payrollFrequency - Payroll frequency ('weekly' or 'biweekly')
 * @param positionSettings - Position settings for salary types
 * @returns Payroll summary object
 */
export const calculatePayrollSummary = (
  employeesToDisplay: IEmployee[],
  attendanceDataEnhanced: IAttendanceEnhancedShifts,
  week1: string,
  week2: string | null,
  payrollFrequency: string,
  positionSettings: any
): PayrollSummary => {
  const result: PayrollSummary = {}

  employeesToDisplay.forEach(employee => {
    result[employee.uid] = {
      totalHours: 0,
      totalSalary: 0,
      positionHours: {},
      totalCut: 0,
      totalDue: 0,
      weeklyHours: { [week1]: 0 },
      weeklyCut: { [week1]: 0 },
      weeklyTips: { [week1]: 0 },
      weeklySalary: { [week1]: 0 },
      weeklyTotalDue: { [week1]: 0 },
      weekKeys: [week1]
    }

    if (payrollFrequency === 'biweekly' && week2) {
      result[employee.uid].weeklyHours[week2] = 0
      result[employee.uid].weeklyCut[week2] = 0
      result[employee.uid].weeklyTips[week2] = 0
      result[employee.uid].weeklySalary[week2] = 0
      result[employee.uid].weeklyTotalDue[week2] = 0
      result[employee.uid].weekKeys.push(week2)
    }

    forEach(attendanceDataEnhanced, (dayEmployees, date) => {
      const employeeShifts = dayEmployees[employee.uid]
      if (!employeeShifts) return

      forEach(employeeShifts, shift => {
        const {
          shiftLengthHours,
          salary,
          totalDue,
          cutToPay,
          cutToReceive,
          tips,
          positionId
        } = shift

        if (!result[employee.uid].positionHours[positionId]) {
          result[employee.uid].positionHours[positionId] = 0
        }

        const startOfWeek = !week2 || date < week2 ? week1 : week2
        const { salaryType = 'wage' } = positionSettings[positionId] || {}

        result[employee.uid].totalHours += shiftLengthHours
        result[employee.uid].positionHours[positionId] += shiftLengthHours

        const salaryPerShift = salary

        result[employee.uid].totalSalary += salaryPerShift
        result[employee.uid].weeklyHours[startOfWeek] += shiftLengthHours

        if (salaryType === 'wage_cut') {
          result[employee.uid].totalCut += cutToReceive
          result[employee.uid].weeklyCut[startOfWeek] += cutToReceive
        }

        // if tips => bonus included in total due
        // otherwise include it in total salary
        if (salaryType === 'wage_tips') {
          result[employee.uid].weeklyTips[startOfWeek] += tips - cutToPay
          result[employee.uid].weeklyTotalDue[startOfWeek] += -totalDue
          result[employee.uid].totalDue += -totalDue
        }

        result[employee.uid].weeklySalary[startOfWeek] += salaryPerShift
      })
    })

    // Round all values
    result[employee.uid].totalHours = round(result[employee.uid].totalHours, 2)
    result[employee.uid].totalSalary = round(result[employee.uid].totalSalary, 2)
    result[employee.uid].weeklyHours[week1] = round(result[employee.uid].weeklyHours[week1], 2)
    result[employee.uid].weeklyCut[week1] = round(result[employee.uid].weeklyCut[week1], 2)
    result[employee.uid].weeklyTips[week1] = round(result[employee.uid].weeklyTips[week1], 2)
    result[employee.uid].weeklySalary[week1] = round(result[employee.uid].weeklySalary[week1], 2)
    result[employee.uid].weeklyTotalDue[week1] = round(result[employee.uid].weeklyTotalDue[week1], 2)

    if (week2) {
      result[employee.uid].weeklyHours[week2] = round(result[employee.uid].weeklyHours[week2], 2)
      result[employee.uid].weeklyCut[week2] = round(result[employee.uid].weeklyCut[week2], 2)
      result[employee.uid].weeklyTips[week2] = round(result[employee.uid].weeklyTips[week2], 2)
      result[employee.uid].weeklySalary[week2] = round(result[employee.uid].weeklySalary[week2], 2)
      result[employee.uid].weeklyTotalDue[week2] = round(result[employee.uid].weeklyTotalDue[week2], 2)
    }
  })

  return result
}

/**
 * Calculates total hours for an employee across all positions
 * @param positionHours - Hours worked per position
 * @returns Total hours worked
 */
export const calculateTotalHours = (positionHours: { [positionId: string]: number }): number => {
  return round(
    Object.values(positionHours).reduce((total, hours) => total + hours, 0),
    2
  )
}

/**
 * Calculates weekly totals for a specific week
 * @param summary - Payroll summary data
 * @param week - Week to calculate totals for
 * @returns Object with weekly totals
 */
export const calculateWeeklyTotals = (
  summary: PayrollSummary,
  week: string
) => {
  const totals = {
    totalHours: 0,
    totalSalary: 0,
    totalCut: 0,
    totalTips: 0,
    totalDue: 0
  }

  Object.values(summary).forEach(employeeSummary => {
    totals.totalHours += employeeSummary.weeklyHours[week] || 0
    totals.totalSalary += employeeSummary.weeklySalary[week] || 0
    totals.totalCut += employeeSummary.weeklyCut[week] || 0
    totals.totalTips += employeeSummary.weeklyTips[week] || 0
    totals.totalDue += employeeSummary.weeklyTotalDue[week] || 0
  })

  return {
    totalHours: round(totals.totalHours, 2),
    totalSalary: round(totals.totalSalary, 2),
    totalCut: round(totals.totalCut, 2),
    totalTips: round(totals.totalTips, 2),
    totalDue: round(totals.totalDue, 2)
  }
}

/**
 * Gets employee summary for a specific employee
 * @param summary - Payroll summary data
 * @param employeeId - Employee ID to get summary for
 * @returns Employee summary or null if not found
 */
export const getEmployeeSummary = (
  summary: PayrollSummary,
  employeeId: string
) => {
  return summary[employeeId] || null
}
