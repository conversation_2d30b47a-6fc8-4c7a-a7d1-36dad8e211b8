// Payroll utility functions - centralized exports
// This file provides a single entry point for all payroll-related utilities

// Period calculations
export {
  getDefaultStartOfPeriod,
  getClosestPayPeriodStart,
  getPayrollPeriodDates
} from './payrollPeriodUtils'

// Employee utilities
export {
  getEmployeesToDisplay,
  sortEmployees,
  checkHasEmployeurDNumbers,
  getEmployeeKeyForSales,
  mergeEmployeesWithIntegrations
} from './employeeUtils'

// Sales and cut calculations
export {
  calculateWeeklyDataAndDailyCuts,
  calculateCutAmount,
  calculateTipsAndCut,
  calculateCutPercentageToShare
} from './salesAndCutUtils'

// Shift conflict detection
export {
  analyzeShiftConflicts,
  calculateExpectedShiftLength,
  isEmployeeWorking,
  findMatchingScheduledShift,
  createConflictShiftData
} from './shiftConflictUtils'

// Overtime calculations
export {
  calculateOvertimeAndSalary,
  calculatePastShiftsDuration,
  isShiftInSecondWeek
} from './overtimeUtils'

// Summary calculations
export {
  calculatePayrollSummary,
  calculateTotalHours,
  calculateWeeklyTotals,
  getEmployeeSummary,
  type PayrollSummary
} from './summaryUtils'

// Re-export types that might be needed by consumers
export type { 
  WeeklyData, 
  DailyCuts, 
  OvertimeCalculationMode 
} from 'types/attendance'
