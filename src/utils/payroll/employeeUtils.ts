import { AttendanceShifts } from 'types/attendance'
import { IPositions } from 'types/company'
import { IEmployee } from 'types/employee'

/**
 * Filters employees to display in payroll based on their positions and shifts
 * @param employees - Array of employees with integrations
 * @param attendanceData - Attendance data for the period
 * @returns Filtered array of employees to display
 */
export const getEmployeesToDisplay = (
  employees: IEmployee[],
  attendanceData: AttendanceShifts,
  jobs?: IPositions
): IEmployee[] => {
  return employees
    .filter(
      employee =>
        // Match exact logic from EmployeeRolesList: just check positions exist
        employee.positions && employee.positions.length > 0
    )
    .map(employee => {
      if (employee.lastPositions && employee.lastPositions.length > 0) {
        return {
          ...employee,
          positions: employee.lastPositions
        }
      }
      return employee
    })
}

/**
 * Sorts employees based on the specified order
 * @param employees - Array of employees to sort
 * @param employeeOrder - The order to sort by ('surname', 'employee-number', etc.)
 * @param hasEmployeurDNumbers - Whether employees have payroll IDs
 * @returns Sorted array of employees
 */
export const sortEmployees = (
  employees: IEmployee[],
  employeeOrder: string,
  hasEmployeurDNumbers: boolean
): IEmployee[] => {
  const keyToSortBy =
    employeeOrder === 'employee-number'
      ? hasEmployeurDNumbers
        ? 'payrollId'
        : 'customId'
      : employeeOrder

  return employees.sort((a, b) => {
    if (employeeOrder === 'employee-number') {
      const aNumber = +(a[keyToSortBy as keyof IEmployee] || Infinity)
      const bNumber = +(b[keyToSortBy as keyof IEmployee] || Infinity)

      return aNumber > bNumber ? 1 : -1
    }

    const aValue = a[keyToSortBy as keyof IEmployee]
    const bValue = b[keyToSortBy as keyof IEmployee]
    return aValue! > bValue! ? 1 : -1
  })
}

/**
 * Checks if employees have payroll integration numbers
 * @param employees - Array of employees to check
 * @param hasEmployerDIntegration - Whether EmployerD integration exists
 * @param hasNethrisIntegration - Whether Nethris integration exists
 * @returns Boolean indicating if employees have payroll numbers
 */
export const checkHasEmployeurDNumbers = (
  employees: IEmployee[],
  hasEmployerDIntegration: boolean,
  hasNethrisIntegration: boolean
): boolean => {
  return (
    employees.some(employee => employee.payrollId !== undefined) &&
    (hasEmployerDIntegration || hasNethrisIntegration)
  )
}

/**
 * Gets the employee key to use for sales integration
 * @param salesProvider - The sales provider ('veloce', 'maitre_d', etc.)
 * @returns The employee property key to use for matching
 */
export const getEmployeeKeyForSales = (salesProvider: string): string => {
  switch (salesProvider) {
    case 'veloce':
      return 'veloceId'
    case 'maitre_d':
      return 'maitreDId'
    default:
      return ''
  }
}

/**
 * Merges employees with their integration IDs
 * @param allEmployees - Object of all employees
 * @param integrationsIds - Integration IDs mapping
 * @returns Array of employees with integration data
 */
export const mergeEmployeesWithIntegrations = (
  allEmployees: { [key: string]: IEmployee } | null,
  integrationsIds: { [key: string]: any } | null
): IEmployee[] => {
  if (!integrationsIds || !allEmployees) {
    return Object.values(allEmployees || {})
  }

  return Object.values(allEmployees).map((employee: IEmployee) => ({
    ...employee,
    ...integrationsIds[employee.uid]
  }))
}
