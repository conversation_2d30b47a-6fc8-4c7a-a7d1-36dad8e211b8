import dayjs from 'dayjs'
import { forEach, round } from 'lodash'
import { IEmployee } from 'types/employee'
import { VeloceInvoice, WeeklyData, DailyCuts } from 'types/attendance'

/**
 * Calculates weekly data and daily cuts from sales data
 * @param sales - Sales data from integration
 * @param employeesToDisplay - Array of employees to consider
 * @param positionSettings - Position settings for salary types
 * @param employeeKeyToUse - Employee key for matching sales
 * @returns Object containing weeklyData and dailyCuts
 */
export const calculateWeeklyDataAndDailyCuts = (
  sales: Record<string, VeloceInvoice>,
  employeesToDisplay: IEmployee[],
  positionSettings: any,
  employeeKeyToUse: string
) => {
  const weeklyData: WeeklyData = {}
  const dailyCuts: DailyCuts = {}

  forEach(sales, sale => {
    const isWaiterSale = employeesToDisplay.some(
      employee =>
        employeeKeyToUse &&
        employee[employeeKeyToUse as keyof IEmployee] === sale.carrierEmployeeId &&
        employee.positions!.some(
          ({ categoryId }) =>
            positionSettings[categoryId]?.salaryType === 'wage_tips'
        )
    )

    if (isWaiterSale && sale.closeLocalTime !== '1970-01-01T00:00:00Z') {
      const date = dayjs(sale.openLocalTime)
        .startOf('week')
        .format('YYYY-MM-DD')

      const closeDate = dayjs(sale.closeLocalTime).format('YYYY-MM-DD')
      const isMorningShift = sale.closeLocalTime < closeDate + 'T17:00:00'

      if (!dailyCuts[closeDate]) {
        dailyCuts[closeDate] = {
          morning: {
            sales: 0,
            cutHours: {}
          },
          evening: {
            sales: 0,
            cutHours: {}
          }
        }
      }

      const key = isMorningShift ? 'morning' : 'evening'
      dailyCuts[closeDate][key].sales += sale.total

      if (!weeklyData[date]) {
        weeklyData[date] = {
          sales: 0,
          cutHours: {}
        }
      }

      weeklyData[date].sales += sale.total
      weeklyData[date].sales = round(weeklyData[date].sales, 2)
    }
  })

  return { weeklyData, dailyCuts }
}

/**
 * Calculates cut amount for a position
 * @param cutDistribution - Distribution method ('daily' or 'weekly')
 * @param params - Parameters for cut calculation
 * @returns The calculated cut amount
 */
export const calculateCutAmount = (
  cutDistribution: string,
  params: {
    weeklyData: WeeklyData
    dailyCuts: DailyCuts
    startOfWeek: string
    date: string
    positionId: string
    shiftLengthHours: number
    cutGroups: string[][]
    positionSettings: any
    cutRoundingValue: string
    cutRoundingType: 'up' | 'down'
    period: 'morning' | 'evening'
  }
) => {
  const {
    weeklyData,
    dailyCuts,
    startOfWeek,
    date,
    positionId,
    shiftLengthHours,
    cutGroups,
    positionSettings,
    cutRoundingValue,
    cutRoundingType,
    period
  } = params

  let cutHours = 0
  let sales = 0

  const positionToSumUp = cutGroups.find((group: string[]) =>
    group.includes(positionId)
  ) || [positionId]

  const percentageToUse = positionToSumUp.reduce(
    (acc: number, positionId: string) => {
      const { percentage = 0 } = positionSettings[positionId] || {}
      return acc + +percentage
    },
    0
  )

  if (cutDistribution === 'daily') {
    cutHours = dailyCuts[date]?.[period]?.cutHours?.[positionId] || 0
    sales = dailyCuts[date]?.[period]?.sales || 0
  } else if (cutDistribution === 'weekly') {
    cutHours = weeklyData[startOfWeek]?.cutHours?.[positionId] || 0
    sales = weeklyData[startOfWeek]?.sales || 0
  }

  let cutToReceive = 0

  if (cutHours && sales && percentageToUse && shiftLengthHours) {
    cutToReceive =
      Math.round(
        sales * (percentageToUse / 100) * (shiftLengthHours / cutHours) * 100
      ) / 100
  }

  if (cutRoundingValue) {
    const cutRoundingValueNumber = +cutRoundingValue

    if (!isNaN(cutRoundingValueNumber) && cutRoundingValueNumber !== 0) {
      cutToReceive =
        cutRoundingType === 'up'
          ? Math.ceil(cutToReceive / cutRoundingValueNumber) *
            cutRoundingValueNumber
          : Math.floor(cutToReceive / cutRoundingValueNumber) *
            cutRoundingValueNumber
    }
  }

  return cutToReceive
}

/**
 * Calculates tips and cut amounts for wage_tips positions
 * @param cutToPayPercentage - Percentage of sales to pay as cut
 * @param sales - Total sales for the shift
 * @param tips - Total tips for the shift
 * @param cashSales - Cash sales for the shift
 * @returns Object with cutToPay and totalDue
 */
export const calculateTipsAndCut = (
  cutToPayPercentage: number,
  sales: number,
  tips: number,
  cashSales: number
) => {
  const cutToPay = round((cutToPayPercentage / 100) * sales, 2)
  let totalDue = round(-tips + cashSales + cutToPay, 2)

  if (totalDue > 0) {
    totalDue = 0
  }

  return { cutToPay, totalDue }
}

/**
 * Calculates cut percentage to share for a position
 * @param positionId - The position ID
 * @param cutGroups - Array of position groups for cut sharing
 * @param positionSettings - Position settings containing percentages
 * @returns The total percentage to share
 */
export const calculateCutPercentageToShare = (
  positionId: string,
  cutGroups: string[][],
  positionSettings: any
): number => {
  const positionToSumUp = cutGroups.find(group =>
    group.includes(positionId)
  ) || [positionId]

  return positionToSumUp.reduce((acc, positionId) => {
    const { percentage = 0 } = positionSettings[positionId] || {}
    return acc + +percentage
  }, 0)
}
