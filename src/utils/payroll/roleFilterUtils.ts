import { Company, IPosition } from 'types/company'
import { IEmployee, IEmployeePosition } from 'types/employee'

export type DepartmentType = 'BOH' | 'FOH' | 'MNG'

export interface RoleFilterItem {
  id: string
  name: string
  subcategories: {
    id: string
    name: string
  }[]
}

export interface DepartmentRoles {
  FOH: RoleFilterItem[]
  BOH: RoleFilterItem[]
  MNG: RoleFilterItem[]
}

export interface RoleFilterState {
  selectedDepartments: DepartmentType[]
  selectedRoles: {
    [departmentKey: string]: string[] // role IDs
  }
  selectedSubcategories: {
    [roleId: string]: string[] // subcategory IDs
  }
}

/**
 * Extracts and organizes roles from company jobs data
 */
export const extractRolesFromCompany = (jobs: {
  [key: string]: IPosition
}): DepartmentRoles => {
  const departments: DepartmentRoles = {
    FOH: [],
    BOH: [],
    MNG: []
  }

  Object.entries(jobs).forEach(([jobId, job]) => {
    if (job.archived) return

    // Skip roles with empty or undefined names
    if (!job.name || job.name.trim() === '') return

    const roleItem: RoleFilterItem = {
      id: jobId,
      name: job.name,
      subcategories: Object.entries(job.subcategories || {})
        .filter(
          ([, subcategory]) =>
            !subcategory.archived &&
            subcategory.name &&
            subcategory.name.trim() !== ''
        )
        .map(([subcategoryId, subcategory]) => ({
          id: subcategoryId,
          name: subcategory.name
        }))
    }

    // Determine department based on job type
    const department: DepartmentType = job.type || 'FOH' // Default to FOH if no type specified
    if (department in departments) {
      departments[department as keyof DepartmentRoles].push(roleItem)
    }
  })

  return departments
}

/**
 * Creates initial filter state with all roles selected by default
 */
export const createInitialFilterState = (
  departmentRoles: DepartmentRoles
): RoleFilterState => {
  const selectedDepartments: DepartmentType[] = []
  const selectedRoles: { [departmentKey: string]: string[] } = {}
  const selectedSubcategories: { [roleId: string]: string[] } = {}

  Object.entries(departmentRoles).forEach(([department, roles]) => {
    if (roles.length > 0) {
      selectedDepartments.push(department as DepartmentType)
      selectedRoles[department] = roles.map((role: RoleFilterItem) => role.id)

      roles.forEach((role: RoleFilterItem) => {
        selectedSubcategories[role.id] = role.subcategories.map(
          (sub: { id: string; name: string }) => sub.id
        )
      })
    }
  })

  return {
    selectedDepartments,
    selectedRoles,
    selectedSubcategories
  }
}

/**
 * Checks if company has only one role in BOH department
 */
export const isSingleBOHRole = (departmentRoles: DepartmentRoles): boolean => {
  const bohRoles = departmentRoles.BOH
  const fohRoles = departmentRoles.FOH
  const mngRoles = departmentRoles.MNG

  return bohRoles.length === 1 && fohRoles.length === 0 && mngRoles.length === 0
}

/**
 * Gets the single role information if company has only one role
 */
export const getSingleRoleInfo = (departmentRoles: DepartmentRoles) => {
  if (isSingleBOHRole(departmentRoles)) {
    return {
      department: 'BOH' as const,
      role: departmentRoles.BOH[0]
    }
  }
  return null
}

/**
 * Filters employees based on role filter state
 */
export const filterEmployeesByRoles = <
  T extends { positions?: IEmployeePosition[] }
>(
  employees: T[],
  filterState: RoleFilterState,
  jobs: { [key: string]: IPosition }
): T[] => {
  // If no departments are selected, return empty array
  if (filterState.selectedDepartments.length === 0) {
    return []
  }

  return employees.filter((employee: T) => {
    if (!employee.positions || employee.positions.length === 0) {
      return false
    }

    return employee.positions.some((position: IEmployeePosition) => {
      const job = jobs[position.categoryId]
      if (!job || job.archived) return false

      const department: DepartmentType = job.type || 'FOH'

      // Check if department is selected
      if (!filterState.selectedDepartments.includes(department)) {
        return false
      }

      // Check if role is selected
      if (
        !filterState.selectedRoles[department]?.includes(position.categoryId)
      ) {
        return false
      }

      // Check if subcategory is selected (only if employee has a subcategory)
      if (position.subcategoryId) {
        if (
          !filterState.selectedSubcategories[position.categoryId]?.includes(
            position.subcategoryId
          )
        ) {
          return false
        }
      }

      return true
    })
  })
}

/**
 * Storage key for role filter persistence
 */
const ROLE_FILTER_STORAGE_KEY = 'payroll_role_filter'

/**
 * Saves role filter state to localStorage
 */
export const saveRoleFilterState = (state: RoleFilterState): void => {
  try {
    localStorage.setItem(ROLE_FILTER_STORAGE_KEY, JSON.stringify(state))
  } catch (error) {
    console.warn('Failed to save role filter state:', error)
  }
}

/**
 * Loads role filter state from localStorage
 */
export const loadRoleFilterState = (): RoleFilterState | null => {
  try {
    const saved = localStorage.getItem(ROLE_FILTER_STORAGE_KEY)
    return saved ? JSON.parse(saved) : null
  } catch (error) {
    console.warn('Failed to load role filter state:', error)
    return null
  }
}

/**
 * Clears role filter state from localStorage
 */
export const clearRoleFilterState = (): void => {
  try {
    localStorage.removeItem(ROLE_FILTER_STORAGE_KEY)
  } catch (error) {
    console.warn('Failed to clear role filter state:', error)
  }
}
