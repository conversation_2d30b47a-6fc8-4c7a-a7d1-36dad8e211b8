// import dayjs from 'dayjs'
// import { checkShiftOverlap } from 'utils/attendance'
import { AttendanceShift, AttendanceShifts } from 'types/attendance'
import { Company } from 'types/company'

export type PopoverConflictType =
  | 'orange' // Conflict that must be selected, modified, or approved
  | 'red' // Must be resolved immediately (overlapping shifts only)
  | 'grey' // Selection needs to be made to save or approve
  | 'green' // Ready to be saved or approved

export type ConflictDetails = {
  type: PopoverConflictType
  message: string
  field?: 'start' | 'end' | 'role' | 'break'
  showExclamation: boolean
  canSave: boolean
  canApprove: boolean
  actions?: ('save' | 'approve' | 'reclaim')[]
  overlappingWith?: {
    shiftNumber: number
    timeRange: string
  }
}

export type ShiftPopoverAnalysis = {
  conflicts: ConflictDetails[]
  overallStatus: PopoverConflictType
  showTooltip: boolean
  tooltipMessage: string
}

/**
 * Analyzes a shift for detailed popover conflicts and actions
 */
export const analyzeShiftForPopover = (
  shift: AttendanceShift,
  shiftIndex: number,
  allShifts: AttendanceShift[],
  employeeId: string,
  date: string,
  attendanceData: AttendanceShifts,
  currentCompany: Company,
  hasUnsavedChanges: boolean = false
): ShiftPopoverAnalysis => {
  const conflicts: ConflictDetails[] = []
  let showTooltip = false
  let tooltipMessage = ''

  // 1. Check for overlapping shifts (RED - highest priority)
  const overlapAnalysis = checkForOverlappingShifts(
    shift,
    shiftIndex,
    allShifts,
    employeeId,
    date,
    attendanceData
  )
  if (overlapAnalysis.hasOverlap) {
    conflicts.push({
      type: 'red',
      message: `Overlaps with Shift ${overlapAnalysis.overlappingWith?.shiftNumber}`,
      field: overlapAnalysis.conflictField,
      showExclamation: true,
      canSave: false,
      canApprove: false,
      overlappingWith: overlapAnalysis.overlappingWith
    })
    showTooltip = true
    tooltipMessage = `Overlaps with Shift ${overlapAnalysis.overlappingWith?.shiftNumber} (${overlapAnalysis.overlappingWith?.timeRange}). Overlapping times must be corrected before saving.`
  }

  // 2. Check for early/late conflicts (ORANGE)
  const timingConflicts = checkTimingConflicts(shift, currentCompany)
  timingConflicts.forEach(conflict => {
    conflicts.push({
      type: 'orange',
      message: conflict.message,
      field: conflict.field,
      showExclamation: true,
      canSave: true,
      canApprove: true,
      actions: conflict.actions
    })
    if (!showTooltip) {
      showTooltip = true
      tooltipMessage = conflict.tooltipMessage
    }
  })

  // 3. Check for missing selections (GREY)
  const selectionConflicts = checkMissingSelections(shift)
  selectionConflicts.forEach(conflict => {
    conflicts.push({
      type: 'grey',
      message: conflict.message,
      field: conflict.field,
      showExclamation: false,
      canSave: false,
      canApprove: false
    })
  })

  // 4. Check if ready to save/approve (GREEN)
  const isReadyForAction =
    conflicts.length === 0 ||
    conflicts.every(c => c.type === 'orange' && hasUnsavedChanges)
  if (isReadyForAction && hasUnsavedChanges) {
    conflicts.push({
      type: 'green',
      message: 'Ready to save or approve',
      showExclamation: false,
      canSave: true,
      canApprove: true,
      actions: ['save', 'approve']
    })
  }

  // Determine overall status
  const overallStatus = getOverallStatus(conflicts)

  return {
    conflicts,
    overallStatus,
    showTooltip,
    tooltipMessage
  }
}

/**
 * Checks for overlapping shifts
 */
const checkForOverlappingShifts = (
  shift: AttendanceShift,
  shiftIndex: number,
  allShifts: AttendanceShift[],
  employeeId: string,
  date: string,
  attendanceData: AttendanceShifts
) => {
  if (!shift.start || !shift.end) {
    return {
      hasOverlap: false,
      conflictField: undefined as 'start' | 'end' | undefined,
      overlappingWith: undefined
    }
  }

  // Check against other shifts on the same day
  for (let i = 0; i < allShifts.length; i++) {
    if (i === shiftIndex) continue

    const otherShift = allShifts[i]
    if (!otherShift.start || !otherShift.end) continue

    const overlaps =
      shift.start < otherShift.end && shift.end > otherShift.start

    if (overlaps) {
      const conflictField: 'start' | 'end' =
        shift.start < otherShift.start ? 'end' : 'start'
      const startTime =
        Math.floor(otherShift.start / 60)
          .toString()
          .padStart(2, '0') +
        ':' +
        (otherShift.start % 60).toString().padStart(2, '0')
      const endTime =
        Math.floor(otherShift.end / 60)
          .toString()
          .padStart(2, '0') +
        ':' +
        (otherShift.end % 60).toString().padStart(2, '0')

      return {
        hasOverlap: true,
        conflictField,
        overlappingWith: {
          shiftNumber: i + 1,
          timeRange: `${startTime} - ${endTime}`
        }
      }
    }
  }

  return {
    hasOverlap: false,
    conflictField: undefined as 'start' | 'end' | undefined,
    overlappingWith: undefined
  }
}

/**
 * Checks for timing conflicts (early/late arrivals)
 */
const checkTimingConflicts = (
  shift: AttendanceShift,
  currentCompany: Company
) => {
  const conflicts: Array<{
    message: string
    field: 'start' | 'end'
    actions: ('save' | 'approve' | 'reclaim')[]
    tooltipMessage: string
  }> = []

  // This would need scheduled shift data to compare against
  // For now, we'll check basic validation rules

  if (shift.start && shift.end) {
    const duration = shift.end - shift.start

    // Check for very short shifts
    if (duration < 30) {
      conflicts.push({
        message: 'Shift too short',
        field: 'end',
        actions: ['save', 'approve'],
        tooltipMessage:
          'This shift is unusually short. Please verify the times are correct.'
      })
    }

    // Check for very long shifts
    if (duration > 12 * 60) {
      conflicts.push({
        message: 'Shift very long',
        field: 'end',
        actions: ['save', 'approve'],
        tooltipMessage:
          'This shift is longer than 12 hours. Please verify the times are correct.'
      })
    }
  }

  return conflicts
}

/**
 * Checks for missing required selections
 */
const checkMissingSelections = (shift: AttendanceShift) => {
  const conflicts: Array<{
    message: string
    field: 'role' | 'start' | 'end'
  }> = []

  if (!shift.positionId) {
    conflicts.push({
      message: 'Role selection required',
      field: 'role'
    })
  }

  if (!shift.start) {
    conflicts.push({
      message: 'Start time required',
      field: 'start'
    })
  }

  if (!shift.end) {
    conflicts.push({
      message: 'End time required',
      field: 'end'
    })
  }

  return conflicts
}

/**
 * Determines the overall status based on all conflicts
 */
const getOverallStatus = (
  conflicts: ConflictDetails[]
): PopoverConflictType => {
  if (conflicts.some(c => c.type === 'red')) return 'red'
  if (conflicts.some(c => c.type === 'orange')) return 'orange'
  if (conflicts.some(c => c.type === 'grey')) return 'grey'
  return 'green'
}

/**
 * Gets the appropriate button states for the popover
 */
export const getPopoverButtonStates = (analysis: ShiftPopoverAnalysis) => {
  const hasRedConflicts = analysis.conflicts.some(c => c.type === 'red')
  const hasGreyConflicts = analysis.conflicts.some(c => c.type === 'grey')

  return {
    canSave: !hasRedConflicts && !hasGreyConflicts,
    canApprove: !hasRedConflicts && !hasGreyConflicts,
    showReclaim: analysis.conflicts.some(c => c.actions?.includes('reclaim')),
    saveButtonColor: analysis.overallStatus === 'green' ? 'green' : 'orange',
    approveButtonColor: analysis.overallStatus === 'green' ? 'green' : 'orange'
  }
}
