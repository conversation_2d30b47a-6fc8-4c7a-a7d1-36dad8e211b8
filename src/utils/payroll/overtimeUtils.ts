import { reduce } from 'lodash'
import { OvertimeCalculationMode } from 'types/attendance'
import { SALARY_TYPE_YEARLY } from 'utils/constants'
import { getTotalSalary } from 'utils/employees'

/**
 * Calculates overtime duration and salary for a shift
 * @param pastShiftsDuration - Total duration of previous shifts
 * @param shiftLengthHours - Length of current shift in hours
 * @param maxHoursPerWeek - Maximum hours per week before overtime
 * @param overtimeCalculationMode - How overtime is calculated
 * @param rate - Hourly rate
 * @param additionalSalary - Additional salary amount
 * @param type - Salary type (hourly, yearly, etc.)
 * @returns Object containing overtime duration and total salary
 */
export const calculateOvertimeAndSalary = (
  pastShiftsDuration: number,
  shiftLengthHours: number,
  maxHoursPerWeek: number,
  overtimeCalculationMode: OvertimeCalculationMode,
  rate: number,
  additionalSalary: number,
  type: string
) => {
  let overtimeDuration = 0
  const overtimeThreshold =
    overtimeCalculationMode === 'weekly'
      ? maxHoursPerWeek
      : maxHoursPerWeek * 2

  const isOvertimeShift =
    overtimeCalculationMode === 'not-calculated'
      ? false
      : pastShiftsDuration + shiftLengthHours > overtimeThreshold

  if (isOvertimeShift) {
    overtimeDuration =
      pastShiftsDuration > overtimeThreshold
        ? shiftLengthHours
        : pastShiftsDuration + shiftLengthHours - overtimeThreshold
  }

  let salary =
    // TODO: handle overtime when the last shift is yearly salary
    overtimeDuration && type !== SALARY_TYPE_YEARLY
      ? getTotalSalary({
          rate: rate * 1.5,
          additionalSalary: 0,
          shiftLengthHours: overtimeDuration,
          type
        }) +
        getTotalSalary({
          rate: rate,
          additionalSalary: additionalSalary,
          shiftLengthHours: shiftLengthHours - overtimeDuration,
          type
        })
      : getTotalSalary({
          rate: rate,
          additionalSalary: additionalSalary,
          shiftLengthHours: shiftLengthHours,
          type
        })

  return {
    overtimeDuration: Math.round(overtimeDuration * 100) / 100,
    salary: Math.round(salary * 100) / 100
  }
}

/**
 * Calculates past shifts duration for overtime calculation
 * @param allShifts - All shifts data
 * @param currentEmployeeId - ID of current employee
 * @param currentDate - Date of current shift
 * @param currentShiftStart - Start time of current shift
 * @param overtimeCalculationMode - How overtime is calculated
 * @param payrollFrequency - Payroll frequency (weekly/biweekly)
 * @param week2 - Second week date for biweekly payroll
 * @param isSecondWeekShift - Whether current shift is in second week
 * @returns Total duration of past shifts
 */
export const calculatePastShiftsDuration = (
  allShifts: any,
  currentEmployeeId: string,
  currentDate: string,
  currentShiftStart: number,
  overtimeCalculationMode: OvertimeCalculationMode,
  payrollFrequency: string,
  week2: string | null,
  isSecondWeekShift: boolean
): number => {
  return reduce(
    allShifts,
    (acc: number, dayEmployees: any, date: string) => {
      if (!dayEmployees[currentEmployeeId]) {
        return acc
      }

      // if overtimeCalculationMode === weekly
      // then we only need hours for that week
      if (
        overtimeCalculationMode === 'weekly' &&
        payrollFrequency === 'biweekly'
      ) {
        const isSecondWeek = week2 && date >= week2

        if (isSecondWeek !== isSecondWeekShift) {
          return acc
        }
      }

      let dayShifts: any[] = []

      if (date < currentDate) {
        dayShifts = Object.values(dayEmployees[currentEmployeeId]).filter(
          (shift: any) => shift.isConfirmed || !shift.isConflictingShift
        )
      }

      // make sure we don't include current shift
      // and make sure we don't include shifts later that day
      if (date === currentDate) {
        dayShifts = Object.values(dayEmployees[currentEmployeeId]).filter(
          (shift: any) =>
            !shift.isConflictingShift &&
            shift.start !== undefined &&
            currentShiftStart !== undefined &&
            shift.start > currentShiftStart
        )
      }

      const dayShiftsDuration = dayShifts.reduce(
        (acc: number, shift: any) => acc + shift.shiftLengthHours,
        0
      )

      return acc + dayShiftsDuration
    },
    0
  )
}

/**
 * Determines if a shift is in the second week of a biweekly period
 * @param date - Date of the shift
 * @param week2 - Start date of second week
 * @returns Boolean indicating if shift is in second week
 */
export const isShiftInSecondWeek = (date: string, week2: string | null): boolean => {
  return Boolean(week2 && date >= week2)
}
