const translationsObject = {
  en: {
    requests: {
      all_employees_have_declined: 'All employees have declined',
      unanswered: 'Unanswered',
      recurrent_day_off: 'recurrent day off',
      recurrent_shift_conflict: 'recurrent shift conflict',
      employees_will_be_taken_by_regular_shift:
        'employee(s) will be assigned to a regular shift.',
      requests: 'requests',
      title_timeoff_requests: 'time off requests',
      title_replacement_requests: 'replacement requests',
      title_exchange_requests: 'exchange requests',
      title_availability_requests: 'availabilities requests',
      title_emergency_requests: 'emergencies requests',
      title_direct_requests: 'direct requests',

      all_requests: 'All requests',
      your_emergency_request: 'Your emergency request',
      new_requests: 'New requests',
      new: 'New',
      pending: 'Pending',
      archive: 'Archive',
      no_new_requests: 'No new requests',
      no_pending_requests: 'No pending requests',
      no_archived_requests: 'No archived requests',
      requests_at_the_moment: 'at the moment',
      shift_starts: 'Shift starts',
      replacers: 'Replacers',
      declined: 'Declined',
      decline: 'Decline',
      cancel_shift: 'Cancel shift',
      s_shift: "'s shift",
      replaced_by: 'Replaced by',
      accepted_by: 'Accepted by',

      times_off: 'Times off',
      replacements: 'Replacements',
      exchanges: 'Exchanges',
      availailities: 'Availabilities',
      emergencies: 'Emergencies',
      direct_inputs: 'Direct inputs',
      what_if: 'What if',
      answer: 'Answer',
      would_like_take_time_off: 'Would like to take a time off',
      in_a_row: 'in a row',
      split: 'split',
      would_like_to_exchange_shifts: 'Would like to exchange shifts',
      after: 'After',
      the_exchange: 'exchange',
      would_like_to_be_replaced: 'Would like to be replaced',
      sent_at: 'Sent at',
      would_like_to_change_availabilities:
        'Would like to change availabilities',
      starting: 'Starting',
      no_conflict: 'No conflict',
      show_availaibilites: 'Show availabilities',
      hide_availaibilites: 'Hide availabilities',
      sent_you_direct_input_request: 'Sent you a direct input request',
      in_pending_for: 'in pending for',
      refuse: 'Refuse',
      accept: 'Accept',
      accepted: 'Accepted',
      refused: 'Refused',
      you_have_accepted_request: 'You have accepted the request.',
      you_have_refused_request: 'You have refused the request.',
      you_have_put_request_in_pending: 'You have put the request in pending.',
      you_can_find_it_in_archive_tab: 'You can find it in archive tab.',
      you_can_find_it_in_pending_tab: 'You can find it in pending tab.',
      dates: 'Dates',
      times: 'Times',
      notify_again: 'Notify again',
      no_employee_has_answered_yet: 'No employee have answered yet...',
      no_employees: 'No employees',
      employee_that_already: 'Employees that already',
      to_replace: 'to replace',
      working: 'Working',
      days: 'days',
      hours: 'hours',
      shifts_at_risk: 'shift(s) at risk',
      employees_available_for: 'employees available for',
      canceled_by_employee: 'Canceled by employee',
      by: 'By',
      would_you_let_employee_know_why:
        'Would you like to let employee know why ?',
      select_reason: 'Select a reason',
      not_enough_employees: 'Not enough employees',
      busy_date: 'Busy date (Holidays, etc.)',
      abusive_timeoff: 'Abusive or repeated use of time off',
      requested_too_early: 'Requested too early',
      requested_too_late: 'Requested too late',

      by_accepting_short: 'By',
      accepting: 'accepting',
      this_request: 'this request...',

      shifts_dont_have_enough_employees: 'shift(s) don’t have enough employees'
    },
    integrations: {
      connect: 'Connect',
      disconnect: 'Disconnect',
      reconnect: 'Reconnect',
      update: 'Update',
      instructions: 'Instructions',
      payroll: {
        errors: {
          invalid_business_code: 'Your business code is invalid',
          allow_external_provider_on: 'Allow service user on ',
          user_code_not_matching_business_code:
            'The service user code (name) does not match this business.',
          user_code_deactivated: 'Your user code is deactivated on ',
          payroll_is_under_maintenance: ' is under maintenance, try later',
          update_user_type_to_service_user:
            'Update user type to service user on ',
          allow_user_account_to_connect_to_the_api:
            'Allow user account to connect to the api on ',
          account_locked:
            'Your account is locked, contact your payroll administrator',
          expired_password: 'Service user password has expired on ',
          invalid_password:
            'Service user password is invalid, check with your payroll administrator',
          invalid_password_attempt_left_1:
            'Service user password is invalid, attempt left 1 then the user will be locked. Contact your payroll administrator.',
          invalid_app_id:
            'The code is invalid, this code represents the name of the service user',
          allow_pivot_on: 'Allow PIVOT as external provider on ',
          language_error: 'Language error'
        }
      },
      you_have_been_disconnected_could_affect_procedures:
        "You've been disconnected. This could affect some procedures."
    },
    reconnect: {
      you_are_disconnected: 'You are disconnected from',
      click_down_to_reconnect: 'Click down to reconnect',
      reconnect: 'Reconnect',
      instructions: 'Instructions'
    },
    common: {
      please_read_and_confrim: 'Please Read and Confirm',
      please_review_terms_and_conditions:
        'To continue, please review the Terms and Conditions document. You must confirm that you have read and understood the content before proceeding.',
      view_document: 'View document',
      i_have_read_and_understood_the_terms_and_conditions:
        'I have read and accept the Terms and Conditions.',
      help: 'Help',
      new_shift: 'New shift',
      email: 'Email',
      write_your_email_here: 'Write your email here',
      incorrect_email: 'Incorrect email',
      password: 'Password',
      repeatPassword: 'Repeat password',
      new_version_released: 'A new version has been released.',
      would_you_like_to_reload_the_app_to_get_the_latest_version:
        'Would you like to reload the app to get the latest version?',
      confirm: 'Confirm',
      cancel: 'Cancel',
      save: 'Save',
      save_shorten: 'Save',
      send: 'Send',
      back: 'Back',
      skip: 'Skip',
      add: 'Add',
      open: 'Open',
      apply: 'Apply',
      next: 'Next',
      close: 'Close',
      create: 'Create',
      continue: 'Continue',
      submit: 'Submit',
      employees: 'Employees',

      addPhoto: 'Add Photo',
      uploading: 'Uploading',
      noFileSelected: 'No file selected',
      or: 'or',
      error_general:
        'Something went wrong, please reload the page and try again. Our team has been notified about this issue.',

      step: 'Step',
      nextStep: 'Next step',
      optional: 'optional',
      connect: 'Connect',
      skipStep: 'Skip step',
      start: 'Start',
      done: 'Done',
      roles: 'Roles',
      role: 'Role',

      search_employees: 'Search employees',
      shift: 'Shift',
      duration: 'Duration',
      day: 'Day',
      starts: 'Starts',
      ends: 'Ends',
      end: 'End',
      show: 'Show',
      undo: 'Undo',
      reason: 'Reason',
      no_reason: 'No reason',
      hide: 'Hide',
      before: 'Before',
      new: 'New',
      availability: 'Availability',
      available: 'Available',
      unavailable: 'Unavailable',
      all_day: 'All day',
      all_day_shorten: 'All day',
      not_available_abbreviation: 'N/A',
      time_off: 'Time off',
      english: 'English',
      french: 'French',

      reconnect_pos: 'You need to reconnect your POS',
      are_you_sure_you_want_to_delete: 'Are you sure you want to delete',
      no_cancel: 'No, cancel',
      yes_remove: 'Yes, remove',
      day_shorten: 'd',
      hour_shorten: 'h',
      maximum_shorten: 'Max',
      year: 'year',
      hours: 'hours',

      support: 'Support',
      no_employees_found: 'No employees found',

      week_shorten: 'W',
      hours_shorten: 'H',
      minutes_shorten: 'Min',
      select: 'Select',
      delete: 'Delete',
      approve: 'Approve'
    },
    cities: {
      montreal: 'Montreal',
      quebec_city: 'Quebec City',
      philadelphia: 'Philadelphia'
    },
    userGuide: {
      welcome_user: 'Welcome user',
      new_account_creation_steps: 'New account creation steps',
      download_mobile_app: 'Download mobile app',
      get_started: 'Get Started !',
      click_on: 'Click on',
      fill_all_the_steps: 'Fill all the steps',
      to: 'to',
      enter_code: 'Enter code',
      request_sent: 'Request sent',
      welcome_to_pivot: 'Welcome to Pivot !',
      new_user_steps: 'New user steps',
      download: 'Download',
      qr_code: 'QR code',
      user_guide_downloaded: 'User guide downloaded successfully',
      error_downloading_user_guide:
        'Error downloading user guide, please try again later. If error persists, call Yannick ************',
      scan_me: 'Scan me !',
      qr_code_downloaded: 'QR code downloaded successfully',
      error_downloading_qr_code:
        'Error downloading QR code, please try again later. If error persists, call Yannick ************',
      select_language_to_download: 'Select language to download',
      new_user_steps_filename: 'New user steps'
    },
    group: {
      fromPersonalAccount: 'From personal account',
      writeaNewPost: 'Write a new post...',
      photo: 'Photo',
      noPostsHereYet: 'No posts here yet, be the ',
      first: 'first',
      allPosts: 'All Posts',
      companyPosts: 'Company Posts',
      employeesPosts: 'Employees Posts',
      thePostWasRemoved: 'The post was removed',
      restore: 'Undo',
      loading: 'Loading...',
      comments: 'Comments',
      deletePost: 'Delete Post',
      hideComments: 'Hide Comments',
      seeComments: 'See Comments',
      writeaNewComment: 'Write a new comment...',
      commentSaved: 'Comment saved!',
      postSaved: 'Posted!',
      document: 'Document',
      click_to_open: 'Click to open',
      writeComment: 'Write Comment',
      //modal
      close: 'Close',
      addFile: 'Add File',
      drag_file_here_or: 'Drag file here or ',
      select_file: 'select file',
      deleteComment: 'Delete comment',
      missing: 'MISSING',
      viewers: 'VIEWERS',
      commentRemoved: 'The comment was removed ',
      emptyComment: "Can't save empty comment",
      unsupported_file_type: 'Unsupported file type',
      max_file_size_is_50_mb:
        'Max file size is 50MB. Contact support if needed.'
    },
    jobs: {
      selectName: 'Select name',
      status: 'Status',
      loading: 'Loading...',
      addRole: 'Add role',
      //jobslist
      addNewPosition: 'Add new position',
      newPosition: 'New position',
      position: 'Position',
      positions: 'Positions',
      //subcatComp
      description: 'Description:',
      nameOfTheJob: 'Name of the Job:',
      //categoryItem
      delete: 'Delete',
      thisCategoryHasEmployees:
        'This category has employees. Move or delete them first',
      cantDeleteLastSubposition:
        "Can't delete last subposition. Position must have at least one subposition",
      thisSubpositionHasEmployees:
        'This subposition has employees. Move or delete them first',
      addSubPosition: '+ Add a sub-position',
      //categoryHeader
      newSubPosition: 'New Subposition',
      back: 'Back',
      hiring: 'Hiring',
      notHiring: 'Not Hiring',
      save: 'Save',
      add: 'Add',
      close: 'Close',
      edit: 'Edit',
      editJob: 'Edit Job',
      //categoryComp
      salary: 'Salary',
      hireForaFirstJob: 'Hiring first-time employees?',
      numberOfYearsExperience: 'Years required:',
      nameOfTheSubPos: 'Name of the subposition:',
      experience: 'Experience:',
      //jobDescription
      nameIsRequired: 'Name is required',
      subpositionNameIsRequired: 'Subposition name is required',
      descriptionIsRequired: 'Description is required',
      salaryIsRequired: 'Salary is required',
      acronymIsRequired: 'Acronym is required',
      nameAndDescriptionIsRequired: 'Name and Description is required',
      departmentIsRequired: 'Department is required',
      Monday: 'Monday',
      Tuesday: 'Tuesday',
      Wednesday: 'Wednesday',
      Thursday: 'Thursday',
      Friday: 'Friday',
      Saturday: 'Saturday',
      Sunday: 'Sunday',
      sureDeletePosition: 'Are you sure you want to delete this position?',
      sureDeleteSubPosition:
        'Are you sure you want to delete this subposition?',
      positionName: "Position's name",
      subPositions: 'Sub-positions',
      acronyms: 'Acronyms',
      acronymAppearsSchedule: 'Appear on schedule',
      acronymNotAppearsSchedule: 'Do not appear',
      deletePosition: 'Delete position',
      experienceRequired: 'Experience required',
      years: ' years',
      hiringFirstJobEmployees: 'Hiring first job employees ',
      lookingAvailabilities: 'Looking availabilities',
      yes: 'Yes',
      no: 'No',
      showOnSchedule: 'Appear on schedule',
      doesNotShowOnSchedule: 'Do not appear',
      addManagersTitle: 'Managers - %{role}',
      'role(s)': 'Role(s)',
      managerAccessFor: 'Manager access for',
      searchRole: 'Search role...',

      roles: 'Roles',
      newRole: 'New role',
      noManagers: 'No managers',
      managerAccess: 'Manager access',
      editManager: 'Edit manager(s)',
      roleName: "Role's name",
      basicSalary: 'Basic salary',
      subRoles: 'Sub-roles',
      subRole: 'Sub-role',
      addSubRole: 'Add sub-role',
      searchEmployee: 'Search employee...',
      unselectedEmployees: 'Unselected employees',
      selectedEmployees: 'Selected employee(s)',
      unselectedRoles: 'Unselected role(s)',
      selectedRoles: 'Selected role(s)',
      emptyPositionWarning:
        "Can't update the order as one of the roles or sub-roles is missing a name",
      colors: 'Colors',
      color_on: 'Color on',
      color_off: 'Color off',

      documents: 'Documents',
      addDocument: 'Add document',
      signature: 'Signature',
      date: 'Date',
      dragAndDrop: 'Drag and drop',
      confirm: 'Confirm',
      documentImport: 'Document import',
      successfullyUploaded: 'Successfully uploaded',
      signatureDate: 'Signature / date',
      addSignatureDate: 'Add signature / date',
      importOrDragFileHere: 'Import or drag a file here',
      import: 'Import',
      reset: 'Reset',
      changesSaved: 'Changes saved',
      deleteDocument: 'Delete document',
      areYouSureToDeleteDocument: 'Are you sure to delete this document ?',

      additionalSalary: 'Additional salary',
      deleteRole: 'Delete role',
      hourly: 'hourly',
      perShift: 'per shift'
    },
    components: {
      Availabilities: {
        availabilities: 'Availabilities',
        day: 'Day',
        evening: 'Evening',
        night: 'Night',
        preview: 'Preview',
        shift: 'Shift',
        beginning: 'Beginning',
        end: 'End',
        allDay: 'All day',
        allDayHead: 'All day',
        preferences: 'Preferences',
        available: 'Available'
      }
    },
    employees: {
      integration_requires_reconnection: 'integration requires reconnection',
      no_results_found: 'No results found',
      did_not_return_any_employees_check_integration_settings:
        "didn't return any employees. Please check the integration settings",
      not_allowed_to_add_delete_this_position:
        'Not allowed to add/delete this position',
      yearly_salary_cant_be_applied_when_having_multiple_yearly_salaries:
        'Yearly salary cannot be applied when having multiple yearly salaries.',
      nethrisId: 'Nethris ID',
      totalEmployees: 'Total employees',
      maxHoursPerWeek: 'Max. hours per week',
      maxDaysPerWeek: 'Max. days per week',
      priorityList: 'Priority list',
      loadingEmployees: 'Loading employees...',
      noEmployeesInThisPosition: 'No employees in this position',
      selectSubpositionYouWantToRemove:
        'Please, select subposition you want to remove from',
      otherPositionsOfTheEmployee: 'Other positions occupied:',
      dateOfHiring: 'Date of hiring:',
      salary: 'Salary',
      statistics: 'Attendance:',
      numberOfShiftExchanges: 'Shift Exchanges',
      numberOfReplacements: 'Replacements',
      numberOfDaysOff: 'Days Off',
      selectPositionAndSubpositionFirst:
        'Please, select position and subposition first',
      loading: 'Loading...',
      youHaveNoPositions: 'You have no positions',
      createaPosition: 'Create a position',
      moveToAnotherPosition: 'Move to another position',
      addAnotherPosition: 'Add another position',
      removeFromThisPosition: 'Remove from this position',
      removedFromThisPosition: 'removed from this position',
      positions: 'Positions',
      addNewEmployee: 'Add new employee',
      areYouSureToWantToRemove: 'Are you sure to want to remove',
      areYouSureRemoveEmployee:
        'Are you sure you want to remove this employee definitely?',
      employeeWillMoveToArchive:
        'By doing so, this employee will be moved to archive.',
      yesArchive: 'Yes, archive',
      fromThisPosition: 'from this position',
      noCancel: 'Cancel',
      yesRemove: 'Yes, remove',
      youCanRecoveryThisEmployeeOnPage:
        'You can recovery this employee on page',
      applicantsDisapproved: '«Applicants/Disapproved»',
      disapproved: 'Disapproved',
      allRight: 'All right!',
      editRoles: 'Edit roles',
      editEmployee: 'Edit employee',
      hired: 'Hired on',
      applyChanges: 'Apply changes',
      noPositionsYet: 'No positions yet',
      save: 'Save',
      firstName: 'First name',
      lastName: 'Last name',
      employeesEmail: "Employee's email",
      employeesAvailabilities: "Employee's availabilities",
      employeesPositions: "Employee's positions",
      createAnEmployee: 'Create an employee',
      typeHere: 'Type here...',
      enterEmail: 'Enter email here...',
      maxHours: 'Max hours per week',
      days: 'days',
      hours: 'hours',
      hour: 'hour',
      roles: 'Roles',
      requests: 'Requests',
      apply: 'Apply',
      salaryPerHour: 'Salary per hour',
      preferredNumberShifts: 'Preferred # of shifts',
      maximumNumberShifts: 'Maximum # of shifts',
      sendAgain: 'Send again',
      invitationPending: 'invitation pending...',
      sendInvitation: 'Send invitation',
      employeeAvailabilitiesUpdated:
        'Employee availabilities have been updated',

      employeeFile: 'Employee file',
      active: 'Active',
      archived: 'Archived',
      generalInformations: 'General informations',
      archiveEmployee: 'Archive employee',
      fullName: 'Full name',
      email: 'Email',
      employeeNumber: 'Employee number',
      phoneNumber: 'Phone number',
      birthDate: 'Birth date',
      dateHired: 'Date hired',
      homeAddress: 'Home address',
      emergencyContact: 'Emergency contact',
      documents: 'Documents',
      incomplete: 'Incomplete',
      required: 'Required',
      notSeen: 'Not seen',
      completed: 'Completed',
      veloceId: 'Veloce ID',
      acombaId: 'acomba ID',
      powerpayId: 'Powerpay ID',
      lightspeedId: 'Lightspeed ID',
      push_notifications_on_mobile_app: 'Push Notifications on Mobile App',
      enabled: 'Enabled',
      disabled: 'Disabled',

      roleS: 'Role(s)',
      effectiveStarting: 'Effective starting',
      hourly: 'hourly',
      perShift: 'per shift',
      yearly: 'yearly',
      not_set: 'not set',
      failedToApplyChanges: 'Failed to apply changes',
      searchEmployee: 'Search employee',
      all_roles: 'All roles',
      selectedEmployee: 'Selected employee',
      priorityListOrder: 'Priority order list',
      inactive: 'Inactive',
      rightAway: 'Right away',
      pending_changes: 'Pending changes',
      employerDId: 'EmployeurD ID',
      yearly_rate_less_than_100:
        'Did you want to select the hourly salary type instead?',
      hourly_rate_more_than_100:
        'Did you want to select the yearly salary type instead?',
      employee_has_been_created: 'Employee has been created',
      you_are_not_allowed_to_select_this_position:
        'You are not allowed to select this position',
      search_veloce_employee_placeholder: 'Search employee (min 3 chars)...',
      veloce_can_search_by_name_adress_phone:
        'You can search an employee by name or surname. Enter at least 3 characters.',
      no_results: 'No results',
      employee_name_and_surname_are_required:
        'Employee name and surname are required'
    },

    addEmployee: {
      no_roles: 'No roles',
      name_and_surname_should_contain_only_eng_or_fr_letters:
        'Name and surname should contain only english or french letters'
    },
    message: {
      no_matches_found: 'No matches found',
      today: 'Today',
      file_size_error: 'File size should not exceed 5 MB',
      sentAttachment: 'Sent an attachment',
      newChat: 'New chat!',
      loading: 'Loading...',
      //dialogues
      dialogs: 'Messages ',
      noChatsFound: 'No chats found',
      //components=>deleteDialogModal
      areYouSureYouWantDeleteHangout:
        'Are you sure you want to delete this hangout?',
      noCancel: 'Cancel',
      yesRemove: 'Yes, delete',
      //messages
      noMessagesToDisplay: 'No messages to display',
      loadMore: ' load more ',
      youHaveNoEmployees: 'You have no employees',
      fired: 'fired',
      notReadYet: 'Not read yet',
      at: 'at',
      read: 'Read',
      sent: 'Sent',
      justNow: 'just now',
      beTheFirstToSend: 'Be the first to send a message...',
      selectConversation: 'Select a conversation',
      deleteConversation: 'Delete conversation',
      you: 'You',

      searchConversation: 'Search conversation...',
      newGroupChat: 'New group chat',
      edit_group_chat: 'Edit group chat',
      online: 'Online',
      offline: 'Offline',
      ago: ' ago',
      editGroup: 'Edit group',
      attachFiles: 'Attach file(s)',
      attachPhotos: 'Attach photo(s)',
      message: 'Message',
      howToNameYourGroupChat:
        'How would you like to name your new group chat ?',
      typeName: 'Type a name',
      newGroupChatWith: 'New group chat with',
      selectedRolesEmployees: 'Selected role(s) / employee(s)',
      noOneSelected: 'No one selected',
      searchEmployee: 'Search employee',
      readAt: 'Read at',
      seen: 'Seen',
      delivered: 'Delivered',

      was_online: 'Was online '
    },
    applicants: {
      priorityList: 'Priority list',
      all: 'All',
      loading: 'Loading...',
      thereIsNoApplicantsAtTheMoment: 'There are no applicants at the moment',
      age: 'Age:',
      languages: 'Languages:',
      contact: 'Contact:',
      phone: 'Phone:',
      address: 'Address:',
      workExperience: 'Work Experience:',
      highestScholarly: 'Highest Level of Education:',
      position: 'Position',
      unsorted: 'Unsorted',
      interview: 'Interview',
      interview_on: 'Interview on',
      pastInterview: 'Past Interview',
      archive: 'Archive',
      directInput: 'Direct input',
      //disapproved
      isUnsorted: ' is Unsorted now',
      isDeleted: ' is deleted',
      deleteForever: 'Delete forever',
      reestablish: 'Reestablish',
      //interview
      isDisapproved: ' is disapproved',
      reject: 'Reject',
      undo: 'Undo',
      //pastinterview
      hireForWork: 'Hire for work',
      //interviewsuccess
      allRight: 'All right!',
      excellent: 'Excellent',
      yourInterviewIsScheduledTheContestant:
        'Your interview is scheduled The contestant will be notified in the near future',
      //assigninterview
      chooseDateAndTime: 'Choose date and time',
      pleaseChooseHowToInterview: 'Please, choose how to interview',
      interviewWith: 'Interview with ',
      ready: ' ready',
      selectDate: 'Select date',
      writeShortFescriptionHere: 'Write short description here',
      scheduleTheInterviewWith: 'Schedule the interview with ',
      time: 'Time:',
      howToInterview: 'How to interview?',
      message: 'Message:',
      startOfInterview: 'Start of interview',
      onThePhone: 'On the phone',
      atTheMeeting: 'In store',
      skype: 'Skype',
      hintText: 'If you want to change the position of the user - drag it',
      cancelInterview: 'Cancel Interview',
      assignInterview: 'Assign Interview',
      interviewDate: 'interview date ',
      filter: 'Filter',
      contentFilter: 'Content filter',
      availabilities: 'AVAILABILITIES',
      skills: 'SKILLS',
      lastSchoolFrequented: 'Last school frequented',
      spokenLanguages: 'Spoken languages',
      levelAchieved: 'Level achieved',
      driverLicence: "Driver's licence",
      non: 'non',
      liveAt: 'Live at',
      experiences: 'EXPERIENCES',
      references: 'REFERENCES',
      presonal: 'PERSONAL',
      discription: 'DISCRIPTION',
      applicantFilter: 'Applicants filter',
      scolarityLevelAchieved: 'Scholarly level achieved',
      requiredExperience: 'Required experience',
      durationYear: 'Duration (year)',
      distance: 'Distance',
      applyFilter: 'Apply filter',
      required: 'Required',
      notRequired: 'Not required',
      date: 'Date',
      positions: 'Positions',
      appliedOn: 'Applied on'
    },
    notifications: {
      availabilities: 'Availabilities',
      isLoading: 'is loading',
      'groupNotifications ': 'Group notifications ',
      'peopleNotifications ': 'People notifications ',
      mustReceiveaSalary: 'Must receive a salary ',
      wantToSwapShiftsOn: 'Want to swap shifts on ',
      wishesToTakeaDayOff: 'Would like to have time off for',
      daysOff: 'Would like to have time off for',
      to: ' to ',
      wantToSwapShifts: 'Would like to make a shift exchange:',
      wantToReplaceShifts: 'Want to replace shift on ',
      replaceShiftWith: ' wants to be replaced by ',
      replaceShiftWithEmergency: ' wants to be emergency replaced by ',
      replaceEmployerEmergency: 'Emergency replace was ',
      replaceEmergencyHasBeenSent:
        'Emergency replace has been sent to employees.',
      by: 'by',
      noNotifications: 'No notifications yet',
      at: ' at ',
      on: ' on ',
      //notificationslist
      allow: 'Accept',
      reject: 'Decline',
      confirmed: 'Confirmed',
      confirm: 'Confirm',
      maxHoursPerWeek: 'Maximum hours per week',
      //notificationsgroup
      loadMore: ' charger plus',
      today: 'Today',
      and: ' and ',
      changeDecision: 'Change decision',
      commentYourPost: 'Comment on your post',
      createdNewPost: 'Created a new post',
      accepted: 'Accepted',
      declined: 'Declined',
      wouldLikeToBe: 'Would like to be',
      all: 'All',
      exchangeRequests: 'Exchange requests',
      replacementRequests: 'Replacement requests',
      dayOffRequests: 'Day(s) off requests',
      emergency: 'EMERGENCY',
      onholdShifts: 'On-Hold shifts',
      noNotificationsYet: 'No notifications yet',
      wouldLike: 'Would like to be',
      replacedBy: 'replaced by',
      reason: 'Reason',
      sShift: "'s shift",
      sShiftFr: ' ',
      forHisShift: 'For his shift',
      request: 'Request',
      wontBeAbleAttendShift: "Won't be able to attend his shift",
      createdByEmployer: 'Created by employer.',
      motive: 'Motive',
      seePotentialReplacers: 'See potential replacers',
      confirmedInterestedEmployees: 'Confirmed interested employees',
      sendConfirmation: 'Send confirmation',
      findAnotherEmployee: 'Find another employee',
      noOneAcceptedRequest: 'No one has been accepted this request',
      hour_left_before_shift: 'hour left before shift',
      changeAvailabilities: 'Would like to change his availabilities',
      changeAvailabilitiesNew:
        'Would like to change their availability starting from ',
      show: 'Show',
      seeAvaillabilities: 'See availabilities',
      beginning: 'Beginning',
      end: 'End',
      allDay: 'All day',
      employees: 'Employees',
      positions: 'Positions',
      attendancy: 'Attendance',
      confrim: 'Confirm',
      acceptedEmployee: 'Accepted employee',
      previous: 'Previous',
      new: 'New',
      getShiftTimeStringError:
        'Shift date cannot be displayed, reloading the app may fix this. Our team has been notified about this issue.',
      getShiftPositionError:
        "Position's name cannot be displayed, reloading the app may fix this. Our team has been notified about this issue.",
      cancelledByEmployee: 'Cancelled by employee',
      day: 'day',
      left_before_shift: 'left before shift',
      hour: 'hour',
      month: 'month',
      less_than_hour_left_before_shift: 'Less than an hour left before shift',
      s_on_call_shift: "'s on-call shift",
      day_off_until: 'until',
      day_off_from: 'from',
      day_off_all_day: 'all day'
    },
    companySettings: {
      you_dont_have_manager_access_to_this_company:
        "You don't have manager access to this company",
      industries: {
        restaurant: 'Restaurant',
        retail: 'Retail Store'
      },
      nameIsRequired: 'Name is required',
      addressIsRequired: 'Address is required',
      cityIsRequired: 'City is required',
      pleaseEnterValidEmailAddress: 'Please, enter valid email address',
      fileShouldBeNoMore:
        'File should be no more than 5 Mb and jpeg or png format',
      writeNameOfYourCompanyHere: 'Write name of your company here',
      writeDomainsIsYourCompanyHere: 'Select company industry',
      chooseTheYear: 'Choose the year',
      writeYourCompanyLocationAddressHere:
        'Write your company location address here',
      writeInWhichCityYourCompanyIsSituatedHere:
        'Write in which city your company is situated here',
      writeYourCompanyEmailAddressHere: 'Write your company email address here',
      writeShortDescriptionOfYourCompanyHere:
        'Write short description of your company here',
      filled: ' filled',
      required: ' required',
      whatIsTheNameOfYourCompany: 'Company name',
      inWhichDomainsIsYourCompany: 'Company industry',
      yearYourCompanyCreated: 'Year your company created',
      whatIsYourCompanyLocationAddress: 'Company street address',
      inWhichCityYourCompanyIsSituated: 'City',
      whatIsYourCompanyEmailAddress: 'Company e-mail address',
      whatIsYourCompanysPhoneNumber: 'Company phone number',
      shortDescriptionOfYourCompany: 'Short description of your company:',
      fillAllNecessaryFields: 'Fill all necessary fields',
      allFieldsAreFilledCorrectly: 'All fields are filled correctly',
      save: 'Save',
      addNewCompany: 'Add new company',
      //savemodal
      allRight: 'All right!',
      changesSaved: 'Changes saved',
      //cancelModal
      areYouSureYouWantToDiscardChanges:
        'Are you sure you want to discard changes?',
      comeBack: 'Come Back',
      confirm: 'Confirm',
      yesRemove: 'Yes,remove',
      scaleAvatar: 'Scale avatar',
      accessCodeShare: 'Access code (share it with head office only)',
      copyToClipboard: 'Copy to clipboard',
      companyAvatar: 'Company avatar',
      companyName: 'Company name',
      areYouSureToCancelIntegration:
        'Are you sure you want to cancel integration?',
      clock_in_url: 'Attendance URL',
      copied_to_clipboard: 'Copied to clipboard'
    },
    accountsettings: {
      libro_general_integration_error: 'Error connecting Libro: {{error}}',
      libro_integration_error_503:
        'Libro is currently unavailable. Please try again later.',
      regular_work_hours: 'Regular work hours per week',
      shift_priority: 'Shift priority',
      longer_shifts: 'Longer shifts',
      chronologically: 'Chronologically',
      close_to_start_of_day_periods:
        'Closer to Start of Day Periods (Morning/Dinner)',
      download_the_zip_archive_below: 'Download the ZIP archive below',
      extract_the_files_to_your_maitred_folder_into:
        'Extract the files to your MaitreD folder into',
      folder_default_path_is_C_posera_maitred_prg:
        'folder. Default path is C:\\posera\\maitred\\prg.',
      the_default_path_is_the_following: 'The default path is the following.',
      default_path: 'C:\\posera\\maitred\\prg',
      ensure_they_are_not_in_a_default_pivot_folder_after_extraction:
        'Ensure they are not in a default Pivot folder after extraction.',
      to_re_download_the_files_unlink_the_integration_first:
        'To re-download the files, unlink the integration first.',
      view_instructions: 'View instructions',
      download: 'Download',
      starting_day_of_payroll_period: 'Starting day of payroll period',
      libro_integration_already_connected:
        'Libro integration already connected',
      libro_integration_added_successfully:
        'Libro integration added successfully',
      libro_integration_error:
        "Error while connecting to Libro's API. Check your internet connection and try again.",
      selectLanguage: 'Select language',
      timeFormat: 'Time format:',
      settingsMasterAccount: 'Master account settings',
      preferenceSettings: 'Preference settings',
      integrations_settings: 'Integrations settings',
      //mastersettings
      pleaseEnterValidEmailAddress: 'Please, enter valid email address',
      emailChanged: 'E-mail changed!',
      newPasswordIsTheSame:
        'New password is the same. Please enter new password.',
      shortPassword: 'Short password',
      passwordsDoNotMatch: 'Passwords do not match',
      passwordChanged: 'Password changed!',
      currentPasswordIsWrong: 'Current password is wrong',
      nameCantBeMoreThan:
        "Name can't be more than 15 chars, please, enter correct name",
      surnameCantBeMoreThan:
        "Surname can't be more than 15 chars, please, enter correct surname",
      changesSaved: 'Changes saved!',
      fileShouldBeNoMoreThan:
        'File should be no more than 5 Mb and jpeg or png format',
      yourCurrentEmail: 'Your current e-mail: ',
      changePassword: 'Change Password',
      oldPassword: 'Old Password',
      writeYourPasswordHere: 'Write your old password here',
      newPassword: 'New Password',
      writeYourNewPasswordHere: 'Write your new password here',
      repeatNewPassword: 'Repeat new password',
      repeatYourNewPasswordHere: 'Repeat your new password here',
      basicInformationAboutTheUser: 'Basic information',
      enterYourFirstName: 'First name',
      writeYourFirstName: 'Write your first name',
      avatar: 'Avatar',
      enterYourLastName: 'Last name',
      writeYourLastNname: 'Write your last name',
      save: 'Save',
      //preferencesettings
      selectTimezone: 'Select Timezone',
      selectCurrency: 'Select Currency',
      apply: 'Apply',
      weekStartingDay: 'Starting day of the week',
      note: "Note: this affects employees schedule interface, so don't change it often",
      timezone: 'Time zone',
      monday: 'Monday',
      tuesday: 'Tuesday',
      wednesday: 'Wednesday',
      thursday: 'Thursday',
      friday: 'Friday',
      saturday: 'Saturday',
      sunday: 'Sunday',

      intervalBetweenNonReccurentShifts:
        'Interval between non-reccurent shifts',

      schedule_generation_format: 'Schedule generation format',
      the_format_influences_your_schedule_generation:
        'The selected format will influence how your schedules will be generated',
      standard: 'Standard',
      seniority: 'Seniority',
      set_priority_order_by_subrole_in_employees_module:
        'Set a priority order by sub-role in the "Employees" module.',
      shifts_will_distribute_equally:
        'Shifts will be distributed more evenly, with the algorithm aiming to ensure that every employee receives at least one shift.',
      set_priority_order_by_role_in_employees_module:
        'Set a priority order by role in the "Employees" module.',
      algorithm_will_prioritize_max_days_per_week:
        'The algorithm will prioritize the maximum of days per week per employee according to the priority order. Employees at the bottom of the list may not receive any shifts.',
      warning: 'Warning',
      currently_you_are_using: 'Currently you are using',
      format_for_schedule_generation: ' format for schedule generation.',
      switching_to_the: 'Switching to the',
      format_will_implact_shifts_distribution:
        'format will significantly impact how shifts are distributed to employees.',
      you_will_need_to_set_priority_order_for_employees_by:
        'You will need to set a priority order for employees by',
      role: 'role',
      subrole: 'sub-role',
      in_the_employees_module: 'in the « Employees » module.',
      would_you_like_to_switch_to_this_format:
        'Would you like to switch to this format?',

      password: 'Password',
      verify: 'Verify',
      verified: 'Verified',
      select_company: 'Select your company',
      company_to_link: 'Company to link',
      association: 'Association',
      you_have_to_connect: 'You have to connect and verify your system',
      connectSystemAccount: 'Connect system account',
      connect_with: 'Connect with',
      connect_with_MaitreD: 'Connect with Maitre D',
      integration_added_successfully: 'Integration added successfully',
      user_code: 'Code',
      business_code: 'Business code',
      user_password: 'User password',
      cluster_pos_api_key: 'POS API key',
      cluster_pos_serial: 'POS Serial key',
      cluster_error: 'The provided data is incorrect',
      cluster_connected_successfully: 'POS found successfully',
      cluster_connection_failed: 'Cluster connection failed',
      acomba_pos_modal_header: 'Steps',
      acomba_pos_modal_step_one:
        "By enabling the integration, an Acomba ID field will be available in the employee's profile.",
      acomba_pos_modal_step_two:
        'Once this field is manually filled in by you, this identifier will be available in the by shift export.',
      no_company_found: 'No company found',
      myr_location_id: 'Your MYR location identifier',
      account_connected_to: 'Account connected to',
      powerpay_pos_modal_header: 'Steps',
      powerpay_pos_modal_step_one:
        "By enabling the integration, you'll have to fill in the Powerpay ID field in the employee's profile.",
      powerpay_pos_modal_step_two:
        "Once this field is manually filled in by you, you'll have to add the PayrollID and the BranchID in the payroll export popup.",
      powerpay_download_file_instructions: 'Powerpay instructions guide:',
      powerpay_download_file_instructions_link: 'instruction_guide.pdf',
      powerpay_powered_by_dayforce: 'Powered by Dayforce',
      myr_get_location_id: 'Find it in your ',
      myr_admin_panel: 'admin panel',
      myr_admin_panel_indication:
        'Please contact the MYR Onboarding team to activate the integration on Pivot:',
      myr_connection_details: {
        email: 'By email at ',
        company_name: 'Include your company name ',
        account_id: 'Your MYR Account ID → 20...',
        delay: 'The integration will then be active within 2 business days.'
      }
    },
    employeesettings: {
      employeeInteractionGroup: 'Employees can interact in the group',
      employeeInteractionMessages:
        'Employees can interact privately in messages',
      employeeInteractionWorkWith: 'Employees see who they’re working with',
      employeeSettings: 'Employee settings',
      exchanges: 'Exchanges',
      monthly: 'Monthly',
      yearly: 'Yearly',
      replacements: 'Replacements',
      employeeLimit: 'Employee request limits',
      requestDayOffLimit: 'Request day(s) off limit',
      daysOff: 'Days off',
      timeRequiredRequest: 'Time required before request',
      callInSick: 'Call in sick',
      emergency: 'Emergency call limit',
      notifyEmployees: 'Notify employees before shifts',
      noEnd: 'NO END shifts default duration',
      hour: ' hour',
      hours: ' hours',
      dailyHoursLessThan: 'Daily hours less than',
      excludeBreaksFromWeeklyHours: 'Exclude breaks from weekly hours',
      breakTimeIs: 'Break time is',
      deleteInterval: 'Delete interval',
      addInterval: '+ Add interval',
      maxDayHours: 'Max hours per day',
      noLimit: 'No limit',
      ON: 'ON',
      OFF: 'OFF'
    },

    pageError: {
      oopsPageNotFound: 'Oops! Page not found =(',
      backOrSupport: 'Back, or contact support',
      logOut: 'Log out'
    },
    signUp: {
      you_need_to_be_logged_in_to_verify_your_email:
        'You need to be logged in to verify your email',
      email_verified: 'Email verified',
      you_can_now_close_this_tab_and_return_to_the_previous_one:
        'You can now close this tab and return to the previous one',

      close_tab: 'Close tab',
      stageOne: 'Stage One',

      link_expired: 'Link has expired, a new link has been sent to your email',
      email_you_entered_is_not_correct: 'The email you entered is not correct',
      enter_code_or_contact_pivot:
        'Enter the code or contact the Pivot team to add a company',
      sorry_wrong_code_contact_support_or_try_again:
        'Sorry, wrong code. Please, contact support service or try again.',
      // should be removed
      registration: 'Registration',

      passwordsDoNotMatch: 'Passwords do not match',
      password: 'Password',
      repeatPassword: 'Repeat Password',
      repeatYourPasswordHere: 'Repeat your password here',
      toSignIn: 'Sign In',
      save: 'Save',

      signUp: 'Sign Up',
      welcome: 'Welcome !',
      welcomeToPivot: 'Welcome to Pivot !',
      connectEmail: 'Connect your email',
      connectWith: 'Connect with',

      // Seva - extract to createCompany after merg

      accountType: 'Account type',
      basicInfos: 'Basic infos',
      integrations: 'Integrations',
      createRoles: 'Create roles',

      selectYourAccountType: 'Select your account type',
      retail: 'Retail',
      restaurant: 'Restaurant',
      headOffice: 'Head office',
      singleLocation: 'Single location',

      basicInformations: 'Basic informations',
      pleaseFillInformations: 'Please, fill the following informations',
      whatsNameOfCompany: 'What is the name of the company ?',
      typeName: 'Type name',
      whatsAddressOfCompany: 'What is the address of the company ?',
      searchAddress: 'Search address',

      integrateWith: 'Integrate with',
      yourPOSsystem: 'Your POS system',
      yourReserveSystem: 'Your reservation system',

      create: 'Create',
      theFollowingStepCreateRoles:
        'The following step is where you must create all roles within your company',
      inYourCompany: 'in your company',
      exManagerWaiterBusboy: 'ex: Manager, Waiter, Busboy, etc.',

      toCreateRoleSearchName: 'To create your first role, search a role name',
      exWaiter: 'ex: Waiter',
      roleShiftsNotSameFuncSubRoleHelpYou:
        'Some shifts within a role might not have the same functions. Sub-roles help you classify in a better way your employees with specific abilities',
      exOpenClose: 'ex: Open, Close, etc.',

      placeAll: 'Place all',
      inTheirDepartment: 'in their department',

      rolesByDepartent: 'Roles by department',
      placeEachRolesInDepartments:
        'Place each roles in their respective departments',

      acronymsAppearOnScheduleShift:
        'Acronyms will appear on the schedule to represent the sub-role of the shifts. Per example, if an employee does the «Close», then it could be «CL»',
      exWOWCL: 'ex: W/O, W/CL, etc.',

      colorMakeAcronymOnSchedule:
        'The color selected will make the acronym appear with this color on the schedule',
      exWO: 'ex: W/O',
      WO: 'W/O',

      // Personal info
      personalInformation: 'Personal information',
      name: 'Name',
      surname: 'Surname',
      avatar: 'Avatar',
      pleaseAllowPopups:
        'It seems that your browser has blocked popups. To continue with social login, please allow popups for this site in your browser settings.'
    },
    forgotPassword: {
      missingDog: 'Missing @',
      missingDot: 'Missing .',
      messageSend: 'Message send',
      doNotWorry: 'Do not worry',
      weWillSendYouaNewPassword: 'We will send you a new password',
      withinTenMinutese:
        'You should receive an e-mail within ten minutes, check spam folder. Click the link and reset your password.',
      toSignIn: 'Sign In',
      //forgotPasswordModal
      checkYourEmail: 'Check your email',
      ifTheMessageDoesNotArrive:
        'If the message does not arrive to your email address within ten minutes, check spam folder, you should repeat the request or write to the support',
      sendAgain: 'Send again',
      send: 'Send',

      forgotPassword: 'Forgot password'
    },
    signIn: {
      no_account_associated_with_provider:
        "If you are a location owner, please go to Sign up. If you are a manager, try signing in with your email, as we couldn't find an account associated with this provider.",
      wrongPassword: 'Wrong password.',
      dontHaveAnAccountYet: "Don't have an accout yet?",
      signUp: 'Sign Up',
      password: 'Password',
      forgotThePassword: 'Forgot password?',
      login: 'Login',

      startNow: 'Start now !',
      signIn: 'Sign in',
      welcomeBack: 'Welcome back'
    },
    signInProviderHint: {
      google:
        'Your account appears to be already associated with a Google ID. Please sign in with Google.',
      facebook:
        'Your account appears to be already associated with a Facebook. Please sign in with Facebook.',
      apple:
        'Your account appears to be already associated with an Apple ID. Please sign in with Apple.',
      microsoft:
        'Your account appears to be already associated with a Microsoft ID. Please sign in with Microsoft.'
    },
    resetPass: {
      shortPassword: 'Short password',
      passwordsDoNotMatch: 'Passwords do not match',
      lastStep: 'Last Step',
      changeThePassword: 'Change the password and you can start working again',
      logo: 'logo',
      newPassword: 'New Password',
      writeYourNewPassword: 'Write your new password here',
      repeat_new_password: 'Repeat new password',
      continue: 'Continue',
      //resetpasswordModal
      excellent: 'Excellent',
      yourPasswordHasBeenSuccessfully:
        'Your password has been successfully changed. Now you can log in to your account',
      allRight: 'All right!',
      sendAgain: 'Send again',
      weSentYouAnEmail: 'We sent you an email',
      ifTheMessageDoesNotArrive:
        'If the message does not arrive to your email adress within ten minutes, check spam folder, you should repeat the request or write the support',
      back: 'Back',

      repeat_password: 'Repeat password',
      password: 'Password'
    },
    resetEmail: {
      verificationEmailSent: 'Verification email sent'
    },
    thankYouPage: {
      registration: 'Registration',
      stageOne: 'Stage One',
      createCompany: 'Create Company',
      stageTwo: 'Stage Two',
      createVacancy: 'Create Vacancy',
      stageThree: 'Stage Three',
      thankYouForRegistering: 'Thank you for registering',
      performThreeMore:
        'Perform two more simple steps: Registration of the company and placing the first vacancy'
    },
    createCompany: {
      writeDirectCodeForApply: 'Write direct code for apply',
      thisCodeAlreadyExist: 'This direct code already exist, try another',
      saved: 'Company settings saved'
    },
    supportModal: {
      post_your_problem: 'Post your problem',
      write_post_about_your_problem: 'Tell us about your problem here',
      yourLetterHasBeenSent: 'Your letter has been sent',
      thanksForYourLetter:
        'Thanks for your letter. Our support team will contact you as soon as possible. Check your e-mail.',
      ok_thanks: 'Ok, thanks',

      page_not_found_or_access_denied: 'Page not found or access denied',
      check_link_address: 'Check the link address.',
      sign_in_using_mobile_app:
        'If you are an employee, sign in using the Pivot mobile app on the App Store or Google Play.',
      contact_employer:
        'If you are a manager, contact your employer to have them grant you manager access so you can sign in to Pivot web.',
      contact_pivot_team:
        'If you still have issues, contact your employer or Pivot team directly ************.'
    },
    navbar: {
      schedules: 'Schedules',
      schedule: 'Schedule',
      structures: 'Structures',
      recurrency: 'Recurrency',
      payroll: 'Payroll',
      timeAttendance: 'Time & attendance',
      requests: 'Requests',
      chat: 'Chat',
      employees: 'Employees',
      roles: 'Roles',
      applicants: 'Applicants',
      group: 'Group',
      profile: 'Profile',
      settings: 'Settings',
      logOut: 'Log out',
      fix: 'Fix',
      skipForNow: 'Skip for now',
      youHave: 'You have',
      conflictingShiftsToConfirm: 'conflicting shifts to confirm'
    },
    hireForWork: {
      chooseCategoryAndSubcategory: 'Choose category and subcategory!',
      hireForWork: 'Hire',
      applicationSubmitted: 'Application submitted:'
    },

    schedule: {
      inactive: 'Inactive',
      on: 'on',
      some_employees_have_conflicting_shifts:
        'Some employees have conflicting shifts: ',
      published: 'Published',
      not_published: 'Not published',
      publish_schedule: 'Publish schedule',
      generate_schedule: 'Generate schedule',
      publish_schedule_for_week: 'Publish schedule for week:',

      must_have_at_least_one_employee:
        'You must have at least one employee in your company in order to generate a schedule.',
      pending_availability_on:
        'This is a pending availability taking effect on',
      pre_publish: 'Pre-publish',

      schedule_downloaded: 'Schedule downloaded',
      error_downloading_schedule:
        'Error downloading schedule, reload page and try again. If error persists, call Yannick ************',
      something_wrong_reload_or_call_admin:
        'Something went wrong. Reload page and try again. If error persists, call Yannick ************',
      manual: 'Manual',
      skip_generation: 'Skip generation',
      selectSubRole: 'sub-role',
      totalActualSales: 'Total actual sales',
      last_year_sales: 'Last year sales',
      last_week_sales: 'Last week sales',
      actualLaborCost: 'Actual labor cost',
      generalError: 'Something went wrong.',
      errorIn: 'There is an error for the input: ',
      morningSales: 'Morning Sales',
      endOfDaySales: 'End Of Day Sales',
      catererSales: 'Caterer Sales',
      laborCost: 'Labor Cost',
      theSalesProjectionOn: 'The sales projection on',
      catererSalesProjectionOn: 'Caterer Sales Projection on',
      isInvalid: 'is invalid.',
      allDay: 'all day',
      sliderWeek: 'Week',
      defaultSort: 'Default sort (as per schedule)',
      alphabeticalSort: 'Alphabetical sort',
      by_employees_compact: 'By employees (compact)',
      selectEmployeeOrder: 'Select employees order',
      week: 'Week',
      confirmed: 'Confirmed',
      confirm: 'Confirm',
      clean: 'Clean',
      chooseTime: 'Choose time',
      success: 'Success',
      chooseCategory: 'Choose category',
      youHaveNoEmployees: 'You have no employees',
      stShift: 'st Shift',
      addShift: 'Add Shift ',
      loading: 'Loading...',
      youHaveNoJobs: 'You have no jobs',
      worksSchedule: 'Employee schedules',
      scheduleOfInterviews: 'Interview schedules',
      youCanEeasilySwitchBetweenCalendars:
        'You can easily switch between calendars',
      allPositions: 'All positions',
      editSchedule: 'Edit Schedule',
      save: 'Save',
      doYouWantSaveThisCalendarAs: 'Do you want save this calendar as ',
      noCancel: 'Cancel',
      yesSave: 'Yes, save',
      changesToTheCalendar: 'Changes to the calendar ',
      haveBeenSaved: ' have been saved',
      ok: 'Ok',
      //editScheduleMain
      subcategoryAlreadySelected: 'Subcategory already selected',
      rolesNeeded: 'Roles Needed:',
      amount: '# of employees required: ',
      amountHold: '# of employees on-hold: ',
      edit: 'Edit',
      print: 'Print',
      next: 'Next',
      chooseSubcategory: 'Choose subcategory',
      time: 'Time:',
      beginningOfWork: 'Beginning of work',
      endOfWork: 'End of work',
      newRole: 'New Role:',
      addRole: 'Add Role',
      //editScheduleRightSide
      editManuallyByTheWeek: 'Edit manually by the week',
      editScheduleWithTemplates: 'Edit schedule with templates',
      switchBetweenCalendarsToManage:
        'Switch between calendars to manage templates.',
      youCanSaveTheSettingsAsaStructure:
        'You can save the settings as a structure to set this schedule to other shifts.',
      saveAsStructure: 'Save As Structure',
      writeScheduleName: 'Write Schedule Name',
      createStructure: 'Create structure',
      repeatEveryWeek: 'Repeat every week',
      youCanLoopTheRepetition: 'You can loop the repetition of the graph',
      //selectEmployees
      priorityList: 'Priority list',
      dragTheRightEmployeesIntoThisArea:
        'Drag the right employees into this area',
      toRemoveAnEmployeeFromThisList:
        'To remove an employee from this list, drag it to the left side of the screen',
      continue: 'Continue',
      addEmployees: 'Add Employees',
      removeFromThisList: 'Remove from this list',
      //tableHead
      employeesAndPositions: 'Employees',
      loadingEmployees: 'Loading employees, try again',
      editLabel: 'Edit label',
      applyLabel: 'Apply label',
      fillWorkTime: 'Fill beginning and end of work.',
      labelColor: 'Color is required',
      reviewSchedule: 'Review Schedule',
      labelsForShift: 'Labels For Shifts',
      editingSchedule: 'Editing Schedule',
      hintTextLabel:
        "A structure's names must represent a variable that influences the required amount of employees scheduled",
      hintTextLabelExample: 'Ex: Revenues, Customer traffic, etc.',
      newStructure: 'New structure',
      shift: 'shift',
      labelName: 'Label name',
      name: 'Name',
      label: 'Label ',
      apply: 'Apply',
      post: 'Post',
      andPositions: 'and positions',
      editManually: 'Edit manually',
      dayOfWeek: 'Day of the week',
      labels: 'Labels',
      newLabel: 'New Label',
      color: 'Color',
      selectColor: 'Select color',
      deleteStructure: 'Delete structure',
      duplicateStructure: 'Duplicate structure',
      duplicateShift: 'Duplicate shift',
      addToOftenUsed: 'Add to often used',
      st: 'st ',
      nd: 'nd ',
      rd: 'rd ',
      th: 'th ',
      search: 'Search...',
      subPosition: 'Sub-position',
      back: 'Back',
      generateSchedule: 'Generate schedule',
      paste: 'Paste',
      oftenUsed: 'OFTEN USED',
      otherLabels: 'OTHER LABELS',
      resetAllFills: 'Reset all fills',
      repeatPreviousWeek: 'Repeat previous week',
      posted: 'POSTED',
      notPosted: 'NOT POSTED',
      nothingYet: 'NOTHING YET',
      beginning: 'Beginning',
      status: 'Status',
      repeat: 'Repeat',
      deleteShift: 'Delete shift',
      applyChanges: 'Apply changes',
      postedSchedule: 'Posted schedules',
      dayStructures: 'Days structures',
      fixedSchedule: 'Fixed schedule',
      allEmployees: 'All employees',
      onlyTheseShifts: 'Only these shifts',
      shiftTime: 'Shift time',
      findReplacer: 'Find a replacer',
      byEmployees: 'By employees',
      byPositions: 'By positions',
      saveChanges: 'Save changes',
      buisnessOperationTimePeriod: 'Business operation time period(s)',
      createNewLabel: '+ Create new',
      morning: 'Morning',
      evening: 'Evening',
      lunch: 'Lunch',
      dinner: 'Dinner',
      night: 'Night',
      add: 'Add',
      addNewPeriod: 'Add new period',
      addNewLabel: 'Add new label',
      reset: 'Reset',
      delete: 'Delete',
      on_call: 'On-call',
      postSchedule: 'Post schedule',
      copy: 'Copy',
      deletingPeriod: 'Deleting periods will delete all labels in it',
      sureDeleteLabel: 'Are you sure you want to delete this labels?',
      'addEmployee(s)': 'Add employee(s)',
      endOpitonal: 'End(optional)',
      availableEmployees: 'Available employees',
      youMustFillAll: 'You must fill all',
      required: 'required',
      toSeeWhosAvail: "informations to see who's available",
      selectTime: 'Select time',
      day: 'Day',
      scheduledEmployees: 'Scheduled employees',
      'addEmployees+': 'Add employee(s)',
      pleaseFillData: 'Please fill data or close popover',
      pleaseSwitchToEdit: 'Please, switch to edit mode.',
      shifts_conflict: 'Shifts are conflicting, please fix',
      selectPosAndSubpos: 'Select position and sub-position',
      shiftsConflicting: 'Shifts are conflicting, please fix',
      removed: 'Removed',
      editMode: 'Edit mode',
      noEmployees: 'No employees',
      labelsWereSaved: 'Labels were saved',
      currentCompanyDoesNotHaveTimePeriod:
        'Current company does not have at least time periods and one label by time period, it is required for new schedule. Fill this info in DAY STRUCTURES',
      youMustSelectInfo: 'You must select the informations',
      'withAn*': "with an * to see who's available",
      select: 'Select...',
      sendRequest: 'Send request',
      end: 'End',
      applyDuplication: 'Apply duplication',
      update: 'Update',
      schedulePosted: 'SCHEDULE POSTED',
      overtime: 'Overtime',
      time_off: 'Time off',
      'employees(s)': 'Employee(s)',
      from: 'From',
      to: 'To',
      accetpedBy: 'Accepted by',
      employeeAvailabilities: 'Employee availabilities',
      //modals
      youHaventUpdatedChanges: 'You have not updated your changes...',
      areYouSureToQuit:
        'Are you sure you want to quit the edit mode without saving ?',
      yesQuit: 'Yes, quit',
      scheduleIsPosted: 'schedule is already posted',
      wouldYouApplyAndNotifyEmployees:
        'Would you like to apply these changes and notify concerned employees?',
      shiftYouModifyIsReccurent:
        'The shift you want to modify is a reccurent shift',
      modifyDefinitely: 'Modify definitely',
      modifyExceptionally: 'Modify exceptionally',
      sureDeleteShift: 'Are you sure you want to delete this shift?',
      deleteDefinitely: 'Delete definitely',
      deleteExceptionally: 'Delete exceptionally',
      yesDelete: 'Yes, delete',
      giveShift: 'Give shift',
      printSchedule: 'Print schedule',
      selectScheduleFormat: 'Select a schedule format',
      createNewSchedule: 'Create new schedule for week',
      // conflict modal
      whatsHappening: 'Here’s what’s happening',
      missingEmployees: 'Missing employees',
      conflict: 'Conflict',
      requiredConfilct: 'required',
      empl: 'empl. ',
      only: 'Only ',
      available: 'available',
      replacerRequest: 'Replacer request',
      ignore: 'Ignore',
      solution: 'Solution ',
      emergencyRequestWillNotify: 'Emergency request will notify',
      everyEmplInPosition: 'every employees in this position',
      youWIllReceiveAnswer: 'You will receive answers from your',
      employeesShortly: 'employees shortly',
      areYouSure: 'Are you sure that you want to',
      ignoreConflict: 'ignore this conflict ?',
      'missingShift/FixedSch': 'Missing shifts Label / Fixed Schedule',
      'label/FixedSch': 'Label / Fixed Schedule',
      byIgnoringIt: 'By ignoring it,',
      willNotBe: 'will NOT be',
      scheduledAt: 'scheduled at ',
      noMatchingLabel: 'No matching label',
      requiredNoMoreEmpl: 'Required no more empl',
      justForOnce: 'Just for once',
      definitely: 'Definitely',
      change: 'Change ',
      fixedschedule: 'fixed schedule',
      noAvailable: 'No available',
      dayOff: 'Day off',
      dayOfTheWeek: 'Day of the week',
      revenue: 'Revenue',
      conflicts: 'Conflicts',
      positions: 'Positions',
      searchEmployee: 'Search employee...',
      close: 'Close',
      total: 'Total',
      laborCostsOverview: 'Labor costs overview',
      laborCostHead: 'Labor cost',
      saveDraft: 'Save draft',
      resetSchedule: 'Reset schedule',
      unpublish: 'Unpublish',
      recurrencyStructure: 'Recurrency structure on',
      startingWeek: 'Starting week',
      'selectEmployee(s)': 'Select employee(s)',
      'selectedEmployee(s)': 'Selected employee(s)',
      selectWeek: 'Select a week',
      thisEmployeeScheduled: 'This employee is already scheduled on a ',
      hisCreateSch: 'his',
      recStructure: 'reccurency structure',
      byConfirmingTransferFixedWillDeleted: 'By confirming his transfer on ',
      willBeDeletedCreateSch: 'will be deleted.',
      availableEmployeesFor: 'Available employees for',
      weekRecurrency: 'weeks recurrency',
      nextStartingWeek: 'Next starting week',
      startWeek: 'Start week',
      addUser: 'Add Employee',
      changeStartingWeek: 'Change starting week for',
      schedule: 'Schedule',
      complete: 'Complete',
      incomplete: 'Incomplete',
      timePeriod: 'Time period',
      potentialAvailEmpl: 'Potential available employees',
      moving_shift: 'Moving shift',
      forwardOvertimeShift: 'Forward overtime shift',
      notAvailable: 'Not available',
      forwardShiftTransfer: 'Transfer',
      noEmployeesAvailable: 'No employee available',
      you_need_to_generate_at_least_one_role:
        'You need to generate at least one role',
      starting: 'Starting',
      ending: 'Ending',
      addTime: 'Add time',
      start: 'Start',
      never: 'Never',
      everyWeek: 'Every week',
      selectStartingWeek: 'Select a starting week',
      no_results_found: 'No results found',
      search_employee: 'Search employee...',
      select_dates: 'Select date(s)...',
      weekRecurrencyFr: ' ',
      weekRecurrencyEng: 'weeks recurrency',
      currentWeekEng: 'Current week',
      currentWeekFr: ' ',
      allDayTime: 'All day',
      allDayTimeShorten: 'All day',
      scheduleView: 'Schedule view',
      weekCreateSch: 'week',
      weeksCreateSch: 'weeks',
      draft: 'Draft',
      red: 'Red',
      pink: 'Pink',
      purple: 'Purple',
      deepPurple: 'Deep purple',
      indigo: 'Indigo',
      blue: 'Blue',
      lightBlue: 'Light blue',
      cyan: 'Cyan',
      teal: 'Teal',
      green: 'Green',
      lightGreen: 'Light green',
      lime: 'Lime',
      yellow: 'Yellow',
      amber: 'Amber',
      orange: 'Orange',
      deepOrange: 'Deep orange',
      brown: 'Brown',
      grey: 'Grey',
      blueGrey: 'Blue grey',
      darkGrey: 'Dark grey',
      morningPop: 'Morning',
      lunchPop: 'Lunch',
      dinnerPop: 'Dinner',
      nightPop: 'Night',
      days: 'Days',
      onCall: 'On - call',
      past: 'Past',
      createNewShift: 'Create new shift',
      nightPeriod: 'Night period',
      dayPeriod: 'Day period',
      revenues: 'Revenues',
      client: 'client',
      clickToFix: 'click to fix',
      selectPositionAndSubpositionFirst:
        'Select position and subposition first.',
      youHaveUnsavedChanges:
        'You have unsaved changes, you will lose these change if you leave this tab (Posted schedules)',
      unpublishConfirm:
        "Are you sure you want to unpublish this week's schedule?",
      are_you_sure_you_want_to_delete_selected_positions:
        'Are you sure you want to delete selected positions?',
      openShifts: 'Open shifts',
      open: 'Open',
      you_are_not_allowed_to_perform_this_action:
        'You are not allowed to perform this action',

      doenst_have_role_assigned: "doesn't have this role assigned",
      show: 'Show',
      giveShiftTo: 'Give shift to',
      noEmployeeSelected: 'No employees selected',
      deleteAll: 'Delete all',
      successfullyAdded: 'Successfully added',
      reccurentShifts: 'recurrent shifts',
      labelShifts: 'label shifts',
      missing: 'Missing',
      onHoldShifts: 'on-hold shifts',
      attestShifts: 'attest shifts',
      publishedSuccessfully: 'Published successfully',
      draftCreatedSuccessfully: 'Draft updated successfully',
      //welcome page
      welcomeToPivot: 'Welcome to Pivot !',
      pleaseSelectTypeAccount: 'Please select type of account',
      filialOffice: 'Branch office',
      headOffice: 'Head office',
      locations: ' Locations',
      account: 'Account',
      company: 'Company',

      //timeoff popover
      startingPopover: 'Starting',
      details: 'Details',
      acceptedBy: 'Accepted by :',
      wasAcceptedOn: 'Was accepted on :',
      reason: 'Reason:',
      reasons: 'Reasons:',
      cancel_time_off: 'Cancel',
      edit_time_off: 'Edit',

      //create timeoff
      create_time_off: 'Create',
      edit_time_off_title: 'Edit',
      timeOfType: 'Time of type',
      addEmployee: '+ Add employee',
      typeEmployeeName: 'Type employee name',
      send: 'Send',
      not_paid_time_off: 'Not paid time off',
      paid_time_off: 'Paid time off',
      selectPeriod: 'Select period...',
      what_type_time_off: 'What type of time off?',
      select_type: 'Select type',
      which_employee: 'Employee',
      when: 'Date',
      starts: 'Starts',
      ends: 'Ends',
      what_is_reason_time_off: 'Reason',
      optional: 'optional',
      type_reason_here: 'Type reason here',
      confirm_time_off: 'Confirm',
      update_time_off: 'Update',
      select_employee: 'Select employee',
      time_off_created: 'Time off created',
      time_off_error: 'Time off error',
      time_off_updated: 'Time off updated',
      shift_scheduled_error: 'A shift is already scheduled for that day',
      shift_draft_error: 'A shift draft already exists for that day',
      day_off_error: 'A day off is already scheduled for that day',
      fix_the_following_days:
        'Please resolve the following conflicts before proceeding:',
      //modal
      modal: {
        theSchedule: 'The schedule',
        isAlreadyPublished: 'is already published.',
        wouldYouApplyAndNotifyEmployees:
          'Would you like to apply latest changes and notify concerned employees?',
        byConfirmingSchedule: 'By confirming, the schedule',
        willBePublished: 'will be published.',
        areYouSurePublish:
          'Are you sure this schedule is ready to be published?',
        confrim: 'Confirm'
      },

      what_your_sales_projections_for_week:
        'What are your sales projections for the week of',
      das_calculation: 'DAS calculation',
      average_tips_short: 'Avr. tips',
      das: 'DAS',
      lastYear: 'Last year',
      lastWeek: 'Last week',
      projection: 'Projection',
      caterer_projections: 'Caterer projections',
      caterer_projection: 'Caterer projection',
      sales_projections: 'Sales projections',
      totalProjection: 'Total projection',
      generate: 'Generate',
      sales: 'Sales',

      detailedView: 'Detailed view',
      overview: 'Overview',
      compactedView: 'Compacted view',
      totalSalesProjections: 'Total sales projections',
      estimatedLaborCost: 'Estimated labor cost',
      salesProjection: 'Sales projection',
      projections: 'Projections',
      dailySales: 'Daily sales',
      closeTheDay: 'Close the day',
      dayClosed: 'Day closed',
      reconnectPos: 'POS',
      closingDay: 'Closing day',
      endOfDay: 'End of day',
      caterer: 'Caterer',
      salesByDay: 'Sales by day',
      youHaveSuccessfullyClosed: 'You have successfully closed',
      done: 'Done',
      wage: 'Wage',
      totalDasWage: 'Total DAS (Wage)',
      tips: 'Tips',
      tipsShort: 'Tips',
      totalDasTips: 'Total DAS (Tips)',
      dailyCostPerDay: 'Daily cost',

      laborCostSettings: 'Labor cost settings',
      placeEachRoleDepartment:
        'Place each roles in their department and indicate which',
      onesYouWantShowLaborTable: 'ones you want to show in labor cost table.',
      frontOfHouse: 'Front of House',
      backOfHouse: 'Back of House',
      management: 'Management',
      averageTips: 'Average tips %',
      MNGCost: 'MNG Cost',
      current_week: 'Current week',
      upcoming_week: 'Upcoming week',
      past_week: 'Past week',
      noSchedule: 'No schedule',

      today: 'Today',
      todayShorten: 'Today',
      totalCost: 'Total cost',
      weeklyCost: 'Weekly cost',
      dailyCost: 'Daily cost',
      costs: 'Costs',

      byFOH: 'By Front of House',
      byBOH: 'By Back of House',
      byMNG: 'By Management',
      byRoles: 'By roles',

      openShift: 'Open shift',
      addingEmployee: 'Adding employee',
      subRole: 'Sub-role',
      noStart: 'No start',
      noEnd: 'No end',
      unavailable: 'Unavailable',

      weekly_log: 'Weekly log',
      whatsNew: "What's new?",
      addLog: 'Add log',
      sureDeleteLog: 'Are you sure to delete this log?',
      noWeeklyLogs: 'No logs yet',
      date: 'Date',

      publish: 'Publish',
      recreate: 'Recreate',
      actions: 'Actions',
      theEndingOfShiftConflictBeginningShift:
        'The ending of 1st shift conflicts with the beginning of 2nd shift',
      includeOnCallShifts: 'Include "On-call" shifts',
      reservations: 'Reservations',
      cancellations: 'Cancellations',
      goToMyLibro: 'Go to my Libro',

      noteForShift: 'Note for the shift',
      typeNoteHere: 'Type a note here',

      time_off_canceled: 'Day off canceled',

      shifts: 'Shifts',
      confirmation: 'Confirmation',
      declined: 'Declined',
      calendar: 'Calendar',
      backToCurrentWeek: 'Back to current week',

      recurrency: 'Recurrency',
      move_shift: 'Move shift',
      mark_as_open: 'Mark as open',
      means_shift_no_longer_assigned_employee:
        'means that the shift will no longer be assigned to an employee',

      warning: 'Warning',
      employee_you_trying_schedule_already_scheduled:
        "The employee that you're trying to schedule is already scheduled during this time",
      what_would_you_do: 'What would you like to do ?',
      give_shift_anyway: 'Give shift anyway',

      trade_shift: 'Trade shift',
      select_position: 'Select position',

      expected_reservations_pre_text: ' ',
      expected: 'Expected',
      expected_reservations_hint_text:
        ' is the amount of clients that our Artificial Intelligence(AI) calculated there would be according to previous data.',
      recurrent_schedule: 'Recurrent schedule',
      roles: 'Roles',
      role: 'Role',
      generate_schedule_for_week: 'Generate schedule for week:',
      restart: 'Restart',
      emergency: 'Emergency',
      labor_cost_header: 'Labor cost',

      select_for_which_roles: 'Select for which role(s) you want to',
      the_schedule: 'the schedule...',

      all_roles: 'All roles',
      all: 'All',
      opened: 'Opened',
      closed: 'Closed',
      remaining: 'remaining',
      generated: 'Generated',
      not_generated: 'Not generated',

      you_successfully_pre_published_schedule:
        'You have successfully pre-published the schedule',
      your_schedule_has_been: 'Your schedule has been',
      your_schedule_has_been_created_in: 'Your schedule has been created in',
      prepublished: 'Pre-published',
      schedule_changes_are_pending_publish_again:
        'Schedule changes are pending. Please publish again to respond to the on-call shifts.',
      you_have_unpublished_changes:
        "You've made changes to a position that is already published, but these changes have not been published yet. They are saved as a draft, and you can publish them at any time.",
      in_draft: 'in draft',
      all_created: 'All created',
      all_published: 'All published',
      in_published: 'in published',
      created: 'created',

      select_period_type: 'Select a period type',
      days_in_row: 'Days in a row',
      split_days: 'Separate days',

      starting_at: 'Starting at',
      ending_at: 'Ending at',

      are_you_sure_you_want_to_cancel_this_time_off_request:
        'Are you sure you want to cancel this time off request?',
      yes_cancel: 'Yes, cancel',
      no: 'No',
      failed_to_remove_day_off_try_again_or_contact_support:
        'Failed to remove day off, try again. If the error persists, contact support.',
      request_sent_success: 'Request has been sent',
      error_sending_request: 'Error sending request',
      shift_deleted: 'Shift deleted',
      shift_with_the_same_start_and_end_already_exists:
        'A shift with the same start and end already exists',

      no_employees_assigned_to_this_role: 'No employees assigned to this role'
    },
    dashboard: {
      link_copied_to_clipboard: 'Link has been copied to clipboard.',
      accessToAccount: 'Access to account',
      totalLaborCost: 'Total costs',
      totalDistributedHours: 'Total Revenue For Week',
      laborCostGraph: 'Labor cost graph',
      detailedLaborCost: 'Detailed Labor Cost',
      addACompany: 'Add a company...',
      viewDashboardGraph: 'View Dashboard Graph',
      detailedlaborCostHead: 'Detailed Labor Cost',
      laborCostHeadPositions: 'Positions',
      laborCostHeadHead: 'Labor cost',
      total: 'TOTAL',
      laborCostHead: 'Labor cost',
      totalSales: 'Total sales',
      totalLaborCostStats: 'Total labor cost',
      openGraph: 'Open graph',
      pending: 'Pending...',
      headOfficeAccess: 'Head office access',
      noHeadOfficeAccess: 'No head office access yet...',
      invitation_success:
        'Invitation email has been sent. You can also copy the link and send it to the user.',
      generel_error:
        'Something went wrong. Please check internet connection and try again.',
      enter_valid_email: 'Please enter a valid email address',
      access_removed: 'Access has been removed',
      email_already_used: 'Email already used.',
      email_already_used_master_account:
        'Email already used to access a master account.',
      back_to_dashboard: 'Back to dashboard'
    },
    indexes: {
      1: '1st',
      2: '2nd',
      3: '3rd',
      4: '4th',
      5: '5th',
      6: '6th',
      plural: 'th'
    },
    scheduleEmployee: {
      from: 'from',
      current_availability: 'Current availability',
      pending_availabilities: 'Pending availabilities',
      forwardShift: 'Forward shift',
      tradeShift: 'Trade shift',
      shiftStart: 'Shift start',
      shiftEnd: 'Shift end',
      addShift: 'Add shift',
      availability: 'Availability',
      notAvailable: 'Not available',
      detailedView: 'Detailed view',
      compactedView: 'Compacted view',
      doYouWantTradeShifts: 'Do you want to trade these shifts ?'
    },
    schedulePosition: {
      availableEmployees: 'Available employees',
      allDayUppercase: 'ALL DAY',
      update: 'Update',
      add: 'Add',
      potentialAvailableEmployees: 'Potential available employees',
      transfer: 'Tansfer',
      followingShiftDoesntMatch:
        "The following shift doesn't match employee's availabilities",
      shiftStart: 'Shift start',
      shiftEnd: 'Shift end',
      available: 'Available',
      doYouStillWantGiveShift: 'Do you still want to give the shift ?',
      start: 'Start',
      addShift: 'Add shift',
      done: 'Done'
    },
    fixedSchedule: {
      morning: 'Morning',
      evening: 'Evening',
      dayOff: 'Day Off'
    },
    attendance: {
      last_name: 'Last name',
      first_name: 'First name',
      employee_number: 'Employee number',
      employee_order: 'Employee order',
      overtime_paid: 'Overtime paid',
      overtime_not_calculated: 'Not calculated',
      overtime_weekly: 'Weekly',
      overtime_biweekly: 'Bi-weekly',
      createdShift: 'Created shift',
      minute: 'Minute',
      by_week_format_title: 'Payroll_by_week',
      by_shift_format_title: 'Payroll_shifts',
      roundningTimeIs:
        "Rounding time is the time our system will automatically round empoyees' clock-ins and clock-outs",
      example: 'Example',
      roundingTimeAt: 'Rounding time at',
      clockAt: 'Clock at',
      rounded: 'Rounded',
      roundedAt: 'Rounded',
      whatIsRoundingTime: 'What is rounding time ?',
      apply: 'Apply',
      clickSolveConflicts: 'Click here to solve conflicting shifts',
      break: 'Break',
      to: 'to',
      roles: 'Roles',
      upcoming: 'Upcoming',
      currentPayPeriod: 'Current pay period',
      pastPeriod: 'Past period',
      readyToExport: 'Ready to export',
      conflictingShifts: 'conflicting shifts',
      allPositions: 'All positions',
      totalHours: 'Total hours',
      totalSalary: 'Total salary',
      week: 'Week',
      breaks: 'breaks',
      shiftConfirmed: 'Shift confirmed',
      confirmShift: 'Confirm shift',
      editShift: 'Confirm changes',
      in: 'In',
      out: 'Out',
      total: 'Total',
      today: 'Today',
      add_shift: 'Add shift',
      position: 'Position',
      subPosition: 'Sub-position',
      scheduled: 'Scheduled',
      notScheduled: 'Not scheduled',
      creatingShift: 'Creating shift',
      employeeNotScheduled: "Employee wasn't scheduled",
      deleteAll: 'Delete all',
      scheduledShift: 'Scheduled shift',
      nonScheduledShift: 'Non-scheduled shift',
      clockedIn: 'Clocked-in',
      clockedOut: 'Clocked-out',
      clocked: 'Clocked',
      hours: 'Hours',
      minutes: 'Minutes',
      delete: 'Delete',
      confirm: 'Confirm',
      changeDecision: 'Change decision',
      select: 'Select...',
      shift: 'shift',
      youMustSovleConflictsBeforeExport:
        'You must solve all conflicting shifts before exporting.',
      overlappingShifts: 'Please fix your overlapping shifts for this period',
      exportPayPeriod: 'Export pay period',
      howToExportPayPeriod: 'How would you like to export this pay period?',
      toPayrollSystem: 'To my payroll system',
      youMustLinkAccount: '*You must link your account*',
      linkMyAccount: 'Link my account',
      excelSpreadsheet: 'Excel spreadsheet',
      willFillHoursAndSalary:
        'Will fill automatically employees hours and salary',
      willDownloadExcel: 'Will download as an Excel document (.xls)',
      willDownloadXML: 'Will download as an XML document (.xml)',
      linked: 'Linked',
      payroll_system: 'Payroll system',
      reservation_system: 'Reservation system',
      cancelIntegration: 'Cancel integration',
      currentlyUnavailable: 'Currently unavailable',

      timesheet: 'Timesheet',
      laborCost: 'Labor cost',
      hoursWorked: 'Hours worked',
      splitTip: 'Split tip',
      splitTipShort: 'Split tip',
      cut: 'Cut',
      cutRounding: 'Cut % + rounding',
      tips: 'Tips',
      tipsShort: 'Tips',
      currentlyWorking: 'Currently working',
      payrollSettings: 'Payroll settings',
      payrollPeriod: 'Payroll period',
      biweekly: 'Biweekly',
      weekly: 'Weekly',
      roundingTime: 'Rounding time',
      tipsDistribution: 'Tips distribution',
      tipsDistributed: 'Tips distributed',
      eachTimePeriod: 'Each time period',
      cutDistribution: 'Cut distribution',
      salaryType: 'Salary type',
      value: 'Value',
      wageOnly: 'Wage only',
      wageTips: 'Wage + Tips',
      wageCut: 'Wage + Cut',

      totalSplitTip: 'Total split tip',
      workedHours: 'Worked hours',
      admissible: 'Admissible %',
      paidSplitTip: 'Paid split tip',
      paidWage: 'Paid wage',
      averageSalary: 'Average salary',
      viewShift: 'View shift',

      nextStartingWeek: 'Next starting week',
      tipsFormat: 'Tips format',
      normal: 'Normal',
      byTimePeriod: 'By time period',
      daily: 'Daily',
      cutFormat: 'Cut format',
      inCash: 'In cash',
      deductedFromPay: 'Deducted from pay',
      general: 'General',
      payrollFrequency: 'Payroll frequency',
      tipsDeclaration: 'Tips declaration',
      export: 'Export',
      salaryExport: 'Salary',
      overtime_export: 'Overtime',
      tipsExport: 'Tips',
      cutExport: 'Cut',
      declarationExport: 'Decl. export (cash)',
      tipsAndCut: 'Tips & Cut',

      clockedShift: 'Clocked shift',
      role: 'Role',
      salary: 'Salary',
      salaryMade: 'Salary made',
      sales: 'Sales',
      declarationShort: 'Decl.',
      totalDue: 'Total due',
      totalCut: 'Total cut',
      employerD_integration_error: 'Missing employerD integration',
      pos_systems: 'POS system',
      bonus: 'Bonus',
      cashSales: 'Cash sales',
      totalRevenue: 'Total revenue',
      due: 'Due',
      nethris_export_format: 'Nethris export format',
      employeurd_export_format: 'EmployeurD export format',
      will_be_download_as_a_text_document: 'Will be downloaded as a document',

      enterYourEmployeeID: 'Enter your employee ID to clock-in/out',
      forgotEmployeeID: 'Forgot Employee ID',
      attendance: 'Attendance',
      employeeID: 'Employee ID',
      enjoyYourShift: 'Enjoy your shift',
      youAreClockedIn: 'You are now clocked-in',
      youAreClockedOut: 'You are now clocked-out',
      hello: 'Hello',
      areYouReadyStartShift: 'Are you ready to start your shift ?',
      areYouReadyEndShift: 'Are you ready to end your shift ?',
      clockIn: 'Clock-in',
      clockOut: 'Clock-out',
      clockedInAt: 'Clocked in at',
      shiftDuration: 'Shift duration',
      thankYou: 'Thank you',
      youWillReceiveEmail:
        'You should receive an e-mail within ten minutes, check spam folder.',
      invalidUrl: 'Invalid URL',
      invalidEmployeeID: 'Invalid employee ID',
      by_shift_export_format: 'By shift export',
      payEvolutionExportFormat: 'Payevolution export format',
      powerpayExportFormat: 'Powerpay export format',
      archived: 'archived',
      error_four_shifts_exist:
        "You've already clocked in for four shifts today.",
      pay_export: {
        company: 'Company',
        employee_no: 'Employee no.',
        record_no: 'Record no.',
        period: 'Period',
        amount: 'Amount',
        quantity: 'Quantity',
        rate: 'Rate',
        week: 'Week'
      },
      export_modal: {
        simplified: 'Simplified',
        detailed: 'Detailed'
      },
      powerpay: {
        branch_id: 'Branch ID',
        payroll_id: 'Payroll Number',
        hourly: 'Hourly',
        amount: 'Amount',
        all_employees_must_have_a_powerpay_id:
          'All employees must have a powerpayId on their employee profile.'
      },
      integrations_settings: 'Integrations settings',
      select_systems_you_need_to_manage:
        'Select the systems you need to manage payroll, booking and POS',
      solutions: 'Solutions',

      link: 'Link',
      disconnect: 'Disconnect',
      connect: 'Connect',
      integrated: 'Integrated',
      integrate: 'Integrate'
    },
    payroll: {
      settings: 'Settings',
      hours: 'Hours',
      tips: 'Tips',
      cuts: 'Cuts',
      feature_coming_soon: 'Feature coming soon! 🔥',
      past_period: 'Past period',
      upcoming: 'Upcoming',
      current_period: 'Current period',
      notes: 'Notes',
      conflicting_shifts: 'Conflicting shifts',
      total_hours: 'Total hours',
      salaries: 'Salaries',
      filter: 'Filter',

      break: 'Break',
      worked_as: 'Worked as',
      select_role: 'Select role',
      no_roles_available: 'No roles available',
      add_break: 'Add break',
      unpaid: 'Unpaid',
      paid: 'Paid',
      total: 'Total',
      planned: 'Planned',
      clocked: 'Clocked',
      rounded: 'Rounded',

      week: 'Week',
      biweekly: 'Biweekly',
      select_new_period: 'Select a new period',
      reset_to_current: 'Reset to current',
      open_selected: 'Open selected',

      shift_not_planned_for_employee: 'Shift is not planned for this employee',
      employee_did_not_clock_out: 'Employee did not clock out',
      shift_was_less_than_3_hours: 'Shift was less than 3 hours',
      overlaps_with_shift: 'Overlaps with Shift',

      activity_log: 'Activity log',
      categories: 'Categories',
      initially: 'Initially',
      updated: 'Updated',
      modified: 'Modified',
      added: 'Added',
      deleted: 'Deleted',
      start_end: 'Start - End',
      role: 'Role',
      start: 'Start',
      end: 'End',
      delete_shift: 'Delete shift',
      add_shift: 'Add shift',
      unplanned: 'Unplanned',
      delete_break: 'Delete break',
      reclaim: 'Reclaim',
      you_are_all_caught_up: "You're all caught up!",
      no_conflicts: 'No conflicts',

      clocked_in_early: 'Clocked in early',
      the_employee_clocked_in_earlier_than_planned:
        'The employee clocked in earlier than planned',
      missing_end: 'Missing end',
      the_employee_did_not_clock_out_of_their_shift:
        'The employee did not clock out of their shift',
      clocked_out_late: 'Clocked out late',
      the_employee_clocked_out_later_than_planned:
        'The employee clocked out later than planned',
      unplanned_shift: 'Unplanned shift',
      the_clocked_shift_was_not_planned_for_this_employee:
        'The clocked shift was not planned for this employee',
      shift_too_short: 'Shift too short',
      shift_was_shorter_than_expected: 'Shift was shorter than expected',
      shift_under_3_hours: 'Shift under 3 hours',
      shift_was_under_3_hours: 'Shift was under 3 hours',

      you_have_claimed_shift_early_shift: 'You have claimed the shift early',
      you_have_deleted_shift: 'You have deleted the shift',
      you_have_approved_shift: 'You have approved the shift',
      shift_update_has_been_saved: 'Shift update has been saved',
      you_save: 'You save',
      with_planned_start_time: 'with planned start time',
      please_check_the_time_cards_below_to_ensure_proper_payroll_management:
        'Please check the time cards below to ensure proper payroll management',
      saved: 'Saved',
      you_saved: 'You saved',
      no_activity_to_date: 'No activity to date',
      activities: 'Activities',
      entire_period: 'Entire period',
      are_you_sure_you_want_to_delete_this_shift:
        'Are you sure you want to delete this shift?',
      are_you_sure_delete_break: 'Are you sure you want to delete this break?'
    },
    scheduleByHeader: {
      laborCost: 'Labor cost'
    },
    auth: {
      please_log_in_to_your_account_accept_invitation:
        'Please log in to your account to accept the invitation.',
      password_setup_successful: 'Password setup successfully',
      re_login:
        'You need to re-login to setup your password. Please check your email for the link.',
      login_successful: 'Login successful',
      setup_password: 'Setup a password for your account',
      signout: 'Signout',
      invalid_invitation_link: 'Invalid invitation link',
      wrong_account:
        'You are trying to accept invitation under wrong account. Please log in into correct account and try again.',
      invitation_accepted_successfully: 'Invitation accepted successfully'
    },
    export_attendance: {
      date: 'Date',
      to: 'to',
      name: 'Name',
      first_name: 'First name',
      last_name: 'Last name',
      empl_number: 'Employee number',
      start: 'Start',
      end: 'End',
      position: 'Position',
      total: 'Total (h)',
      pay_period: 'Pay period',
      hours: 'Hours',
      employees: 'Employees',
      rate: 'Rate',
      s1: 'S1',
      s2: 'S2',
      additional_salary: 'Additional salary',
      amount: 'Amount',
      shift_worked: 'Nb. shifts worked',
      acombaId: 'acomba ID',
      unpaid_break: 'Unpaid break',
      shift: 'Shift'
    }
  },

  // import { I18n } from 'react-redux-i18n'
  // {I18n.t('schedule.employeesAndPositions')}

  fr: {
    reconnect: {
      you_are_disconnected: 'Vous êtes déconnecté de',
      click_down_to_reconnect: 'Cliquez ci-dessous pour vous reconnecter',
      reconnect: 'Reconnecter',
      instructions: 'Instructions'
    },
    requests: {
      all_employees_have_declined: 'Tous les employés ont refusé',
      unanswered: 'Non répondu',
      recurrent_day_off: 'Quart fixe',
      recurrent_shift_conflict: 'Conflit avec un quart fixe',
      employees_will_be_taken_by_regular_shift:
        "l'employé(e) sera affecté(e) à un quart de travail régulier.",
      requests: 'demandes',
      title_timeoff_requests: 'demandes de congés',
      title_replacement_requests: 'demandes de remplacement',
      title_exchange_requests: 'demandes d’échanges',
      title_availabilitiy_requests: 'demandes de disponibilités',
      title_emergency_requests: 'demandes d’urgences',
      title_direct_requests: 'demandes directes',
      all_requests: 'Toutes les demandes',
      your_emergency_request: 'Votre demande d’urgence',
      new_requests: 'Nouvelles demandes',
      new: 'Nouveau',
      pending: 'En attente',
      archive: 'Archives',
      no_new_requests: 'Aucune nouvelle demande',
      no_pending_requests: 'Aucune demande en attente',
      no_archived_requests: 'Aucune demande archivée',
      requests_at_the_moment: 'pour le moment',
      shift_starts: 'Quart début',
      replacers: 'Remplaçants',
      cancel_shift: 'Annuler le quart',
      declined: 'Refusé',
      decline: 'Refuser',
      s_shift: 'Quart de',
      replaced_by: 'Remplacé par',
      accepted_by: 'Accepté par',

      times_off: 'Congés',
      replacements: 'Remplacements',
      exchanges: 'Échanges',
      availailities: 'Disponibilités',
      emergencies: 'Urgences',
      direct_inputs: 'Entrées directes',
      what_if: 'Impact',
      answer: 'Répondre',
      would_like_take_time_off: 'Aimerait prendre un congé',
      in_a_row: 'de suite',
      split: 'séparés',
      would_like_to_exchange_shifts: 'Aimerait échanger des quarts',
      after: 'Après',
      the_exchange: 'l’échange',
      would_like_to_be_replaced: 'Aimerait être remplacé',
      sent_at: 'Envoyé à',
      would_like_to_change_availabilities:
        'Aimerait changer ses disponibilités',
      starting: 'Débutant',
      no_conflict: 'Aucun conflit',
      show_availaibilites: 'Afficher les disponibilités',
      hide_availaibilites: 'Masquer les disponibilités',
      sent_you_direct_input_request:
        'Vous a envoyé une demande d’entrée directe',
      in_pending_for: 'en attente pour',
      refuse: 'Refuser',
      accept: 'Accepter',
      accepted: 'Accepté',
      refused: 'Refusé',
      you_have_accepted_request: 'Vous avez accepté la demande.',
      you_have_refused_request: 'Vous avez refusé la demande.',
      you_have_put_request_in_pending: 'Vous avez mis la demande en attente.',
      you_can_find_it_in_archive_tab:
        'Vous pouvez la trouver dans l’onglet archives.',
      you_can_find_it_in_pending_tab:
        'Vous pouvez la trouver dans l’onglet en attente.',
      dates: 'Dates',
      times: 'Temps',
      notify_again: 'Notifier',
      no_employee_has_answered_yet: "Aucun employé n'a encore répondu...",
      no_employees: 'Aucun employé',
      employee_that_already: 'Employés qui ont déjà',
      to_replace: 'de remplacer',
      working: 'ouvrables',
      days: 'Jours',
      hours: 'Heures',

      shifts_at_risk: 'quart(s) à risque',
      employees_available_for: 'employés disponibles pour',
      canceled_by_employee: 'Annulé par l’employé',
      by: 'Par',
      would_you_let_employee_know_why:
        'Aimeriez-vous donner une raison à l’employé ?',
      select_reason: 'Sélectionnez une raison',
      not_enough_employees: 'Manque d’employés disponibles',
      busy_date: 'Date très occupée (fêtes, etc.) ',
      abusive_timeoff: 'Utilisation abusive ou répétée des congés',
      requested_too_early: 'Demande reçue trop en avance',
      requested_too_late: 'Demande reçue trop tard',

      by_accepting_short: 'En',
      accepting: 'acceptant',
      this_request: 'cette demande..',

      shifts_dont_have_enough_employees: 'quart(s) n’ont pas assez d’employés'
    },
    integrations: {
      connect: 'Connecter',
      disconnect: 'Déconnecter',
      reconnect: 'Reconnecter',
      update: 'Mise à jour',
      payroll: {
        errors: {
          invalid_business_code: 'Votre code d’entreprise est invalide',
          allow_external_provider_on: 'Autoriser l’utilisateur de service sur ',
          user_code_not_matching_business_code:
            'Le code (nom) de l’utilisateur de service ne correspond pas à cette entreprise.',
          user_code_deactivated: 'Le code employé est désactivé',
          payroll_is_under_maintenance:
            ' est en maintenance, réessayez plus tard',
          update_user_type_to_service_user:
            'Veuillez changer le type de compte en compte de service',
          allow_user_account_to_connect_to_the_api:
            'Autoriser le compte utilisateur à se connecter à l’API sur ',
          account_locked:
            'Votre compte est verrouillé, contactez votre administrateur de paie',
          expired_password:
            'Le mot de passe de l’utilisateur de service a expiré sur ',
          invalid_password:
            'Le mot de passe de l’utilisateur de service est invalide, vérifiez auprès de votre administrateur de paie',
          invalid_password_attempt_left_1:
            'Le mot de passe de l’utilisateur de service est invalide, il reste 1 tentative, ensuite l’utilisateur sera verrouillé. Contactez votre administrateur de paie.',
          invalid_app_id:
            'Ce code est invalide, le code est le nom de l’utilisateur de service Pivot',
          allow_pivot_on: 'Autoriser PIVOT comme utilisateur de service sur ',
          language_error: 'Erreur de langue'
        }
      },

      you_have_been_disconnected_could_affect_procedures:
        'Vous avez été déconnecté. Cela pourrait affecter certaines procédures.'
    },
    common: {
      please_read_and_confrim: 'Veuillez Lire et Confirmer',
      please_review_terms_and_conditions:
        'Pour continuer, veuillez examiner le document des Conditions d’utilisation. Vous devez confirmer que vous avez lu et compris le contenu avant de procéder.',
      view_document: 'Voir le document',
      i_have_read_and_understood_the_terms_and_conditions:
        'J’ai lu et j’accepte les Termes et Conditions.',
      help: 'Aide',
      new_shift: 'Nouveau quart de travail',
      email: 'Courriel',
      write_your_email_here: 'Écrivez votre courriel ici',
      incorrect_email: 'Courriel incorrect',
      password: 'Mot de passe',
      repeatPassword: 'Répéter le mot de passe',
      new_version_released: 'Une nouvelle version a été publiée.',
      would_you_like_to_reload_the_app_to_get_the_latest_version:
        "Voulez-vous recharger l'application pour obtenir la dernière version ?",
      confirm: 'Confirmer',
      cancel: 'Annuler',

      save: 'Sauvegarder',
      save_shorten: 'Sauver',
      send: 'Envoyer',
      back: 'Retour',
      skip: 'Sauter',
      add: 'Ajouter',
      open: 'Ouvrir',
      apply: 'Appliquer',
      next: 'Suivant',
      close: 'Fermer',
      create: 'Créer',
      continue: 'Continuer',
      submit: 'Soumettre',
      employees: 'Employés',

      addPhoto: 'Ajouter une photo',
      uploading: 'Téléchargement',
      noFileSelected: 'Aucun fichier sélectionné',
      or: 'ou',
      error_general:
        "Quelque chose s'est mal passé, veuillez recharger la page et réessayer. Notre équipe a été informée de ce problème.",

      step: 'Étape',
      nextStep: 'Étape suivante',
      optional: 'optionnel',
      connect: 'Connecter',
      skipStep: 'Suivant',
      start: 'Débuter',
      done: 'Terminé',
      roles: 'Rôles',
      role: 'Rôle',

      search_employees: 'Rechercher des employés',
      shift: 'Quart',
      duration: 'Durée',
      day: 'Jour',
      starts: 'Début',
      ends: 'Fin',
      end: 'Fin',
      show: 'Afficher',
      undo: 'Annuler',
      reason: 'Raison',
      no_reason: 'Aucune raison',
      hide: 'Masquer',
      before: 'Avant',
      new: 'Nouveau',
      availability: 'Disponibilité',
      available: 'Disponible',
      unavailable: 'Indisponible',
      all_day: 'Journée complète',
      all_day_shorten: 'Journée',
      not_available_abbreviation: 'N/D',
      time_off: 'Congé',
      english: 'Anglais',
      french: 'Français',

      reconnect_pos: 'Vous devez reconnecter votre POS',
      are_you_sure_you_want_to_delete: 'Êtes-vous sûr de vouloir supprimer',
      no_cancel: 'Non, annuler',
      yes_remove: 'Oui, enlever',
      day_shorten: 'j',
      hour_shorten: 'h',
      maximum_shorten: 'Max',
      year: 'année',
      hours: 'heures',

      support: 'Aide',
      no_employees_found: 'Aucun employé trouvé',

      week_shorten: 'S',
      hours_shorten: 'H',
      minutes_shorten: 'Min',
      select: 'Sélectionner',
      delete: 'Supprimer',
      approve: 'Approuver'
    },
    cities: {
      montreal: 'Montréal',
      quebec_city: 'Ville de Québec',
      philadelphia: 'Philadelphie'
    },
    userGuide: {
      welcome_user: 'Nouveau Utilisateur',
      new_account_creation_steps: "Création d'un nouveau compte",
      download_mobile_app: "Téléchargez l'application",
      get_started: "C'est ma première fois !",
      click_on: 'Cliquez sur',
      fill_all_the_steps: 'Remplissez les étapes',
      to: 'à',
      enter_code: 'Entrez le code',
      request_sent: 'Demande envoyée',
      welcome_to_pivot: 'Bienvenue sur Pivot !',
      new_user_steps: 'Étapes nouvel utilisateur',
      download: 'Télécharger',
      qr_code: 'QR code',
      user_guide_downloaded: "Guide d'utilisation téléchargé avec succès",
      error_downloading_user_guide:
        "Erreur lors du téléchargement du guide d'utilisation. Si l'erreur persiste, veuillez contacter Yannick au ************",
      scan_me: 'Scannez moi !',
      qr_code_downloaded: 'QR code téléchargé avec succès',
      error_downloading_qr_code:
        "Erreur lors du téléchargement du QR code. Si l'erreur persiste, veuillez contacter Yannick au ************",
      select_language_to_download: 'Sélectionnez la langue à télécharger',
      new_user_steps_filename: 'Étapes nouvel utilisateur'
    },
    group: {
      fromPersonalAccount: 'De mon compte personnel',
      writeaNewPost: 'Ecrire un nouveau message ...',
      photo: 'Photo',
      noPostsHereYet: 'Aucun message ici, soyez le ',
      first: 'premier',
      allPosts: 'Tous les publications',
      companyPosts: 'Publications de la compagnie',
      employeesPosts: 'Publications des employés',
      thePostWasRemoved: 'La publication fut supprimée',
      restore: 'Restaurer',
      loading: 'Chargement...',
      comments: 'Commentaires',
      deletePost: 'Supprimer',
      hideComments: 'Masquer les commentaires',
      seeComments: 'Voir commentaires',
      writeaNewComment: 'Écrire un commentaire...',
      commentSaved: 'Commentaire enregistré!',
      postSaved: 'Publication sauvegardée!',
      document: 'Document',
      click_to_open: 'Cliquez pour ouvrir',
      writeComment: 'Écrire un commentaire',
      //modal
      close: 'Fermer',
      addFile: 'Ajouter le fichier',
      drag_file_here_or: 'Glisser un document ici ou ',
      select_file: 'sélectionner un fichier',
      deleteComment: 'Supprimer',
      missing: 'Personnes manquantes',
      viewers: 'VUES',
      commentRemoved: 'La commentaire fut supprimée ',
      emptyComment: "Impossible d'enregistrer un commentaire vide",
      unsupported_file_type: 'Type de fichier non supporté',
      max_file_size_is_50_mb:
        'La taille maximale du fichier est de 50MB. Contactez le support si nécessaire.'
    },
    jobs: {
      selectName: 'Sélectionnez le nom',
      status: 'Statut',
      loading: 'Chargement...',
      addRole: 'Ajouter un rôle',
      //jobslist
      addNewPosition: 'Ajouter un poste',
      newPosition: 'Nouveau poste',
      position: 'Rôle',
      positions: 'Rôles',
      //subcatComp
      description: 'Description:',
      nameOfTheJob: 'Nom du poste',
      //categoryItem
      delete: 'Supprimer',
      thisCategoryHasEmployees:
        "Cette catégorie a des employés. Déplacez ou supprimez-les d'abord",
      cantDeleteLastSubposition:
        'Impossible de supprimer la dernière sous-position. La position doit avoir au moins une sous-position',
      thisSubpositionHasEmployees:
        "Cette sous-section a des employés. Déplacez ou supprimez-les d'abord",
      addSubPosition: '+ Ajouter une sous-poste',
      //categoryHeader
      newSubPosition: 'Nouvelle Subposition',
      back: 'Arrière',
      hiring: 'Disponible',
      notHiring: 'Occupé',
      save: 'Sauvegarder',
      add: 'Ajouter',
      close: 'Fermer',
      edit: 'Modifier',
      editJob: 'Modifier le poste',
      //categoryComp
      salary: 'Salaire de base',
      hireForaFirstJob: 'Embaucher pour un premier emploi?',
      numberOfYearsExperience: 'Nombre d’années d’expérience:',
      nameOfTheSubPos: 'Nom du sous-titre:',
      experience: 'Expérience:',
      //jobDescription
      nameIsRequired: 'Le nom est requis',
      subpositionNameIsRequired: 'Le nom de la sous-couche est obligatoire',
      descriptionIsRequired: 'La description est requise',
      salaryIsRequired: 'Le salaire est requis',
      acronymIsRequired: 'Acronym est requis',
      nameAndDescriptionIsRequired:
        'Le nom et la description sont obligatoires',
      departmentIsRequired: 'Le département est requis',
      Monday: 'Lundi',
      Tuesday: 'Mardi',
      Wednesday: 'Mercredi',
      Thursday: 'Jeudi',
      Friday: 'Vendredi',
      Saturday: 'Samedi',
      Sunday: 'Dimanche',
      sureDeletePosition: 'Êtes-vous sûr de vouloir supprimer cette position?',
      sureDeleteSubPosition:
        'Êtes-vous certain de vouloir supprimer ce sous-poste?',
      positionName: 'Nom du poste ',
      subPositions: 'Sous-rôles',
      acronyms: 'Acronymes',
      acronymAppearsSchedule: 'Apparaît sur l’horaire',
      acronymNotAppearsSchedule: 'N’apparaît pas sur l’horaire',
      deletePosition: 'Supprimer',
      experienceRequired: 'Expérience requise',
      years: ' années',
      hiringFirstJobEmployees: 'Embaucher des premiers employés ',
      lookingAvailabilities: 'Rechercher les disponibilités',
      yes: 'Oui',
      no: 'Non',
      showOnSchedule: "Montrer à l'horaire",
      doesNotShowOnSchedule: 'N’apparaît pas sur l’horaire',
      addManagersTitle: 'Gestionnaires - %{role}',
      'role(s)': 'Poste(s)',
      managerAccessFor: 'Accès gestionnaire',
      searchRole: 'Recherche rôle...',

      roles: 'Roles',
      newRole: 'Nouveau role',
      noManagers: 'Aucun gestionnaire',
      managerAccess: 'Accès gestionnaire',
      editManager: 'Modifier',
      roleName: 'Nom du rôle',
      basicSalary: 'Salaire de base',
      subRoles: 'Sous-rôles',
      subRole: 'Sous-rôle',
      addSubRole: 'Ajouter sous-rôle',
      searchEmployee: 'Chercher un employé...',
      unselectedEmployees: 'Employés non sélectionnés',
      selectedEmployees: 'Employé(s) sélectionné',
      unselectedRoles: 'Rôle(s) non sélectionnés',
      selectedRoles: 'Rôle(s) sélectionné',
      emptyPositionWarning:
        "Impossible de mettre à jour l'ordre car il manque un nom à l'un des rôles ou sous-rôles",
      colors: 'Couleurs',
      color_off: 'Couleur off',
      color_on: 'Couleur on',

      documents: 'Documents',
      addDocument: 'Ajouter',
      signature: 'Signature',
      date: 'Date',
      dragAndDrop: 'Glisser déposer',
      confirm: 'Confirmer',
      documentImport: 'Importation de documents',
      successfullyUploaded: 'Téléversé avec succès',
      signatureDate: 'Signature / date',
      addSignatureDate: 'Ajouter signature / date',
      importOrDragFileHere: 'Importez ou faites glisser un fichier ici',
      import: 'Importer',

      reset: 'Réinitialiser',
      changesSaved: 'Modifications enregistrées',
      deleteDocument: 'Supprimer',
      areYouSureToDeleteDocument:
        'Voulez-vous vraiment supprimer ce document ?',

      additionalSalary: 'Salaire additionnel',
      deleteRole: 'Supprimer le rôle',
      hourly: 'horaire',
      perShift: 'par quart'
    },
    components: {
      Availabilities: {
        availabilities: "Disponibilités de l'employé",
        day: 'Jour',
        evening: 'Soir',
        night: 'Nuit',
        preview: 'Aperçu',
        shift: 'Quart',
        beginning: 'Début',
        end: 'Fin',
        allDay: 'Toute la journée',
        allDayHead: 'Journée complète',
        preferences: 'Préférences',
        available: 'Disponible'
      }
    },
    employees: {
      integration_requires_reconnection:
        'intégration nécessite une reconnexion',
      no_results_found: 'Aucun résultat trouvé',
      did_not_return_any_employees_check_integration_settings:
        'aucun employé n’a été retourné. Veuillez vérifier les paramètres de l’intégration.',
      not_allowed_to_add_delete_this_position:
        'Non autorisé à ajouter/supprimer ce poste',
      yearly_salary_cant_be_applied_when_having_multiple_yearly_salaries:
        "Le salaire annuel ne peut être appliqué que lorsqu'il n'y a qu'un seul salaire annuel",
      nethrisId: 'Nethris ID',
      totalEmployees: 'Total employés',
      maxHoursPerWeek: 'Max. heures par semaine',
      maxDaysPerWeek: 'Max. jours par semaine',
      priorityList: 'Ordre de priorité',
      loadingEmployees: 'Chargement des employés...',
      noEmployeesInThisPosition: 'Aucun employé dans cette position',
      selectSubpositionYouWantToRemove:
        'Veuillez sélectionner la sousposition que vous souhaitez supprimer',
      otherPositionsOfTheEmployee: 'Autres rôles occupés:',
      dateOfHiring: 'Date d’embauche:',
      salary: 'Salaire',
      statistics: 'Présence:',
      numberOfShiftExchanges: 'Échanges de quarts',
      numberOfReplacements: 'Remplacements',
      numberOfDaysOff: 'Congés',
      selectPositionAndSubpositionFirst:
        "S'il vous plaît, sélectionnez d'abord la position et la sous-position",
      loading: 'Chargement...',
      youHaveNoPositions: "Vous n'avez pas de positions",
      createaPosition: 'Créer un poste',
      moveToAnotherPosition: 'Passer à une autre position',
      addAnotherPosition: 'Ajouter un autre poste',
      removeFromThisPosition: 'Retirer de cette position',
      removedFromThisPosition: 'retirer de cette position',
      positions: 'Rôles',
      addNewEmployee: 'Ajouter un nouvel employé',
      areYouSureToWantToRemove: 'Êtes-vous sûr de vouloir supprimer',
      areYouSureRemoveEmployee:
        'Êtes-vous certain de vouloir retirer cet employé du groupe?',
      employeeWillMoveToArchive:
        'En faisant cela, il se retrouvera dans les archives.',
      yesArchive: 'Oui, archiver',
      fromThisPosition: 'de cette position',
      noCancel: 'Annuler',
      yesRemove: 'Oui, enlever',
      youCanRecoveryThisEmployeeOnPage:
        'Vous pouvez récupérer cet employé sur la page',
      applicantsDisapproved: '«Demandeurs / Refusés»',
      disapproved: 'Refusé',
      allRight: "D'accord!",
      editRoles: 'Modifier les rôles',
      editEmployee: 'Modifier un employé',
      hired: 'Embauché le',
      applyChanges: 'Sauvegarder',
      noPositionsYet: 'Pas encore de poste',
      save: 'Sauvegarder',
      employeesEmail: "Courriel de l'employé",
      employeesAvailabilities: "Disponibilités de l'employé",
      employeesPositions: 'Rôles occupés par l’employé',
      createAnEmployee: 'Créer un employé',
      firstName: 'Prénom',
      lastName: 'Nom de famille',
      typeHere: 'Ecrire ici...',
      enterEmail: 'Entrez courriel ici...',
      maxHours: 'Max heures par semaine',
      days: 'jours',
      hours: 'heures',
      hour: 'heure',
      roles: 'Rôles',
      requests: 'Demandes',
      apply: 'Sauvegarder',
      salaryPerHour: 'Salaire horaire',
      preferredNumberShifts: 'Préféré # de quarts',
      maximumNumberShifts: 'Maximum # de quarts',
      sendAgain: 'Envoyer à nouveau',
      invitationPending: 'invitation en attente...',
      sendInvitation: 'Envoyer une invitation',
      employeeAvailabilitiesUpdated:
        "Les disponibilités de l'employé ont été mises à jour",

      employeeFile: 'Dossier salarié',
      active: 'Actif',
      archived: 'Archivé',
      generalInformations: 'Informations générales',
      archiveEmployee: 'Archiver cet employé',
      fullName: 'Nom et prénom',
      employeeNumber: "Numéro d'employé",
      phoneNumber: 'Téléphone',
      birthDate: 'Date de naissance',
      dateHired: "Date d'embauche",
      homeAddress: 'Adresse du domicile',
      emergencyContact: "Personne à contacter en cas d'urgence",
      documents: 'Documents',
      incomplete: 'Incomplet',
      required: 'Requis',
      notSeen: 'Pas vu',
      completed: 'Complété',
      veloceId: 'Veloce ID',
      acombaId: 'acomba ID',
      lightspeedId: 'Lightspeed ID',
      push_notifications_on_mobile_app:
        'Notifications push sur l’application mobile',
      enabled: 'Activées',
      disabled: 'Désactivées',

      roleS: 'Poste(s)',
      effectiveStarting: 'Effectif',
      hourly: 'horaire',
      perShift: 'par quart',
      yearly: 'annuel',
      not_set: 'pas encore défin',
      failedToApplyChanges: "Échec de l'application des modifications",
      searchEmployee: 'Chercher un employé',
      all_roles: 'Tous les rôles',
      selectedEmployee: 'Employé sélectionné',
      priorityListOrder: 'Ordre de priorité',
      inactive: 'Inactif',
      rightAway: 'Tout de suite',
      pending_changes: 'Changements en attente',
      employerDId: 'EmployeurD ID',
      yearly_rate_less_than_100:
        'Voulez-vous utiliser le salaire horaire au lieu de la salaire annuelle?',
      hourly_rate_more_than_100:
        'Voulez-vous utiliser la salaire annuelle au lieu de la salaire horaire?',
      employee_has_been_created: 'L’employé a été créé',
      you_are_not_allowed_to_select_this_position:
        'Vous n’êtes pas autorisé à sélectionner cette position',
      search_veloce_employee_placeholder: 'Rechercher (min 3 cars)...',
      veloce_can_search_by_name_adress_phone:
        'Vous pouvez rechercher un employé par son nom ou prénom. Entrez au moins 3 caractères.',
      no_results: 'Aucun résultat',
      employee_name_and_surname_are_required:
        'Le nom et le prénom de l’employé sont requis'
    },

    addEmployee: {
      no_roles: 'Aucun poste sélectionné',
      name_and_surname_should_contain_only_eng_or_fr_letters:
        'Le nom et le prénom ne doivent contenir que des lettres en anglais ou en français'
    },
    message: {
      no_matches_found: 'Aucun résultat trouvé',
      today: 'Aujourd’hui',
      file_size_error: 'La taille du fichier ne doit pas dépasser 5 Mo',
      sentAttachment: 'A envoyé un attachement',
      newChat: 'Nouvelle conversation!',
      loading: 'Chargement...',
      //dialogues
      dialogs: 'Conversations ',
      noChatsFound: 'Aucune conversation trouvée',
      //components=>deleteDialogModal
      areYouSureYouWantDeleteHangout:
        'Êtes-vous sûr de vouloir supprimer cette conversation?',
      noCancel: 'Annuler',
      yesRemove: 'Oui, supprimer',
      //messages
      noMessagesToDisplay: 'Aucun message à afficher',
      loadMore: ' charger plus ',
      youHaveNoEmployees: "Vous n'avez aucun employé",
      fired: 'archivé',
      notReadYet: 'Pas encore lu',
      at: 'à',
      read: 'Lu',
      sent: 'Envoyé',
      justNow: "à l'instant",
      beTheFirstToSend: 'Soyez le premier à envoyer un message...',
      selectConversation: 'Sélectionnez une conversation',
      deleteConversation: 'Supprimer',
      you: 'Moi',

      searchConversation: 'Recherchez une conversation...',
      newGroupChat: 'Nouvelle discussion de groupe',
      edit_group_chat: 'Modifier la discussion de groupe',
      online: 'En ligne',
      offline: 'Hors-ligne',
      ago: ' ',
      editGroup: 'Modifier',
      attachFiles: 'Joindre un(des) fichier(s)',
      attachPhotos: 'Joindre une(des) photo(s)',
      message: 'Message',
      howToNameYourGroupChat:
        'Comment souhaitez-vous nommer votre nouveau chat de groupe ?',
      typeName: 'Tapez un nom',
      newGroupChatWith: 'Nouvelle discussion de groupe avec',
      selectedRolesEmployees: 'Rôle(s) / employé(s) sélectionné(s)',
      noOneSelected: "Personne n'a sélectionné",
      searchEmployee: 'Chercher un employé',
      readAt: 'Lu à',
      seen: 'Vu',
      delivered: 'Envoyé',

      was_online: 'En ligne'
    },
    applicants: {
      priorityList: 'Applicants',
      all: 'Tout',
      loading: 'Chargement...',
      thereIsNoApplicantsAtTheMoment: 'Aucun applicant pour le moment',
      age: 'Âge:',
      languages: 'Langues:',
      contact: 'Contact:',
      phone: 'Téléphone:',
      address: 'Adresse:',
      workExperience: "L'expérience professionnelle:",
      highestScholarly: 'Plus haut savant:',
      position: 'Rôles',
      unsorted: 'Non trié',
      interview: 'Entrevue',
      interview_on: 'Interview le',
      pastInterview: 'Entrevues passées',
      disapproved: 'Rejetés',
      directInput: 'Entrée directe',
      archive: 'Archiver',
      //disapproved
      isUnsorted: ' est Non triée maintenant',
      isDeleted: ' est supprimé',
      deleteForever: 'Supprimer pour toujours',
      reestablish: 'Rétablir',
      //interview
      isDisapproved: ' est refusé',
      reject: 'Rejeter',
      undo: 'Annuler',
      //pastinterview
      hireForWork: 'Ajouter au groupe',
      //interviewsuccess
      allRight: "D'accord!",
      excellent: 'Excellent',
      yourInterviewIsScheduledTheContestant:
        'Votre entrevue est prévue Le participant sera informé dans un proche avenir',
      //assigninterview
      chooseDateAndTime: "Choisissez la date et l'heure",
      pleaseChooseHowToInterview:
        "S'il vous plaît, choisissez comment interviewer",
      interviewWith: 'Interview avec ',
      ready: ' prêt',
      selectDate: 'Date',
      writeShortFescriptionHere: 'Ecrire une courte description ici',
      scheduleTheInterviewWith: 'Céduler une entrevue avec ',
      time: 'Temps:',
      howToInterview: 'Type d’entrevue',
      message: 'Message:',
      startOfInterview: 'Début de l’entrevue',
      onThePhone: 'Au téléphone',
      atTheMeeting: 'En magasin',
      skype: 'Skype',
      hintText:
        'Vous pouvez changer l’ordre des applicants - déplacez avec votre curseur',
      cancelInterview: 'Annuler Interview',
      assignInterview: 'Attribuer Entrevue',
      interviewDate: "date de l'interview ",
      filter: 'Filtre',
      contentFilter: 'Filtre de contenu',
      availabilities: 'DISPONIBILITÉS',
      skills: 'COMPÉTENCES',
      lastSchoolFrequented: 'Dernière école fréquentée',
      spokenLanguages: 'Langues parlées',
      levelAchieved: 'Niveau atteint',
      driverLicence: 'Permis de conduire',
      non: 'non',
      liveAt: 'Vivre à',
      experiences: 'EXPÉRIENCES',
      references: 'LES RÉFÉRENCES',
      presonal: 'PERSONNELLE',
      discription: 'DESCRIPTION',
      applicantFilter: 'Applicant filtrent',
      scolarityLevelAchieved: 'Niveau savant atteint',
      requiredExperience: 'Expérience requise',
      durationYear: 'Durée (année)',
      distance: 'Distance',
      applyFilter: 'Appliquer le filtre',
      required: 'Requis',
      notRequired: 'Non requis',
      date: 'Date',
      positions: 'Rôles',
      appliedOn: 'Appliqué sur'
    },
    notifications: {
      availabilities: 'Disponibilités',
      isLoading: 'chargement',
      'groupNotifications ': 'Notifications de groupe ',
      'peopleNotifications ': 'Notifications de personnes ',
      mustReceiveaSalary: 'Doit recevoir un salaire ',
      wantToSwapShiftsOn: 'Voulez-vous changer de quart de travail ',
      wishesToTakeaDayOff: 'Souhaite prendre un jour de congé',
      daysOff: 'Souhaite prendre congé du',
      to: ' au ',
      wantToSwapShifts: 'Souhaitent échanger leurs quarts de travail:',
      wantToReplaceShifts: 'Voulez-vous remplacer le décalage sur ',
      replaceShiftWith: ' souhaite se faire remplacer par ',
      replaceShiftWithEmergency: " veut être remplacé par d'urgence par ",
      noNotifications: 'Aucune notification pour le moment',
      replaceEmployerEmergency: "Remplacement d'urgence était ",
      replaceEmergencyHasBeenSent:
        "Le remplacement d'urgence a été envoyé à l'employé.",
      by: 'par',
      at: ' à ',
      on: ' le ',
      //notificationslist
      allow: 'Permettre',
      reject: 'Rejeter',
      confirmed: 'Confirmé',
      confirm: 'Confirmer',
      declined: 'Refusé',
      maxHoursPerWeek: 'Heures maximum par semaine',
      //notificationsgroup
      loadMore: ' charger plus',
      today: "Aujourd'hui",
      and: ' et ',
      changeDecision: 'Changer la décision',
      commentYourPost: 'Commentaire sur votre message',
      createdNewPost: 'Créé un nouveau post',
      accepted: 'Accepté',
      wouldLikeToBe: 'Aimeriez-vous être',
      reason: 'Raison',
      all: 'Toutes les demandes',
      exchangeRequests: 'Demandes d’échange',
      replacementRequests: 'Demandes de remplacement',
      dayOffRequests: 'Demandes de congé',
      emergency: 'URGENCES',
      onholdShifts: 'Quarts en attente',
      noNotificationsYet: 'Aucune notification pour le moment',
      wouldLike: 'Aimerait être',
      replacedBy: 'remplacé(e) par',
      sShift: ' ',
      sShiftFr: 'Quart à ',
      forHisShift: 'Pour son quart de travail',
      request: 'Demande',
      wontBeAbleAttendShift:
        "Ne sera pas en mesure d'assister à son quart de travail",
      createdByEmployer: "Créé par l'employeur.",
      motive: 'Motif',
      seePotentialReplacers: 'Voir les remplacements potentiels',
      confirmedInterestedEmployees: 'Employés intéressés confirmés',
      sendConfirmation: 'Envoyer confirmation',
      findAnotherEmployee: 'Trouver un autre employé',
      noOneAcceptedRequest: 'Personne n’a accepté la demande',
      hour_left_before_shift: 'heure restante avant le quart de travail',
      changeAvailabilities: 'Voudrait changer ses disponibilités',
      changeAvailabilitiesNew:
        'Voudrait changer leur disponibilité à partir de ',
      show: 'Montrer',
      seeAvaillabilities: 'Voir ses disponibilités',
      beginning: 'Début',
      end: 'Fin',
      allDay: 'Toute la journée',
      employees: 'Employés',
      positions: 'Rôles',
      attendancy: 'Présence',
      confrim: 'Confirmer',
      acceptedEmployee: 'Employé accepté',
      previous: 'Précédent', // TODO
      new: 'Nouveau',
      getShiftTimeStringError:
        "La date de décalage ne peut pas être affichée, le rechargement de l'application peut résoudre ce problème. Notre équipe a été informée de ce problème.",
      getShiftPositionError:
        "Le nom de la position ne peut pas être affiché, le rechargement de l'application peut résoudre ce problème. Notre équipe a été informée de ce problème.",
      cancelledByEmployee: "Annulé par l'employé",
      day: 'jour',
      left_before_shift: 'restante avant le quart de travail',
      hour: 'heure',
      month: 'mois',
      less_than_hour_left_before_shift:
        "Il reste moins d'une heure avant le quart de travail",
      s_on_call_shift: "'s quart de 'sur appel'",
      day_off_from: 'à partir de',
      day_off_until: "jusqu'à",
      day_off_all_day: 'toute la journée'
    },
    companySettings: {
      you_dont_have_manager_access_to_this_company:
        "Vous n'avez pas accès à ce compte",
      industries: {
        restaurant: 'Restaurant',
        retail: 'Commerce de détail'
      },
      nameIsRequired: 'Le nom est requis',
      addressIsRequired: "L'adresse est requise",
      cityIsRequired: 'La ville est requise',
      pleaseEnterValidEmailAddress: 'Veuillez entrer une adresse e-mail valide',
      fileShouldBeNoMore:
        'Le fichier ne doit pas dépasser 5 Mb et le format jpeg ou png',
      writeNameOfYourCompanyHere: 'Écrivez le nom de votre entreprise ici',
      writeDomainsIsYourCompanyHere: "Sélectionnez l'industrie de l'entreprise",
      chooseTheYear: "Choisissez l'année",
      writeYourCompanyLocationAddressHere:
        "Écrivez l'adresse de votre entreprise ici",
      writeInWhichCityYourCompanyIsSituatedHere:
        'Ecrivez dans quelle ville votre entreprise est située ici',
      writeYourCompanyEmailAddressHere:
        "Écrivez l'adresse email de votre entreprise ici",
      writeShortDescriptionOfYourCompanyHere:
        'Écrivez une courte description de votre ',
      filled: ' rempli',
      required: ' champs obligatoires',
      whatIsTheNameOfYourCompany: 'Nom de votre compagnie',
      inWhichDomainsIsYourCompany: 'Domaine de votre compagnie',
      yearYourCompanyCreated: 'Année de création de votre entreprise',
      whatIsYourCompanyLocationAddress: 'Adresse de votre compagnie',
      inWhichCityYourCompanyIsSituated: 'Ville',
      whatIsYourCompanyEmailAddress: 'Courriel de la compagnie',
      whatIsYourCompanysPhoneNumber: 'Numéro de téléphone de la compagnie',
      shortDescriptionOfYourCompany: 'Courte description de votre compagnie:',
      fillAllNecessaryFields: 'Remplissez tous les champs nécessaires',
      allFieldsAreFilledCorrectly: 'Tous les espaces sont remplis correctement',
      save: 'Sauvegarder',
      addNewCompany: 'Ajouter une entreprise',
      //savemodal
      allRight: "D'accord!",
      changesSaved: 'Changements sauvegardés',
      //cancelModal
      areYouSureYouWantToDiscardChanges:
        'Êtes-vous certain de vouloir annuler les modifications?',
      comeBack: 'Annuler',
      confirm: 'Confirmer',
      yesRemove: 'Oui, supprimer',
      scaleAvatar: 'Mettre la photo de profil à l’échelle',
      accessCodeShare:
        "Code d'accès (partager avec le siège social uniquement)",
      copyToClipboard: "Copier le code d'accès",
      companyAvatar: "Avatar de l'entreprise",
      companyName: 'Nom de la compagnie',
      areYouSureToCancelIntegration:
        "Êtes-vous sûr de vouloir annuler l'intégration?",
      clock_in_url: 'Lien web pour horodateur',
      copied_to_clipboard: 'Copié dans le presse-papiers'
    },
    accountsettings: {
      libro_general_integration_error: 'Erreur de connexion à Libro: {{error}}',
      libro_integration_error_503:
        'Libro est actuellement indisponible. Veuillez réessayer plus tard.',
      regular_work_hours: 'Heures de travail régulières par semaine',
      chronologically: 'Chronologiquement',
      shift_priority: 'Priorité de quart',
      longer_shifts: 'Des quarts de travail plus longs',
      close_to_start_of_day_periods:
        'Périodes plus proches du début de la journée (matin/dîner)',
      download_the_zip_archive_below: 'Téléchargez l’archive ZIP ci-dessous',
      extract_the_files_to_your_maitred_folder_into:
        'Extraire les fichiers de votre dossier MaitreD dans le dossier',
      folder_default_path_is_C_posera_maitred_prg:
        '. Le chemin par défaut est C:\\posera\\maitred\\prg.',
      the_default_path_is_the_following: 'Le chemin par défaut est le suivant.',
      default_path: 'C:\\posera\\maitred\\prg',
      ensure_they_are_not_in_a_default_pivot_folder_after_extraction:
        'Assurez-vous que les fichiers ne se trouvent pas dans un dossier Pivot par défaut après leur extraction.',
      to_re_download_the_files_unlink_the_integration_first:
        'Pour re-télécharger les fichiers, déliez d’abord l’intégration.',
      view_instructions: 'Voir les instructions',
      download: 'Télécharger',
      starting_day_of_payroll_period: 'Jour de début de la période de paie',
      libro_integration_already_connected: 'Libro est déjà connecté',
      libro_integration_added_successfully:
        "L'intégration de Libro a été ajoutée",
      libro_integration_error:
        "Erreur lors de la connexion à l'API de Libro. Vérifiez votre connexion Internet et réessayez.",
      selectLanguage: 'Language par défaut',
      timeFormat: 'Format d’heure:',
      settingsMasterAccount: 'Réglages compte maître',
      preferenceSettings: 'Réglages de préférences',
      integrations_settings: 'Paramètres d’intégration',
      //mastersettings
      pleaseEnterValidEmailAddress: 'Veuillez entrer une adresse e-mail valide',
      emailChanged: 'E-mail changé!',
      newPasswordIsTheSame:
        'Le nouveau mot de passe est le même. Veuillez entrer un nouveau mot de passe.',
      shortPassword: 'Mot de passe court',
      passwordsDoNotMatch: 'Les mots de passe ne correspondent pas',
      passwordChanged: 'Mot de passe changé!',
      currentPasswordIsWrong: 'Le mot de passe actuel est incorrect',
      nameCantBeMoreThan:
        'Le nom ne peut pas dépasser 15 caractères, veuillez entrer un nom correct',
      surnameCantBeMoreThan:
        'Le nom de famille ne peut pas dépasser 15 caractères, veuillez entrer un nom de famille correct',
      changesSaved: 'Changements sauvegardés!',
      fileShouldBeNoMoreThan:
        'Le fichier ne doit pas dépasser 5 Mb et le format jpeg ou png',
      yourCurrentEmail: 'Votre courriel actuel: ',
      changePassword: 'Changer mot de passe',
      oldPassword: 'Ancien mot de passe',
      writeYourPasswordHere: 'Écrivez votre ancien mot de passe ici',
      newPassword: 'Nouveau mot de passe',
      writeYourNewPasswordHere: 'Écrivez votre nouveau mot de passe ici',
      repeatNewPassword: 'Répéter nouveau mot de passe',
      repeatYourNewPasswordHere: 'Répétez votre nouveau mot de passe ici',
      basicInformationAboutTheUser: 'Informations de base',
      enterYourFirstName: 'Prénom',
      writeYourFirstName: 'Ecrivez votre prénom',
      avatar: 'Avatar',
      enterYourLastName: 'Nom',
      writeYourLastNname: 'Ecrivez votre nom de famille',
      save: 'Sauvegarder',
      //preferencesettings
      selectTimezone: 'Sélectionner le fuseau horaire',
      selectCurrency: 'Sélectionnez la devise',
      apply: 'Appliquer',
      weekStartingDay: 'Jour de début de semaine',
      note: "Remarque: cela affecte l'interface de planification des employés, donc ne la changez pas souvent",
      timezone: 'Fuseau horaire',
      monday: 'Lundi',
      tuesday: 'Mardi',
      wednesday: 'Mercredi',
      thursday: 'Jeudi',
      friday: 'Vendredi',
      saturday: 'Samedi',
      sunday: 'Dimanche',

      intervalBetweenNonReccurentShifts:
        'Intervalle entre les quarts de travail non récurrents',

      schedule_generation_format: "Format de génération de l'horaire",
      the_format_influences_your_schedule_generation:
        'Le format sélectionné influencera la manière dont vos horaires seront générés.',
      standard: 'Standard',
      seniority: 'Ancienneté',
      set_priority_order_by_subrole_in_employees_module:
        'Définir un ordre de priorité par sous-rôle dans le module « Employés »',
      shifts_will_distribute_equally:
        'Les quarts seront répartis de manière plus équitable, l’algorithme visant à s’assurer que chaque employé reçoive au moins un quart de travail',
      set_priority_order_by_role_in_employees_module:
        'Définir un ordre de priorité par rôle dans le module « Employés »',
      algorithm_will_prioritize_max_days_per_week:
        'L’algorithme priorisera le nombre maximum de jours par semaine par employé selon l’ordre de priorité. Les employés en bas de la liste pourraient ne pas recevoir de quart de travail',
      warning: 'Avertissement',
      currently_you_are_using: 'Vous utilisez actuellement le format',
      format_for_schedule_generation: ' pour la génération des horaires.',
      switching_to_the: 'Passer au format',
      format_will_implact_shifts_distribution:
        'aura un impact considérable sur la façon dont les quarts de travail sont distribués aux employés.',
      you_will_need_to_set_priority_order_for_employees_by:
        'Vous devrez définir un ordre de priorité des employés par',
      role: 'rôle',
      subrole: 'sous-rôle',
      in_the_employees_module: 'dans le module « Employés ».',
      would_you_like_to_switch_to_this_format:
        'Souhaitez-vous passer à ce format ?',

      password: 'Mot de passe',
      verify: 'Vérifier',
      verified: 'Vérifié',
      select_company: 'Sélectionnez votre entreprise',
      company_to_link: 'Société à lier',
      you_have_to_connect:
        'Vous devez vous connecter et vérifier votre système',
      connectSystemAccount: 'Connecter le compte système',
      connect_with: 'Connecter le compte avec',
      connect_with_MaitreD: 'Connecter le compte avec Maitre D',
      integration_added_successfully: 'Intégration ajoutée avec succès',
      user_code: "Code d'utilisateur",
      business_code: "Code d'entreprise",
      user_password: "Mot de passe de l'utilisateur",
      cluster_pos_api_key: 'POS API key',
      cluster_pos_serial: 'POS Serial key',
      cluster_error: 'Les données fournies sont incorrectes',
      cluster_connected_successfully: 'POS trouvé avec succès',
      cluster_connection_failed: 'Connexion à Cluster échouée',
      no_company_found: 'Aucune entreprise trouvée',
      acomba_pos_modal_header: 'Étapes',
      acomba_pos_modal_step_one:
        "En activant l'intégration, un champ acomba ID sera disponible sur le dossier de l'employé.",
      acomba_pos_modal_step_two:
        "Une fois ce champ rempli manuellement par vous, cet identifiant sera disponible sur le fichier d'exportation par quart de travail.",
      association: 'Association',
      account_connected_to: 'Compte connecté à',
      powerpay_pos_modal_header: 'Étapes',
      powerpay_pos_modal_step_one:
        "En activant l'intégration, vous devrez remplir le champ Powerpay ID dans le dossier de l'employé.",
      powerpay_pos_modal_step_two:
        "Une fois ce champ rempli manuellement par vous, vous devrez ajouter le PayrollID et le BranchID dans la fenêtre d'exportation de paie.",
      powerpay_download_file_instructions:
        "Guide des instructions d'intégration Powerpay:",
      powerpay_download_file_instructions_link: 'instruction_guide.pdf',
      powerpay_powered_by_dayforce: 'Propulsé par Dayforce',
      myr_location_id: 'Votre identifiant MYR',
      myr_get_location_id: 'Trouvez votre identifiant sur le ',
      myr_admin_panel: 'panneau d’administration',
      myr_admin_panel_indication:
        'Veuillez contacter l’équipe d’onboarding MYR pour activer l’intégration sur Pivot :',
      myr_connection_details: {
        email: "Par email à l'adresse ",
        company_name: 'Inclure le nom de votre entreprise ',
        account_id: 'Votre identifiant MYR → 20...',
        delay: 'L’intégration sera alors active dans les 2 jours ouvrés.'
      }
    },
    employeesettings: {
      employeeInteractionGroup: 'Employés peuvent interagir dans le groupe',
      employeeInteractionMessages: 'Employés peuvent communiquer entre eux',
      employeeInteractionWorkWith:
        'Employés peuvent voir avec qui ils travaillent',
      employeeSettings: 'Paramètres des employés',
      exchanges: 'Échanges',
      monthly: 'Mensuel',
      yearly: 'Annuel',
      replacements: 'Remplacements',
      employeeLimit: 'Limites des demandes',
      requestDayOffLimit: 'Demandes de congés',
      daysOff: 'Jours de congés',
      timeRequiredRequest: 'Temps requis avant la demande',
      callInSick: 'Appel à des malades',
      emergency: "Limite d'appel d'urgence",
      notifyEmployees: 'Avertir les employés avant leurs quarts',
      noEnd: 'Durée standard des quarts de travail sans fin',
      hour: ' heure',
      hours: ' heures',
      dailyHoursLessThan: 'Heures journalières moins de',
      excludeBreaksFromWeeklyHours:
        'Exclure les pauses des heures hebdomadaires',
      breakTimeIs: 'Le temps de pause est',
      deleteInterval: 'Supprimer',
      addInterval: '+ Ajouter un intervalle',
      maxDayHours: 'Max heures par jour',
      noLimit: 'Aucune limite',
      ON: '',
      OFF: ''
    },

    pageError: {
      oopsPageNotFound: 'Oops! Page non trouvée =(',
      backOrSupport: 'Retour ou contactez le support.',
      logOut: 'Se déconnecter'
    },
    signUp: {
      you_need_to_be_logged_in_to_verify_your_email:
        'Vous devez être connecté pour vérifier votre email',
      email_verified: 'Email vérifié',
      you_can_now_close_this_tab_and_return_to_the_previous_one:
        'Vous pouvez maintenant fermer cet onglet et revenir à l’onglet précédent',

      close_tab: 'Fermer l’onglet',
      stageOne: 'Première étape',

      link_expired:
        'Le lien a expiré, un nouveau lien a été envoyé à votre email',
      email_you_entered_is_not_correct: "Ce courriel n'est pas valide",
      enter_code_or_contact_pivot:
        "Entrez le code ou contactez l'équipe de Pivot pour ajouter une entreprise",
      sorry_wrong_code_contact_support_or_try_again:
        "Désolé, mauvais code. Veuillez contacter le service d'assistance ou réessayer",

      registration: 'Enregistrement',
      passwordsDoNotMatch: 'Les mots de passe ne correspondent pas',
      password: 'Mot de passe',
      repeatPassword: 'Répéter le mot de passe',
      repeatYourPasswordHere: 'Répétez votre mot de passe ici',
      toSignIn: 'Se connecter',
      save: 'Sauvegarder',

      signUp: "S'inscrire",
      welcome: 'Bienvenue !',
      // Seva - extract to createCompany after merg

      welcomeToPivot: 'Bienvenue sur Pivot !',
      connectEmail: 'Connectez-vous à votre courriel',
      connectWith: 'Connecter à',

      accountType: 'Type de compte',
      basicInfos: 'Infos de base',
      integrations: 'Intégrations',
      createRoles: 'Rôles',

      selectYourAccountType: 'Sélectionnez un type de compte',
      retail: 'Détail',
      restaurant: 'Restaurant',
      headOffice: 'Siège social',
      singleLocation: 'Un emplacement',

      basicInformations: 'Informations de base',
      pleaseFillInformations: 'Remplissez les informations suivantes',
      whatsNameOfCompany: 'Quel est le nom de votre entreprise ?',
      typeName: 'Nom de l’entreprise',
      whatsAddressOfCompany: 'Quelle est l’adresse de votre entreprise ?',
      searchAddress: 'Recherchez une adresse',

      integrateWith: 'Intégrer avec',
      yourPOSsystem: 'Mon système «POS»',
      yourReserveSystem: 'Mon système de réservation',

      theFollowingStepCreateRoles:
        'La prochaine étape consiste en la création de tous les rôles dans votre compagnie',
      create: 'Créez tous les',
      inYourCompany: 'au sein de votre entreprise',
      exManagerWaiterBusboy: 'ex: Gérant, Serveur, Busboy, etc.',

      toCreateRoleSearchName:
        'Afin de créer votre premier rôle, écrivez le nom du rôle',
      exWaiter: 'ex: Serveur',
      roleShiftsNotSameFuncSubRoleHelpYou:
        'Certains quarts de travail peuvent avoir des fonctions différentes. Les sous-rôles sont optionnels, mais peuvent vous aider à mieux classer vos employés selon leurs compétences',
      exOpenClose: 'ex: Ouverture, Fermeture, etc.',
      placeAll: 'Créez tous les',
      inTheirDepartment: 'au sein de votre entreprise',

      rolesByDepartent: 'Rôles par départements',
      placeEachRolesInDepartments:
        'Placez tous les rôles dans leurs département respectif...',

      acronymsAppearOnScheduleShift:
        'Les acronymes apparaissent sur l’horaire afin de représenter le sous-rôle d’un quart. Par exemple, si un employé est attribuer un quart «Fermeture», l’acronyme pourrait être',
      exWOWCL: 'ex: F, S/F, C, CL etc.',

      colorMakeAcronymOnSchedule:
        'La couleur sélectionnée déterminera la couleur de l’acronyme sur l’horaire',
      exWO: 'ex: S/F',
      WO: 'S/F',

      // Personal info
      personalInformation: 'Informations personnelles',
      name: 'Nom',
      surname: 'Prénom',
      avatar: 'Avatar',
      pleaseAllowPopups:
        'Il semble que votre navigateur ait bloqué les pop-ups. Pour continuer avec la connexion via les réseaux sociaux, veuillez autoriser les pop-ups pour ce site dans les paramètres de votre navigateur.'
    },
    forgotPassword: {
      missingDog: 'Manquant @',
      missingDot: 'Manquant .',
      messageSend: 'Message envoyé',
      doNotWorry: 'Ne vous inquiétez pas',
      weWillSendYouaNewPassword: 'Nous vous enverrons un nouveau mot de passe',
      withinTenMinutese:
        'Vous recevrez un courriel pour créer un nouveau mot de passe (vérifier le dossier spam)',
      toSignIn: 'Se connecter',
      //forgotPasswordModal
      checkYourEmail: 'Vérifiez votre courriel',
      ifTheMessageDoesNotArrive:
        "Si le message n'arrive pas à votre adresse e-mail dans les dix minutes, vérifier le dossier spam, vous devez répéter la demande ou écrire au support",
      sendAgain: 'Envoyer à nouveau',
      send: 'Envoyer',

      forgotPassword: 'Mot de passe oublié'
    },
    signIn: {
      no_account_associated_with_provider:
        "Si vous êtes propriétaire d'un établissement, veuillez vous rendre sur la page d'inscription. Si vous êtes un gestionnaire, essayez de vous connecter avec votre adresse e-mail, car nous n'avons pas pu trouver de compte associé à ce fournisseur.",
      wrongPassword: 'Mauvais mot de passe.',
      dontHaveAnAccountYet: 'Vous n’avez pas de compte ?',
      signUp: 'Créer un compte',
      password: 'Mot de passe',
      forgotThePassword: 'Oublié mot de passe?',
      login: 'Se connecter',

      startNow: 'Commencer !',
      signIn: 'Me connecter',
      welcomeBack: 'Ravis de vous revoir'
    },
    signInProviderHint: {
      google:
        "Votre compte semble être associé à un identifiant Google. S'il vous plaît, connectez-vous avec Google.",
      facebook:
        "Votre compte semble être associé à un identifiant Facebook. S'il vous plaît, connectez-vous avec Facebook.",
      apple:
        "Votre compte semble être associé à un identifiant Apple. S'il vous plaît, connectez-vous avec Apple.",
      microsoft:
        "Votre compte semble être associé à un identifiant Microsoft. S'il vous plaît, connectez-vous avec Microsoft."
    },
    resetPass: {
      shortPassword: 'Mot de passe court',
      passwordsDoNotMatch: 'Les mots de passe ne correspondent pas',
      lastStep: 'Dernière étape',
      changeThePassword:
        'Changez le mot de passe et vous pouvez recommencer à travailler',
      logo: 'logo',
      newPassword: 'Nouveau mot de passe',
      writeYourNewPassword: 'Écrivez votre nouveau mot de passe ici',
      repeat_new_password: 'Répéter le nouveau mot de passe',
      continue: 'Continuer',
      //resetpasswordModal
      excellent: 'Excellent',
      yourPasswordHasBeenSuccessfully:
        'Votre mot de passe a été changé avec succès. Vous pouvez maintenant vous connecter à votre compte',
      allRight: "D'accord!",
      sendAgain: 'Envoyer à nouveau',
      weSentYouAnEmail: 'Nous vous avons envoyé un courriel de confirmation !',
      ifTheMessageDoesNotArrive:
        "Si la confirmation ne s'est pas à votre adresse courriel, veuillez vérifier  dans vos courriels indésirables. Autrement, veuillez répéter la demande o contacter notre support",
      back: 'Retour',

      repeat_password: 'Répéter le mot de passe',
      password: 'Mot de passe'
    },
    resetEmail: {
      verificationEmailSent: "L'email de vérification a été envoyé"
    },

    thankYouPage: {
      registration: 'Enregistrement',
      stageOne: 'Première étape',
      createCompany: 'Créer une entreprise',
      stageTwo: 'Deuxième étape',
      createVacancy: 'Créer une vacance',
      stageThree: 'Troisième étape',
      thankYouForRegistering: 'Merci de votre inscription',
      performThreeMore:
        "Effectuez deux étapes plus simples: Enregistrement de l'entreprise et placement de la première vacance"
    },
    createCompany: {
      writeDirectCodeForApply: 'Code d’entrée directe',
      thisCodeAlreadyExist: 'Ce code direct existe déjà, essayez un autre',
      saved: 'Changements enregistrés'
    },
    supportModal: {
      post_your_problem: 'Comment pouvons-nous vous aider',
      write_post_about_your_problem:
        'Veuillez indiquer le problème que vous rencontrez ici',
      yourLetterHasBeenSent: 'Votre lettre a été envoyée',
      thanksForYourLetter:
        'Merci pour votre lettre. Notre équipe de support vous contactera dès que possible. Vérifiez votre e-mail.',
      ok_thanks: 'Ok, merci',
      page_not_found_or_access_denied: 'Page non trouvée ou accès refusé',
      check_link_address: 'Vérifier l’adresse du lien.',
      sign_in_using_mobile_app:
        'Si vous êtes un employé, connectez-vous à l’aide de l’application Pivot sur l’App Store ou Google Play.',
      contact_employer:
        'Si vous êtes un manager, contactez votre employeur pour qu’il vous accorde l’accès manager afin que vous puissiez vous connecter à Pivot web.',
      contact_pivot_team:
        'Si vous rencontrez toujours des problèmes, contactez votre employeur ou l’équipe Pivot directement ************.'
    },
    navbar: {
      schedules: 'Horaires',
      schedule: 'Horaire',
      structures: 'Prévisions',
      recurrency: 'Récurrence',
      payroll: 'Paie',
      timeAttendance: 'Temps & présence',
      requests: 'Demandes',
      chat: 'Messages',
      employees: 'Employés',
      roles: 'Rôles',
      applicants: 'Applicants',
      group: 'Groupe',
      profile: 'Profil',
      settings: 'Paramètres',
      logOut: 'Se déconnecter',
      fix: 'Arranger',
      skipForNow: 'Plus tard',
      youHave: 'Vous avez',
      conflictingShiftsToConfirm: 'quarts problématiques à confirmer'
    },
    hireForWork: {
      chooseCategoryAndSubcategory:
        'Choisissez la catégorie et la sous-catégorie!',
      hireForWork: 'Ajouter',
      applicationSubmitted: 'Application déposée:'
    },

    schedule: {
      inactive: 'Inactif',
      on: 'le',
      some_employees_have_conflicting_shifts:
        'Quelques employés ont des quarts problématiques: ',
      published: 'Publié',
      not_published: 'Non publié',
      publish_schedule: 'Publier l’horaire',
      generate_schedule: 'Générer l’horaire',
      publish_schedule_for_week: 'Publier l’horaire pour la semaine',

      must_have_at_least_one_employee:
        'Vous devez avoir au moins un employé dans votre entreprise pour générer un horaire.',
      pending_availability_on:
        "Il s'agit d'une disponibilité en attente prenant effet le",
      pre_publish: 'Pré-publier',

      schedule_downloaded: 'Calendrier téléchargé',
      error_downloading_schedule:
        "Erreur lors du téléchargement du calendrier, rechargez la page et réessayez. Si l'erreur persiste, appelez Yannick ************",
      something_wrong_reload_or_call_admin:
        "Quelque chose s'est mal passé. Actualisez la page et réessayez. Si l'erreur persiste, appelez Yannick ************",
      manual: 'Manuel',
      skip_generation: 'Skip generation',
      selectSubRole: 'sous-rôle',
      totalActualSales: 'Ventes totales réelles',
      last_year_sales: "Ventes de l'année dernière",
      last_week_sales: 'Ventes de la semaine dernière',
      actualLaborCost: 'Coûts réels main d’oeuvre',
      generalError: "Quelque chose s'est mal passé.",
      errorIn: "Il y a une erreur pour l'entrée : ",
      morningSales: 'Ventes du matin',
      endOfDaySales: 'ventes de fin de journée',
      catererSales: 'Ventes de traiteur',
      theSalesProjectionOn: 'La projection des ventes sur',
      catererSalesProjectionOn: 'La projection des ventes de traiteur sur',
      isInvalid: 'est invalide',
      allDay: 'toute la journée',
      sliderWeek: 'Semaine du',
      defaultSort: 'Ordre de priorité définie',
      alphabeticalSort: 'Ordre alphabétique',
      by_employees_compact: 'Par employés (compacte)',
      selectEmployeeOrder:
        'Sélectionnez l’ordre de disposition de vos employés sur l’horaire',
      week: 'Semaine',
      clean: 'Nettoyer',
      confirmed: 'Confirmé',
      confirm: 'Confirmer',
      chooseTime: "Choisissez l'heure",
      success: 'Succès',
      chooseCategory: 'Choisissez une catégorie',
      youHaveNoEmployees: "Vous n'avez aucun employé",
      stShift: 'er Quart',
      addShift: 'Ajouter',
      loading: 'Chargement...',
      youHaveNoJobs: "Vous n'avez aucun travail",
      worksSchedule: 'Horaire travail',
      scheduleOfInterviews: 'Horaire entrevues',
      youCanEeasilySwitchBetweenCalendars:
        'Vous pouvez facilement basculer entre les calendriers',
      allPositions: 'Tous les rôles',
      editSchedule: 'Modifier le calendrier',
      save: 'Sauvegarder',
      doYouWantSaveThisCalendarAs:
        'Voulez-vous enregistrer ce calendrier en tant que ',
      noCancel: 'Annuler',
      yesRemove: 'Oui, supprimer',
      changesToTheCalendar: 'Changements au calendrier ',
      haveBeenSaved: ' ont été sauvés',
      ok: "D'accord",
      //editScheduleMain
      subcategoryAlreadySelected: 'Sous-catégorie déjà sélectionnée',
      rolesNeeded: 'Rôles nécessaires:',
      amount: '# des employés requis: ',
      amountHold: '# des employés en attente: ',
      edit: 'Modifier',
      print: 'Imprimer',
      next: 'Suivant',
      chooseSubcategory: 'Choisissez la sous-catégorie',
      time: 'Temps:',
      beginningOfWork: 'Début',
      endOfWork: 'Fin',
      newRole: 'Nouveau rôle:',
      addRole: 'Ajouter un rôle',
      //editScheduleRightSide
      editManuallyByTheWeek: 'Modifier manuellement à la semaine',
      editScheduleWithTemplates: 'Modifier le calendrier avec des modèles',
      switchBetweenCalendarsToManage:
        'Basculer entre les calendriers pour gérer les modèles.',
      youCanSaveTheSettingsAsaStructure:
        "Vous pouvez enregistrer les paramètres en tant que structure pour définir cette planification sur d'autres équipes.",
      saveAsStructure: 'Enregistrer en tant que structure',
      writeScheduleName: "Écrire le nom de l'horaire",
      createStructure: 'Créer une structure',
      repeatEveryWeek: 'Répétez chaque semaine',
      youCanLoopTheRepetition: 'Vous pouvez boucler la répétition du graphique',
      //selectEmployees
      priorityList: 'Ordre de priorité',
      dragTheRightEmployeesIntoThisArea:
        'Faites glisser les bons employés dans cette zone',
      toRemoveAnEmployeeFromThisList:
        "Pour supprimer un employé de cette liste, faites-le glisser vers la gauche de l'écran",
      continue: 'Continuer',
      addEmployees: 'Ajouter des employés',
      removeFromThisList: 'Retirer de cette liste',
      //tableHead
      employeesAndPositions: 'Employé et Jours',
      loadingEmployees: 'Chargement des employés, réessayez',
      editLabel: 'Modifier le libellé',
      applyLabel: 'Appliquer de l’étiquette',
      fillWorkTime: 'Remplissez le début et la fin du travail.',
      labelColor: 'La couleur est requise',
      reviewSchedule: 'Horaires',
      labelsForShift: 'Étiquettes',
      editingSchedule: 'Créer horaire',
      hintTextLabel:
        "Les noms d'une structure doivent représenter une variable qui influe sur le nombre requis d'employés prévus",
      hintTextLabelExample: 'Ex: Revenues, Customer traffic, etc.',
      newStructure: 'Nouvelle structure',
      shift: 'Quart',
      labelName: 'Titre',
      name: 'Titre',
      apply: 'Appliquer',
      post: 'Poster',
      andPositions: 'et positions',
      editManually: 'Modifier manuellement',
      dayOfWeek: 'Jour de la semaine',
      labels: 'Prévisions',
      newLabel: 'Créer une étiquette',
      color: 'Couleur',
      duplicateLabel: 'Dupliquer',
      addToOftenUsed: 'Ajouter à favoris',
      st: 'er ',
      nd: 'e ',
      rd: 'e ',
      th: 'e ',
      search: 'Chercher...',
      label: 'L’étiquette ',
      subPosition: 'Sous-position',
      chooseLabel: 'Sélectionnez...',
      back: 'Retour',
      generateSchedule: 'Générer l’horaire',
      paste: 'Coller',
      oftenUsed: 'SOUVENT UTILISÉ',
      otherLabels: 'AUTRES ÉTIQUETTES',
      resetAllFills: 'Réinitialiser la semaine',
      repeatPreviousWeek: 'Répétez la semaine précédente',
      posted: 'PUBLIÉ',
      notPosted: 'NON PUBLIÉ',
      nothingYet: 'RIEN POUR LE MOMENT',
      beginning: 'Début',
      status: 'Statut',
      repeatf: 'Répéter',
      selectColor: 'Couleur',
      deleteShift: 'Supprimer',
      deleteStructure: 'Supprimer',
      duplicateStructure: 'Dupliquer',
      duplicateShift: 'Dupliquer',
      applyChanges: 'Enregistrer',
      postedSchedule: 'Horaires',
      dayStructures: 'Prévisions',
      fixedSchedule: 'Horaire de base',
      allEmployees: 'Tous les employés',
      onlyTheseShifts: 'Horaire complet',
      shiftTime: 'Quart de travail',
      findReplacer: 'Urgence',
      byEmployees: 'Par employés',
      byPositions: 'Par rôles',
      saveChanges: 'Sauvegarder',
      buisnessOperationTimePeriod: 'Période de service',
      createNewLabel: '+ Créer un nouveau',
      morning: 'Matin',
      evening: 'Soir',
      lunch: 'Dîner',
      dinner: 'Souper',
      night: 'Nuit',
      add: 'Ajouter',
      addNewPeriod: 'Ajouter une nouvelle période',
      addNewLabel: 'Ajouter une nouvelle étiquette',
      reset: 'Réinitialiser',
      delete: 'Supprimer',
      on_call: 'Sur appel',
      postSchedule: 'Publier',
      copy: 'Copie',
      deletingPeriod: 'Supprimer des périodes supprimera toutes les étiquettes',
      sureDeleteLabel: 'Êtes-vous sûr de vouloir supprimer ces étiquettes?',
      'addEmployee(s)': 'Ajouter un employé(s)',
      endOpitonal: 'Fin(facultatif)',
      availableEmployees: 'Employés disponibles',
      youMustFillAll: 'Vous devez remplir toutes les informations',
      required: 'requises',
      selectTime: 'Heure',
      toSeeWhosAvail: 'pour voir qui est disponible',
      day: 'Journée',
      scheduledEmployees: 'Employés cédulés',
      'addEmployees+': 'Ajouter un employé(s)',
      pleaseFillData: "S'il vous plaît remplir les données ou fermer popover",
      pleaseSwitchToEdit: 'Allez sur le mode édition.',
      shifts_conflict:
        'Les changements sont contradictoires, veuillez corriger',
      selectPosAndSubpos: 'Sélectionnez la position et la sous-position',
      shiftsConflicting:
        'Les changements sont contradictoires, veuillez corriger',
      removed: 'Supprimé',
      editMode: 'Mode édition',
      noEmployees: 'Aucun employé',
      labelsWereSaved: 'Les étiquettes ont été sauvegardées',
      currentCompanyDoesNotHaveTimePeriod:
        "La société actuelle n'a pas au moins des périodes de temps et une étiquette par période de temps, il est nécessaire pour le nouvel horaire. Remplissez cette information dans STRUCTURES DE JOUR",
      youMustSelectInfo: 'Vous devez sélectionner les informations',
      'withAn*': 'avec un * pour voir qui est disponible',
      select: 'Sélectionnez...',
      sendRequest: 'Envoyer demande',
      end: 'Fin',
      applyDuplication: 'Appliquer la duplication',
      update: 'Mettre à jour',
      schedulePosted: 'HORAIRE POSTÉ',
      overtime: 'Heures supplémentaires',
      time_off: 'Congés',
      'employees(s)': 'Employés',
      from: 'De',
      to: 'À',
      accetpedBy: 'Accepté par',
      employeeAvailabilities: 'Employés disponibles',
      //modals
      youHaventUpdatedChanges:
        "Vous n'avez pas mis à jour vos modifications...",
      areYouSureToQuit:
        "Êtes-vous certain de vouloir le mode d'édition sans enregistrer ?",
      yesQuit: 'Oui, quitter',
      scheduleIsPosted: 'horaire est déjà posté',
      wouldYouApplyAndNotifyEmployees:
        'Souhaitez-vous appliquer ces changements et aviser les employés concernés?',
      shiftYouModifyIsReccurent:
        'Le changement que vous voulez modifier est un changement récurrent',
      modifyDefinitely: 'Modifier définitivement',
      modifyExceptionally: 'Modifier exceptionnellement',
      sureDeleteShift: 'Êtes-vous sûr de vouloir supprimer ce changement?',
      deleteDefinitely: 'Supprimer définitivement',
      deleteExceptionally: 'Supprimer exceptionnellement',
      yesDelete: 'Oui, supprimer',
      giveShift: 'Donner le quart',
      printSchedule: 'Options d’impression',
      selectScheduleFormat: 'Sélectionnez un format d’impression',
      createNewSchedule: 'Créer un nouvel horaire pour la semaine',
      //conflict modal
      whatsHappening: 'Solutions proposées',
      missingEmployees: 'Employés manquants',
      conflict: 'Conflit',
      requiredConfilct: 'requis',
      empl: 'empl. ',
      only: 'Seulement ',
      available: 'disponible',
      replacerRequest: 'Trouver un remplaçant',
      ignore: 'Ignorer',
      solution: 'Solution ',
      emergencyRequestWillNotify: "Demande d'urgence avisera",
      everyEmplInPosition: 'tous les employés dans cette position',
      youWIllReceiveAnswer: 'Vous recevrez des réponses',
      employeesShortly: 'de vos employés sous peu',
      areYouSure: 'Êtes-vous sûr de vouloir',
      ignoreConflict: 'ignorer ce conflit ?',
      'missingShift/FixedSch': 'Étapes manquantes Étiquette / Horaire de base',
      'label/FixedSch': 'Étiquette / Horaire de base',
      byIgnoringIt: "En l'ignorant,",
      willNotBe: 'ne sera PAS',
      scheduledAt: 'prévu à ',
      noMatchingLabel: "Pas d'étiquette correspondante",
      requiredNoMoreEmpl: 'Pas besoin empl',
      justForOnce: 'Juste pour une fois',
      definitely: 'Absolument',
      change: 'Changement ',
      fixedschedule: 'horaire de base',
      noAvailable: 'Aucun disponible',
      dayOff: 'Congé',
      dayOfTheWeek: 'Jour de la semaine',
      revenue: 'Revenu',
      conflicts: 'Conflits',
      saveDraft: 'Sauvegarder brouillon',
      positions: 'Rôles',
      searchEmployee: 'Recherche employé...',
      close: 'Fermer',
      laborCost: 'Coût de main d’oeuvre',
      total: 'Total',
      newSchedule: 'Nouvel horaire',
      laborCostsOverview: "Vue d'ensemble",
      laborCostHead: "Main d'oeuvre",
      resetSchedule: 'Réinitialiser horaire',
      unpublish: 'Dépublier',
      recurrencyStructure: 'Structure de récurrence sur',
      startingWeek: 'Semaine de départ',
      'selectEmployee(s)': 'Sélectionnez un/des employé(s)',
      'selectedEmployee(s)': 'Employé(s) sélectionnés',
      selectWeek: 'Sélectionnez une semaine',
      hisCreateSch: 'sa',
      thisEmployeeScheduled:
        'Cet employé se trouve déjà dans le structure de récrurrence sur ',
      recStructure: 'structure de récurrence',
      byConfirmingTransferFixedWillDeleted:
        'En confirmant son transfert vers la structure de ',
      willBeDeletedCreateSch: 'sera supprimée.',
      availableEmployeesFor: 'Employés disponibles pour',
      weekRecurrency: 'récurrence de semaines',
      nextStartingWeek: 'Prochaine semaine',
      startWeek: 'Semaine de début',
      addUser: 'Ajouter un employé',
      changeStartingWeek: 'Changer la semaine de départ',
      schedule: 'Horaire',
      complete: 'Complet',
      incomplete: 'Incomplet',
      timePeriod: 'Période',
      potentialAvailEmpl: 'Employés disponibles pour ce quart',
      moving_shift: 'Déménagement quart',
      forwardOvertimeShift: 'Transférer le quart de travail',
      notAvailable: 'Non-disponible',
      forwardShiftTransfer: 'Transférer',
      noEmployeesAvailable: 'Aucun employé disponible',
      you_need_to_generate_at_least_one_role:
        'Vous devez générer au moins un rôle',
      start: 'Débuter',
      starting: 'Départ',
      ending: 'Fin',
      addTime: 'Ajouter du temps',
      never: 'Jamais',
      everyWeek: 'Chaque semaine',
      selectStartingWeek: 'Sélectionnez une semaine',
      no_results_found: 'Aucun résultat trouvé',
      search_employee: 'Recherche employé...',
      select_dates: 'Sélectionnez les date(s)...',
      weekRecurrencyFr: 'Récurrence sur',
      weekRecurrencyEng: 'semaine',
      currentWeekEng: 'Semaine',
      currentWeekFr: 'actuelle',
      allDayTime: 'Journée complète',
      allDayTimeShorten: 'Journée compl.',
      scheduleView: 'Vue de l’horaire',
      weekCreateSch: 'semaine',
      weeksCreateSch: 'semaines',
      draft: 'Brouillon',
      red: 'Rouge',
      pink: 'Rose',
      purple: 'Mauve',
      deepPurple: 'Violet',
      indigo: 'Indigo',
      blue: 'Bleu',
      lightBlue: 'Bleu pâle',
      cyan: 'Cyan',
      teal: 'Turquoise',
      green: 'Vert',
      lightGreen: 'Vert pâle',
      lime: 'Lime',
      yellow: 'Jaune',
      amber: 'Jaunâtre',
      orange: 'Orange',
      deepOrange: 'Orange foncé',
      brown: 'Brun',
      grey: 'Gris',
      blueGrey: 'Gris bleuté',
      darkGrey: 'Gris foncé',
      morningPop: 'Matin',
      lunchPop: 'Midi',
      dinnerPop: 'Soir',
      nightPop: 'Nuit',
      days: 'Journées',
      onCall: 'Sur appel',
      past: 'Passé',
      createNewShift: 'Créer un nouveau quart',
      nightPeriod: 'Période de nuit',
      dayPeriod: 'Période de jour',
      revenues: 'Revenus',
      client: 'client',
      clickToFix: 'cliquez pour corriger',
      selectPositionAndSubpositionFirst:
        "Sélectionnez d'abord la position et la sous-position.",
      youHaveUnsavedChanges:
        'Vous avez des modifications non enregistrées, vous les perdrez si vous quittez cet onglet (Horaires publiés)',
      unpublishConfirm:
        "Voulez-vous vraiment dépublier l'horaire de cette semaine?",
      are_you_sure_you_want_to_delete_selected_positions:
        'Êtes-vous sûr de vouloir supprimer les rôles sélectionnés?',
      openShifts: 'Quarts ouverts',
      open: 'Ouverts',
      show: 'Ouvrir',
      you_are_not_allowed_to_perform_this_action:
        'Vous n’êtes pas autorisé à effectuer cette action',

      doenst_have_role_assigned: "n'a pas ce rôle attribué à son profil",
      giveShiftTo: 'Attribuer ce quart à',
      noEmployeeSelected: 'Aucun employé sélectionné',
      deleteAll: 'Tout supprimer',
      successfullyAdded: 'Ajouté avec succès',
      reccurentShifts: 'changements récurrents',
      labelShifts: "changements d'étiquette",
      missing: 'Disparu',
      onHoldShifts: "quarts d'attente",
      attestShifts: 'attester les changements',
      publishedSuccessfully: 'Publié avec succès',
      draftCreatedSuccessfully: 'Brouillon mis à jour avec succès',

      //welcome page
      welcomeToPivot: "Bienvenue dans l'ère Pivot !",
      pleaseSelectTypeAccount: 'Veuillez sélectionner votre type de compte',
      filialOffice: 'Une succursale',
      headOffice: 'Siège social',
      locations: ' Emplacements',
      account: 'Compte',
      company: 'Compagnie',

      //timeoff popover
      startingPopover: 'Début',
      details: 'Détails',
      acceptedBy: 'Accepté par :',
      wasAcceptedOn: 'A été accepté le :',
      reason: 'Raison:',
      reasons: 'Raisons:',
      cancel_time_off: 'Annuler',
      edit_time_off: 'Modifier',

      //create timeoff
      create_time_off: 'Créer',
      edit_time_off_title: 'Modifier',
      timeOfType: 'Heure du type',
      addEmployee: '+ Add employee',
      typeEmployeeName: "Écrivez le nom de l'employé",
      send: 'Envoyer',
      not_paid_time_off: 'Congés non payés',
      paid_time_off: 'Congés payés',
      selectPeriod: 'Période...',
      what_type_time_off: 'Quel type de congés ?',
      select_type: 'Sélectionner',
      which_employee: 'Employé',
      when: 'Date',
      starts: 'Début',
      ends: 'Fin',
      what_is_reason_time_off: 'Raison',
      optional: 'optionnel',
      type_reason_here: 'Tapez la raison ici',
      confirm_time_off: 'Confirmer congés',
      update_time_off: 'Mettre à jour congés',
      select_employee: 'Sélectionner',
      time_off_created: 'Demande de congés créée',
      time_off_error: 'Erreur dans la demande de congés',
      time_off_updated: 'Demande de congés mise à jour',
      shift_scheduled_error: 'Un quart de travail est déjà prévu pour ce jour',
      shift_draft_error:
        'Un brouillon de quart de travail existe déjà pour ce jour',
      day_off_error: 'Un quart de travail est assigné à cette date',
      fix_the_following_days:
        'Veuillez résoudre les conflits suivants avant de continuer :',

      //modal
      modal: {
        theSchedule: "L'horaire du",
        isAlreadyPublished: 'est déjà publié.',
        wouldYouApplyAndNotifyEmployees:
          'Souhaitez-vous appliquer ces changements et aviser les employés concernés?',
        byConfirmingSchedule: "En confirmant, l'horaire du",
        willBePublished: 'sera publié.',
        areYouSurePublish:
          'Êtes-vous certain que cet horaire est prêt à être publié?',
        confrim: 'Confirmer'
      },

      what_your_sales_projections_for_week:
        'Quelles sont vos prévisions de ventes pour la semaine de',
      das_calculation: 'Calcul DAS',
      average_tips_short: 'Pourb. moyens',
      das: 'DAS',
      lastYear: 'Année dernière',
      lastWeek: 'Semaine dernière',
      projection: 'Projections',
      caterer_projections: 'Projections de traiteur',
      caterer_projection: 'Projection de traiteur',
      sales_projections: 'Projections de ventes',
      totalProjection: 'Projections totales',
      generate: 'Produire',
      sales: 'Ventes',

      detailedView: 'Vue détaillée',
      overview: 'Aperçu',
      compactedView: 'Vue compactée',
      totalSalesProjections: 'Projections de ventes totales',
      estimatedLaborCost: "Main-d'œuvre estimé",
      salesProjection: 'Projection des ventes',
      projections: 'Projections',
      dailySales: 'Ventes quotidiennes',
      closeTheDay: 'Fermer journée',
      dayClosed: 'Journée fermée',
      reconnectPos: 'POS',
      closingDay: 'Fermer journée',
      endOfDay: 'Fin de journée',
      caterer: 'Traiteur',
      salesByDay: 'Ventes par jour',
      youHaveSuccessfullyClosed: 'Vous avez fermé avec succès',
      done: 'Terminé',
      wage: 'Salaire',
      totalDasWage: 'Total DAS (Salaire)',
      tips: 'Pourboires',
      tipsShort: 'Pourb.',
      totalDasTips: 'Total DAS (Pourboires)',
      dailyCostPerDay: 'Coût par jour',

      laborCostSettings: "Paramètres de coût de la main-d'œuvre",
      placeEachRoleDepartment:
        'Placez chaque rôle dans son département et indiquez ceux',
      onesYouWantShowLaborTable:
        "que vous souhaitez afficher dans le tableau des coûts de main-d'œuvre.",
      frontOfHouse: 'FOH - Service',
      backOfHouse: 'BOH - Cuisine',
      management: 'Gestion',
      averageTips: '% pourboires moyen',
      MNGCost: 'Coût MNG',
      current_week: 'Semaine courante',
      upcoming_week: 'Semaine suivante',
      past_week: 'Semaine précédente',
      noSchedule: 'Aucun horaire',

      today: "Aujourd'hui",
      todayShorten: 'Aujourd.',
      totalCost: 'Coût total',
      weeklyCost: 'Coût hebdomadaire',
      dailyCost: 'Coût quotidien',
      costs: 'Coûts',

      byFOH: 'Par Front of House',
      byBOH: 'Par Back of House',
      byMNG: 'Par Management',
      byRoles: 'Par rôles',

      openShift: 'Quart ouverts',
      addingEmployee: "Ajout d'un employé",
      subRole: 'Sous-rôle',
      noStart: 'Pas début',
      noEnd: 'Sans fin',
      unavailable: 'Indisponible',

      weekly_log: 'Journal',
      whatsNew: 'Quoi de neuf?',
      addLog: 'Ajouter',
      sureDeleteLog: 'Voulez-vous vraiment supprimer ce journal?',
      noWeeklyLogs: 'Pas encore de journaux',
      date: 'Date',

      publish: 'Publier',
      recreate: 'Recréer',
      actions: 'Actions',
      theEndingOfShiftConflictBeginningShift:
        'La fin du 1er quart est en conflit avec le début du 2e quart',
      includeOnCallShifts: 'Inclure les quarts de travail "sur appel"',
      reservations: 'Réservations',
      cancellations: 'Annulations',
      goToMyLibro: 'Accéder Libro',

      noteForShift: 'Ajouter une note à ce quart',
      typeNoteHere: 'Tapez une note ici',

      time_off_canceled: 'Demande de congé annulée',

      shifts: 'Quarts',
      confirmation: 'Confirmation',
      declined: 'Refusé',
      calendar: 'Calendrier',
      backToCurrentWeek: 'Retour à la semaine en courante',

      recurrency: 'Récurrence',
      move_shift: 'Déplacer le quart',
      mark_as_open: 'Marquer comme ouvert',
      means_shift_no_longer_assigned_employee:
        'signifie que le quart de travail ne sera plus attribué à un employé',

      warning: 'Avertissement',
      employee_you_trying_schedule_already_scheduled:
        "TL'employé que vous essayez de planifier est déjà programmé à cette période.",
      what_would_you_do: "Qu'est-ce que tu aimerais faire ?",
      give_shift_anyway: 'Donne quand même',

      trade_shift: 'Échanger un quart',
      select_position: 'Sélectionnez une position',

      expected_reservations_pre_text: 'Le nombre de clients ',
      expected: 'Attendus',
      expected_reservations_hint_text:
        ' repésente le nombre de clients qui sont attendus dans votre restaurant selon ce que notre intelligence artificielle (IA) a calculé en fonction des données passées.',
      recurrent_schedule: 'Horaire de base',
      roles: 'Rôles',
      role: 'Rôle',

      generate_schedule_for_week: 'Générer l’horaire pour la semaine du:',
      restart: 'Redémarrage',
      emergency: 'Urgence',
      labor_cost_header: 'Main d’oeuvre',

      select_for_which_roles:
        'Sélectionnez les rôles pour lesquels vous désirez',
      the_schedule: 'l’horaire...',

      all_roles: 'Tous les rôles',
      all: 'Tout',
      opened: 'Ouvert',
      closed: 'Fermé',
      remaining: 'restants',
      generated: 'Généré',
      not_generated: 'Non généré',

      you_successfully_pre_published_schedule:
        'Vous avez pré-publié le planning avec succès',
      your_schedule_has_been: 'Votre horaire a été',
      your_schedule_has_been_created_in: 'Votre horaire a été créé en',
      prepublished: 'Pré-publié',
      schedule_changes_are_pending_publish_again:
        'Des modifications d’horaire sont en attente. Veuillez publier à nouveau pour répondre aux quarts sur appel.',
      you_have_unpublished_changes:
        "Vous avez apporté des modifications à un poste déjà publié, mais ces modifications n'ont pas encore été publiées. Ils sont enregistrés sous forme de brouillon et vous pouvez les publier à tout moment.",
      in_draft: 'en brouillon',
      all_created: 'Tous créés',
      all_published: 'Tous publiés',
      in_published: 'dans publié',
      created: 'créé',

      select_period_type: 'Sélectionnez une option',
      days_in_row: 'Jours de suite',
      split_days: 'Jours séparés',

      starting_at: 'Début à',
      ending_at: 'Fin à',

      are_you_sure_you_want_to_cancel_this_time_off_request:
        'Êtes-vous certain de vouloir annuler cette demande de congé ?',
      yes_cancel: 'Oui, annuler',
      no: 'Non',
      failed_to_remove_day_off_try_again_or_contact_support:
        'Échec de la suppression du congé. Veuillez réessayer ou contacter le support.',
      request_sent_success: 'Demande envoyée',
      error_sending_request: "Erreur lors de l'envoi de la demande",
      shift_deleted: 'Quart supprimé',
      shift_with_the_same_start_and_end_already_exists:
        'Un quart avec le même début et la même fin existe déjà',

      no_employees_assigned_to_this_role:
        'Aucun employé n’est assigné à ce rôle'
    },
    dashboard: {
      link_copied_to_clipboard: 'Lien copié dans le presse-papiers',
      accessToAccount: 'Accès au compte',
      totalLaborCost: 'Coûts totaux',
      totalDistributedHours: 'Revenu total pour la semaine',
      laborCostGraph: 'Graphique des coûts de main d’oeuvre',
      detailedLaborCost: 'Coût de main d’oeuvre détaillé',
      addACompany: 'Ajouter une entreprise...',
      viewDashboardGraph: 'Vue graphique du tableau',
      detailedlaborCostHead: 'Coût du travail détaillé',
      laborCostHeadPositions: 'Rôles',
      laborCostHeadHead: 'Main d’oeuvre',
      total: 'TOTAL',
      laborCostHead: 'Main d’oeuvre',
      totalSales: 'De ventes totales',
      totalLaborCostStats: 'Coût total de main d’oeuvre',
      openGraph: 'Ouvrir graph',
      pending: 'En attente...',
      headOfficeAccess: 'Accès au siège social',
      noHeadOfficeAccess: "Pas encore d'accès au siège social...",
      invitation_success:
        "L'e-mail d'invitation a été envoyé. Vous pouvez également copier le lien et l'envoyer à l'utilisateur.",
      generel_error:
        "Quelque chose s'est mal passé. Veuillez vérifier la connexion Internet et réessayer.",
      enter_valid_email: "S'il vous plaît, mettez une adresse email valide",
      access_removed: "L'accès a été supprimé",
      email_already_used:
        'Courriel déjà utilisé pour accéder à un compte maître.',
      email_already_used_master_account:
        'Courriel déjà utilisé pour accéder à un compte maître.',
      back_to_dashboard: 'Retour au tableau'
    },
    indexes: {
      1: '1er',
      2: '2e',
      3: '3e',
      4: '4e',
      5: '5e',
      6: '6e',
      plural: 'e'
    },
    scheduleEmployee: {
      from: 'de',
      current_availability: 'Disponibilité actuelle',
      pending_availabilities: 'Disponibilités en attente',
      forwardShift: 'Transférer un quart',
      tradeShift: 'Échanger un quart',
      shiftStart: 'Début',
      shiftEnd: 'Fin',
      addShift: 'Ajouter',
      availability: 'Disponibilité',
      notAvailable: 'Non-disponible',
      detailedView: 'Vue détaillée',
      compactedView: 'Vue compactée',
      doYouWantTradeShifts: 'Voulez-vous échanger ces changements ?'
    },
    schedulePosition: {
      availableEmployees: 'Employés disponibles',
      allDayUppercase: 'TOUTE LA JOURNÉE',
      update: 'Mettre à jour',
      add: 'Ajouter',
      potentialAvailableEmployees: 'Employés disponibles pour ce quart',
      transfer: 'Transfert',
      followingShiftDoesntMatch:
        'Le quart de travail suivant ne correspond pas aux disponibilités des employés',
      shiftStart: 'Début',
      shiftEnd: 'Fin',
      available: 'Disponible',
      doYouStillWantGiveShift: 'Voulez-vous toujours donner le changement ?',
      start: 'Début',
      addShift: 'Ajouter',
      done: 'Terminé'
    },
    fixedSchedule: {
      morning: 'Matin',
      evening: 'Soir',
      dayOff: 'Congé'
    },
    attendance: {
      last_name: 'Nom',
      first_name: 'Prénom',
      employee_number: 'Numéro d’employé',
      employee_order: 'Ordre des employés',
      overtime_paid: 'Supplément',
      overtime_not_calculated: 'Pas calculé',
      overtime_weekly: 'Hebdo',
      overtime_biweekly: 'Bi-hebdo',
      createdShift: 'Quart créé',
      minute: 'minute',
      by_week_format_title: 'Paie_par_semaine',
      by_shift_format_title: 'Paie_quarts',
      roundningTimeIs:
        "Notre système arrondira les heures d'entrées et de sorties des employés de manière automatique",
      example: 'Exemple',
      roundingTimeAt: 'Arrondir à',
      clockAt: 'Entrée',
      rounded: 'Arrondit à',
      whatIsRoundingTime: 'Que veut dire arrondir?',
      apply: 'Confirmer',
      clickSolveConflicts: 'Appuyez ici pour arranger les quarts',
      break: 'Pause',
      to: 'à',
      roles: 'Rôles',
      upcoming: 'À venir',
      currentPayPeriod: 'Période courante',
      pastPeriod: 'Période passée',
      readyToExport: 'Prêt à exporter',
      conflictingShifts: 'quarts problématiques',
      allPositions: 'Tous les rôles',
      totalHours: 'Heures totales',
      totalSalary: 'Salaire total',
      week: 'Semaine',
      breaks: 'pauses',
      shiftConfirmed: 'Quart confirmé',
      confirmShift: 'Confirmer',
      editShift: 'Modifier',
      in: 'Entrée',
      out: 'Sortie',
      total: 'Total',
      today: "Aujourd'hui",
      add_shift: 'Ajouter',
      position: 'Poste',
      subPosition: 'Sous-poste',
      scheduled: 'Cédulé',
      notScheduled: 'Aucun quart',
      creatingShift: 'Ajouter un quart',
      employeeNotScheduled: "Cet employé n'était pas cédulé",
      deleteAll: 'Tout supprimer',
      scheduledShift: 'Quart cédulé',
      nonScheduledShift: 'Quart non cédulé',
      clockedIn: 'Entrée',
      clockedOut: 'Sortie',
      clocked: 'Poinçonné',
      hours: 'Heures',
      minutes: 'Minutes',
      delete: 'Supprimer',
      confirm: 'Confirmer',
      changeDecision: 'Changer de décision',
      select: 'Choisir...',
      shift: 'quart',
      youMustSovleConflictsBeforeExport:
        "Vous devez régler les quarts problématiques avant d'exporter",
      overlappingShifts:
        'Veuillez corriger vos quarts de travail qui se chevauchent pour cette période',
      exportPayPeriod: 'Exporter',
      howToExportPayPeriod:
        'Comment souhaitez-vous exporter cette période de paie?',
      toPayrollSystem: 'Vers mon système de paie',
      youMustLinkAccount: '*Vous devez lier votre compte*',
      linkMyAccount: 'Intégrer mon compte',
      excelSpreadsheet: 'Tableau Excel',
      willFillHoursAndSalary:
        'Remplira automatiquement les heures et le salaire des employés',
      willDownloadExcel: 'À télécharger sous forme de document Excel (.xls)',
      willDownloadXML: 'À télécharger sous forme de document XML (.xml)',
      linked: 'Intégré',
      payroll_system: 'Système de paie',
      reservation_system: 'Système de réservation',
      cancelIntegration: "Annuler l'intégration",
      currentlyUnavailable: 'Actuellement indisponible',

      timesheet: 'Feuille de temps',
      laborCost: 'Coût main d’oeuvre',
      hoursWorked: 'Heures travaillées',
      splitTip: 'Pourboires partagés',
      splitTipShort: 'Pourb. partagés',
      cut: 'Cote',
      cutRounding: 'Cote % + arrondir',
      tips: 'Pourboires',
      tipsShort: 'Pourb.',
      currentlyWorking: 'Travaille présentement',
      payrollSettings: 'Paramètres de la paie',
      payrollPeriod: 'Période de paie',
      biweekly: 'Bihebdomadaire',
      weekly: 'Hebdomadaire',
      roundingTime: 'Arrondir à',
      tipsDistribution: 'Distribution pourb.',
      tipsDistributed: 'Pourb. distribué',
      eachTimePeriod: 'Chaque période de service',
      cutDistribution: 'Distribution cote',
      salaryType: 'Type de rémun.',
      value: 'Poid',
      wageOnly: 'Seulement salaire',
      wageTips: 'Salaire + Pourb.',
      wageCut: 'Salaire + Cote',

      totalSplitTip: 'Pourb. partagés total',
      workedHours: 'Heures travaillées',
      admissible: '% Admissible',
      paidSplitTip: 'Pourb. partagés payé',
      paidWage: 'Salaire payé',
      averageSalary: 'Salaire moyen',
      viewShift: 'Voir quart',

      nextStartingWeek: 'Prochaine semaine',
      tipsFormat: 'Format pourb.',
      normal: 'Normal',
      byTimePeriod: 'Par période',
      daily: 'Quotidien',
      cutFormat: 'Distribution cote',
      inCash: 'En liquide',
      deductedFromPay: 'Déduit du salaire',
      general: 'Général',
      payrollFrequency: 'Fréquence de paie',
      tipsDeclaration: 'Pourb. déclaration',
      export: 'Exporter',
      salaryExport: 'Salaires',
      overtime_export: 'Temps supl.',
      tipsExport: 'Pourboires',
      cutExport: 'Cotte',
      declarationExport: 'Déclaration',
      tipsAndCut: 'Pourb. & Cote',

      clockedShift: 'Poinçonné',
      role: 'Rôle',
      salary: 'Salaire',
      salaryMade: 'Salaire fait',
      sales: 'Ventes',
      declarationShort: 'Décl.',
      totalDue: 'Total dû',
      totalCut: 'Cote totale',
      employerD_integration_error: 'Intégration employeurD manquante',
      pos_systems: 'Système POS',
      bonus: 'Bonus',
      cashSales: 'Ventes comptant',
      totalRevenue: 'Revenu total',
      due: 'Dû',
      nethris_export_format: "Format d'exportation Nethris",
      employeurd_export_format: "Format d'exportation EmployeurD",
      will_be_download_as_a_text_document:
        'Sera téléchargé sous forme de document',

      enterYourEmployeeID:
        "Entrez votre identifiant d'employé pour pointer à l'arrivée/au départ",
      forgotEmployeeID: "Numéro d'employé oublié",
      attendance: 'Présence',
      employeeID: "ID d'employé",
      enjoyYourShift: 'Profitez de votre quart de travail',
      youAreClockedIn: 'Vous êtes maintenant entrée',
      youAreClockedOut: 'Vous êtes maintenant sortie',
      hello: 'Bonjour',
      areYouReadyStartShift:
        'Êtes-vous prêt à commencer votre quart de travail?',
      areYouReadyEndShift: 'Êtes-vous prêt à terminer votre quart de travail?',
      clockIn: 'Entrée',
      clockOut: 'Sortie',
      clockedInAt: 'Entrée à',
      shiftDuration: 'Durée',
      thankYou: 'Merci',
      youWillReceiveEmail:
        'Vous recevrez un courriel pour créer un nouveau mot de passe (vérifier le dossier spam)',
      invalidUrl: 'URL invalide',
      invalidEmployeeID: 'Code d’employé incorrect',
      by_shift_export_format: 'Exportation par quart de travail',
      payEvolutionExportFormat: "Format d'exportation Payevolution",
      powerpayExportFormat: "Format d'exportation Powerpay",
      archived: 'archivé',
      error_four_shifts_exist:
        "Vous avez déjà effectué quatre quarts de travail aujourd'hui.",
      pay_export: {
        company: 'Compagnie',
        employee_no: 'Matricule',
        record_no: 'No relevé',
        period: 'Période',
        amount: 'Montant',
        quantity: 'Quantité',
        rate: 'Taux',
        week: 'Sem.'
      },
      export_modal: {
        simplified: 'Simplifié',
        detailed: 'Détaillé'
      },
      powerpay: {
        branch_id: 'ID de branche',
        payroll_id: 'Numéro de paie',
        hourly: 'Heure',
        amount: 'Montant',
        all_employees_must_have_a_powerpay_id:
          'Tous les employés doivent avoir un ID Powerpay sur leur profil employé.'
      },
      integrations_settings: 'Paramètres des intégrations',
      select_systems_you_need_to_manage:
        'Sélectionnez les systèmes dont vous avez besoin de gérer la paie, les réservations et le POS',
      solutions: 'Solutions',

      link: 'Lier',
      disconnect: 'Déconnecter',
      Linked: 'Lié',
      connect: 'Connecter',
      integrate: 'Intégrer',
      integrated: 'Intégré'
    },
    payroll: {
      settings: 'Paramètres',
      hours: 'Heures',
      tips: 'Pourboires',
      cuts: 'Cotisations',
      feature_coming_soon: 'Module à venir bientôt ! 🔥',
      past_period: 'Période passée',
      upcoming: 'À venir',
      current_period: 'Période courante',
      notes: 'Notes',
      conflicting_shifts: 'Quarts en conflit',
      total_hours: 'Heures totales',
      salaries: 'Salaires',
      filter: 'Filtrer',
      break: 'Pause',
      worked_as: 'Travaillé comme',
      select_role: 'Sélectionner',
      no_roles_available: 'Aucun rôle disponible',
      add_break: 'Ajouter une pause',
      unpaid: 'Non payé',
      paid: 'Payé',
      total: 'Total',
      planned: 'Planifié',
      clocked: 'Poinçonné',
      rounded: 'Arrondi',
      week: 'Semaine',
      biweekly: 'Bi-hebdomadaire',
      select_new_period: 'Sélectionner une période',
      reset_to_current: 'Période courante',
      open_selected: 'Ouvrir la période',

      shift_not_planned_for_employee: 'Quart non planifié pour cet employé',
      employee_did_not_clock_out: 'Employé n’a pas poinçonné',
      shift_was_less_than_3_hours: 'Quart était moins de 3 heures',
      overlaps_with_shift: 'Chevauche avec Quart',

      activity_log: 'Journal des activités',
      categories: 'Catégories',
      initially: 'Initialement',
      updated: 'Mis à jour',
      modified: 'Modifié',
      added: 'Ajouté',
      deleted: 'Supprimé',
      start_end: 'Début - Fin',
      role: 'Rôle',
      start: 'Début',
      end: 'Fin',
      delete_shift: 'Supprimer quart',
      add_shift: 'Ajouter quart',
      unplanned: 'Non planifié',
      delete_break: 'Supprimer pause',
      reclaim: 'Récupérer',
      you_are_all_caught_up: 'Vous êtes tout à fait au point!',
      no_conflicts: 'Aucun conflit',

      clocked_in_early: 'Entrée plus tôt',
      the_employee_clocked_in_earlier_than_planned:
        'L’employé a pointé son entrée plus tôt que prévu.',
      missing_end: 'Fin ManquanteClocked out late',
      the_employee_did_not_clock_out_of_their_shift:
        "L'employé n'a pas pointé son heure de sortie.",
      clocked_out_late: 'Sortie après l’heure',
      the_employee_clocked_out_later_than_planned:
        'L’employé a pointé sa sortie plus tard que prévu',
      unplanned_shift: 'Quart non planifié',
      the_clocked_shift_was_not_planned_for_this_employee:
        'Le quart pointé n’était pas prévu pour cet employé',
      shift_too_short: 'Quart trop court',
      shift_was_shorter_than_expected:
        'L’employé a pointé un quart de très courte durée',
      shift_under_3_hours: 'Quart de moins de 3 heures',
      shift_was_under_3_hours: 'L’employé a travaillé moins de 3 heures',

      you_have_claimed_shift_early_shift: 'Vous avez réclamé le quart',
      you_have_deleted_shift: 'Vous avez supprimé le quart',
      you_have_approved_shift: 'Vous avez approuvé le quart',
      shift_update_has_been_saved: 'Mise à jour du quart enregistrée',
      you_save: 'Vous économisez',
      with_planned_start_time: 'avec le temps de début planifié',
      please_check_the_time_cards_below_to_ensure_proper_payroll_management:
        'Veuillez vérifier les cartes de temps ci-dessous pour assurer une gestion de paie appropriée',
      saved: 'Sauvé',
      you_saved: 'Vous économisez',
      no_activity_to_date: 'Aucune activité à cette date',
      activities: 'Activités',
      entire_period: 'Période entière',
      are_you_sure_you_want_to_delete_this_shift:
        'Êtes-vous sûr de vouloir supprimer ce quart?',
      are_you_sure_delete_break:
        'Êtes-vous sûr de vouloir supprimer cette pause?'
    },
    scheduleByHeader: {
      laborCost: 'Main d’oeuvre'
    },
    auth: {
      please_log_in_to_your_account_accept_invitation:
        'Veuillez vous connecter à votre compte pour accepter l’invitation',
      password_setup_successful: 'Configuration du mot de passe réussie',
      re_login:
        "Vous devez vous reconnecter pour configurer votre mot de passe. S'il vous plaît vérifier votre e-mail pour le lien.",
      login_successful: 'Connexion réussie',
      setup_password: 'Configurez un mot de passe pour votre compte',
      signout: 'Déconnexion',
      invalid_invitation_link: "Lien d'invitation invalide",
      wrong_account:
        "Vous essayez d'accepter une invitation sous un mauvais compte. Veuillez vous connecter au bon compte et réessayer.",
      invitation_accepted_successfully: 'Invitation acceptée avec succès'
    },
    export_attendance: {
      date: 'Date',
      to: 'à',
      name: 'Nom',
      first_name: 'Prénom',
      last_name: 'Nom',
      empl_number: 'Numéro d’employé',
      start: 'Début',
      end: 'Fin',
      position: 'Position',
      total: 'Total (h)',
      pay_period: 'Période de paie',
      hours: 'Heures',
      employees: 'Employés',
      rate: 'Taux',
      s1: 'S1',
      s2: 'S2',
      additional_salary: 'Salaire additionnel',
      amount: 'Montant',
      shift_worked: 'Nb. de quarts travaillés',
      acombaId: 'acomba ID',
      powerpayId: 'Powerpay ID',
      unpaid_break: 'Pause non payée',
      shift: 'Quart'
    }
  }
}

export default translationsObject
